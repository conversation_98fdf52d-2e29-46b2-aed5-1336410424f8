open Vitest
open TestingLibraryReact

let mockQueryProductProducers = data => {
  CatalogProducerSelect.Query.productProducers: data,
}

describe("component", () => {
  let userEvent = TestingLibraryEvent.setup()

  itPromise("should be disabled when the data is loading", async () => {
    let onChange = fn1(ignore)
    let useProductProducersQuery = _variables => AsyncData.Loading

    render(
      <CatalogProducerSelect
        shopId=Some("mock-shop-id") value=None useProductProducersQuery onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Producer: All")
    expect(triggerElement)->toHaveAttributeValue("aria-disabled", "true")
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should be disabled when there is no shopId", async () => {
    let onChange = fn1(ignore)
    let useProductProducersQuery = _variables => AsyncData.Done(Ok(mockQueryProductProducers([])))

    render(
      <CatalogProducerSelect
        shopId=None value=None useProductProducersQuery onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Producer: All")
    expect(triggerElement)->toHaveAttributeValue("aria-disabled", "true")
    expect(onChange)->toHaveBeenCalledTimes(0)
  })

  itPromise("should render with the value passed selected", async () => {
    let onChange = fn1(ignore)
    let useProductProducersQuery = _variables => AsyncData.Done(
      Ok(mockQueryProductProducers(["mock-product-producer-1", "mock-product-producer-0"])),
    )

    render(
      <CatalogProducerSelect
        shopId=Some("mock-shop-id")
        value=Some("mock-product-producer-1")
        useProductProducersQuery
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Producer: mock-product-producer-1")
    expect(triggerElement)->Vitest.not->toHaveAttribute("aria-disabled")
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let (option1, option2, option3) = within(listbox)->getAllByRoleExn3(#option)

    expect(option1)->toHaveTextContent("All")
    expect(option2)->toHaveTextContent("mock-product-producer-1")
    expect(option3)->toHaveTextContent("mock-product-producer-0")

    await userEvent->TestingLibraryEvent.click(option1)

    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenLastCalledWith1(None)
  })

  itPromise("should render with the placeholder when the passed value is not found", async () => {
    let onChange = fn1(ignore)
    let useProductProducersQuery = _variables => AsyncData.Done(Ok(mockQueryProductProducers([])))

    render(
      <CatalogProducerSelect
        shopId=Some("mock-shop-id")
        value=Some({"mock-product-producer"})
        useProductProducersQuery
        onChange={onChange->fn}
      />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("Producer: Select")
    expect(triggerElement)->Vitest.not->toHaveAttribute("aria-disabled")
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.click(triggerElement)

    let listbox = screen->getByRoleExn(#listbox)
    let option1 = within(listbox)->getByRoleExn(#option)

    expect(option1)->toHaveTextContent("All")
  })
})

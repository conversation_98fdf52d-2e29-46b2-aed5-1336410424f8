open Vitest

describe("BillingPlanKind", () => {
  test("toString", () => {
    open BillingAccount
    expect(BillingPlanKind.Standard->BillingPlanKind.toString)->toStrictEqual("Standard")
    expect(BillingPlanKind.Company->BillingPlanKind.toString)->toStrictEqual("Company")
    expect(BillingPlanKind.Boost->BillingPlanKind.toString)->toStrictEqual("Boost")
    expect(BillingPlanKind.Warehouse->BillingPlanKind.toString)->toStrictEqual("Warehouse")
  })
  test("fromString", () => {
    open BillingAccount
    expect("standard"->BillingPlanKind.fromString)->toStrictEqual(Ok(Standard))
    expect("company"->BillingPlanKind.fromString)->toStrictEqual(Ok(Company))
    expect("boost"->BillingPlanKind.fromString)->toStrictEqual(Ok(Boost))
    expect("warehouse"->BillingPlanKind.fromString)->toStrictEqual(Ok(Warehouse))
  })
})

describe("mostImportantShopIssue", () => {
  test("it shoud return missing payment as most important issue", () => {
    expect(
      BillingAccount.BillingIssue.mostImportantShopIssue([
        {shopId: "mock-shop-id-1", issue: MissingPaymentMethod},
        {shopId: "mock-shop-id-2", issue: InvalidBillingMandate},
      ]),
    )->toStrictEqual(Some({shopId: "mock-shop-id-1", issue: MissingPaymentMethod}))
    expect(
      BillingAccount.BillingIssue.mostImportantShopIssue([
        {shopId: "mock-shop-id-2", issue: InvalidBillingMandate},
        {shopId: "mock-shop-id-1", issue: MissingPaymentMethod},
      ]),
    )->toStrictEqual(Some({shopId: "mock-shop-id-1", issue: MissingPaymentMethod}))
    expect(
      BillingAccount.BillingIssue.mostImportantShopIssue([
        {shopId: "mock-shop-id-1", issue: MissingPaymentMethod},
      ]),
    )->toStrictEqual(Some({shopId: "mock-shop-id-1", issue: MissingPaymentMethod}))
  })
  test("it shoud return invalid mandate as most important issue", () => {
    expect(
      BillingAccount.BillingIssue.mostImportantShopIssue([
        {shopId: "mock-shop-id-2", issue: InvalidBillingMandate},
      ]),
    )->toStrictEqual(Some({shopId: "mock-shop-id-2", issue: InvalidBillingMandate}))
  })
})

describe("mostImportantIssue", () => {
  test("it shoud return missing payment as most important issue", () => {
    expect(
      BillingAccount.BillingIssue.mostImportantIssue([MissingPaymentMethod, InvalidBillingMandate]),
    )->toStrictEqual(Some(MissingPaymentMethod))
    expect(
      BillingAccount.BillingIssue.mostImportantIssue([InvalidBillingMandate, MissingPaymentMethod]),
    )->toStrictEqual(Some(MissingPaymentMethod))
    expect(BillingAccount.BillingIssue.mostImportantIssue([MissingPaymentMethod]))->toStrictEqual(
      Some(MissingPaymentMethod),
    )
  })
  test("it shoud return invalid mandate as most important issue", () => {
    expect(BillingAccount.BillingIssue.mostImportantIssue([InvalidBillingMandate]))->toStrictEqual(
      Some(InvalidBillingMandate),
    )
  })
})

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`should match snapshot with disable prop set to false 1`] = `
<body>
  <div>
    <div
      role="button"
      style="cursor: pointer; display: block; position: relative; user-select: text;"
      tabindex="0"
    >
      <div
        class="css-text-901oao"
        dir="auto"
        style="word-break: breakWord;"
      >
        my button
      </div>
    </div>
  </div>
</body>
`;

exports[`should match snapshot with disable prop set to true 1`] = `
<body>
  <div>
    <div
      aria-disabled="true"
      role="button"
      style="cursor: default; display: block; position: relative; user-select: text;"
    >
      <div
        class="css-text-901oao"
        dir="auto"
        style="word-break: breakWord;"
      >
        my button
      </div>
    </div>
  </div>
</body>
`;

exports[`should not change displayName 1`] = `"Touchable"`;

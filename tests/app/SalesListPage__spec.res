open Vitest
open TestingLibraryReact

describe("SalesExportRequest", () => {
  test("encodeRequestBodyJson", () => {
    let {encodeRequestBodyJson} = module(SalesListPage.SalesExportRequest)
    let startDate = Js.Date.now()
    let endDate = Js.Date.now()

    expect(
      encodeRequestBodyJson(
        ~shopIds=["mock-shop-id"],
        ~startDate,
        ~endDate,
        ~exportType=SalesListPage.ExportType.All,
      ),
    )->toUnsafeStrictEqual({
      "shopIds": ["mock-shop-id"],
      "startDate": startDate,
      "endDate": endDate,
      "timeZone": "UTC",
      "exportType": "ALL",
    })
    expect(
      encodeRequestBodyJson(
        ~shopIds=[],
        ~startDate,
        ~endDate,
        ~exportType=SalesListPage.ExportType.ReceiptsAndRefunds,
      ),
    )->toUnsafeStrictEqual({
      "shopIds": [],
      "startDate": startDate,
      "endDate": endDate,
      "timeZone": "UTC",
      "exportType": "RECEIPTS",
    })
  })
})

describe("SalesListPage", () => {
  module TestableSalesListPage = {
    @react.component
    let make = (~request, ~history=?) =>
      <Providers ?history>
        <SalesListPage request />
      </Providers>
  }

  itPromise("should display date range error message on SalesExportDateRangeFailure", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let requestResult = fn1(
      () => Error(
        Request.InvalidRequestFailures([
          {
            Request.kind: "SalesExportDateRangeFailure",
            message: "",
            data: None,
          },
        ]),
      ),
    )
    let request = (~shopIds as _, ~startDate as _, ~endDate as _, ~exportType as _) =>
      Future.makePure(resolve => resolve(requestResult->fn()))

    let _ = <TestableSalesListPage request />->render

    let button =
      screen->getAllByRoleWithOptionsExn(#button, {name: "Export the document"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    await waitFor(
      () => {
        expect(
          screen->getByTextExn("The sales cannot be exported for a period exceeding one year."),
        )->toBeVisible
      },
    )
  })

  itPromise("should display error on request failure", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let request = (~shopIds as _, ~startDate as _, ~endDate as _, ~exportType as _) =>
      Future.makePure(resolve => resolve(requestResult->fn()))

    let _ = <TestableSalesListPage request />->render

    let button =
      screen->getAllByRoleWithOptionsExn(#button, {name: "Export the document"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    await waitFor(
      () => {
        expect(
          screen->getByTextExn(
            "An issue occurred when attempting to download the sales file. Wino did not respond, please reiterate your request.",
          ),
        )->toBeVisible
      },
    )
  })

  itPromise("should display date tag for single day", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let request = (~shopIds as _, ~startDate as _, ~endDate as _, ~exportType as _) =>
      Future.makePure(resolve => resolve(requestResult->fn()))

    let _ = <TestableSalesListPage request />->render

    let button =
      screen->getAllByRoleWithOptionsExn(#button, {name: "Current month"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    let button = screen->getAllByRoleWithOptionsExn(#button, {name: "Today"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    let now = Js.Date.make()
    let date = now->Intl.dateTimeFormat(~dateStyle=#short)

    await waitFor(
      () => {
        expect(screen->getByTextExn("On " ++ date))->toBeVisible
      },
    )
  })

  itPromise("should display date tag for a range", async () => {
    let userEvent = TestingLibraryEvent.setup()

    let requestResult = fn1(() => Error(Request.UnexpectedServerError))
    let request = (~shopIds as _, ~startDate as _, ~endDate as _, ~exportType as _) =>
      Future.makePure(resolve => resolve(requestResult->fn()))

    let _ = <TestableSalesListPage request />->render

    let button =
      screen->getAllByRoleWithOptionsExn(#button, {name: "Current month"})->Array.getExn(0)
    expect(button)->toBeVisible

    await userEvent->TestingLibraryEvent.click(button)

    let now = Js.Date.make()

    let startDate =
      Js.Date.makeWithYMD(
        ~year=now->Js.Date.getFullYear,
        ~month=now->Js.Date.getMonth,
        ~date=1.,
        (),
      )->Intl.dateTimeFormat(~dateStyle=#short)

    let today = now->Intl.dateTimeFormat(~dateStyle=#short)

    await waitFor(
      () => {
        expect(screen->getByTextExn("From " ++ startDate ++ " to " ++ today))->toBeVisible
      },
    )
  })
})

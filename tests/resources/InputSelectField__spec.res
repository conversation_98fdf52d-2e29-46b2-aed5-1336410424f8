open Vitest
open TestingLibraryReact
open WebAPI

let expectListBoxIsOpened = () => {
  let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: true, hidden: true})
  let triggerElementControlsId = triggerElement->DomElement.getAttribute("aria-controls")

  expect(triggerElement)->toBeVisible

  let listboxElement = screen->getByRoleExn(#listbox)
  let listboxElementId = listboxElement->DomElement.getAttribute("id")

  expect(listboxElement)->toBeVisible
  expect(triggerElementControlsId)->toBe(listboxElementId)
}

let expectListBoxIsClosed = () => {
  let triggerElement = screen->getByRoleWithOptionsExn(#button, {expanded: false, hidden: false})

  expect(triggerElement)->toBeVisible
  expect(screen->queryByRole(#listbox))->toBeNone
}

let userEvent = TestingLibraryEvent.setup()

module TestableInputSelectField = {
  @react.component
  let make = (
    ~label="",
    ~errorMessage=?,
    ~placeholder=?,
    ~disabled=?,
    ~required=?,
    ~searchable=?,
    ~defaultValue,
    ~renderTriggerView=?,
    ~renderItemContent=?,
    ~sections,
    ~onChange=?,
  ) => {
    let (value, setValue) = React.useState(() => defaultValue)

    <InputSelectField
      label
      ?placeholder
      ?disabled
      ?errorMessage
      ?searchable
      ?required
      ?renderTriggerView
      ?renderItemContent
      sections
      value
      onChange={value => {
        setValue(_ => value)
        onChange->Option.forEach(onChange => onChange(value))
      }}
    />
  }
}

itPromise("should render with default value and be possible to interact with", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.items: [
        {
          key: "default",
          label: "All",
          value: None,
        },
        {
          key: "first",
          label: "First",
          value: Some("first"),
        },
      ],
    },
  ]

  let {baseElement} = render(
    <TestableInputSelectField defaultValue=None sections onChange={onChange->fn} />,
  )

  let triggerElement = screen->getByRoleExn(#button)

  expect(triggerElement)->toBeVisible
  expect(triggerElement)->toHaveTextContent("All")
  expect(triggerElement)->Vitest.not->toHaveAttribute("aria-disabled")
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  await userEvent->TestingLibraryEvent.click(baseElement)
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsClosed()
  expect(onChange)->toHaveBeenCalledTimes(0)
})

itPromise("should render with a label & value and not be possible to interact with", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.items: [
        {
          key: "default",
          label: "All",
          value: None,
        },
        {
          key: "first",
          label: "First",
          value: Some("first"),
        },
      ],
    },
  ]

  render(
    <TestableInputSelectField
      label="My label" disabled=true defaultValue=Some("first") sections onChange={onChange->fn}
    />,
  )->ignore

  let triggerElement = screen->getByRoleExn(#button)

  expect(triggerElement)->toBeVisible
  expect(triggerElement)->toHaveTextContent("My labelFirst")
  expect(triggerElement)->toHaveAttributeValue("aria-disabled", "true")
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsClosed()
  expect(onChange)->toHaveBeenCalledTimes(0)
})

itPromise(
  "should render with different titlted sections with a disabled option, and change value on selection",
  async () => {
    let onChange = fn1(ignore)
    let sections = [
      {
        Select.items: [
          {
            key: "default",
            label: "All",
            value: None,
          },
        ],
      },
      {
        title: "Wines",
        items: [
          {
            key: "cheverny-red",
            label: "Cheverny Rouge",
            value: Some("cheverny-red"),
            disabled: true,
          },
          {
            key: "colombard-sauvignon-white",
            label: "Colombard Sauvignon Blanc",
            value: Some("colombard-sauvignon-white"),
          },
        ],
      },
      {
        title: "Beers",
        items: [
          {
            key: "cibulle",
            label: "La Cibulle",
            value: Some("cibulle"),
          },
        ],
      },
    ]

    render(
      <TestableInputSelectField label="Drink" defaultValue=None sections onChange={onChange->fn} />,
    )->ignore

    let triggerElement = screen->getByRoleExn(#button)

    expect(triggerElement)->toBeVisible
    expect(triggerElement)->toHaveTextContent("DrinkAll")
    expectListBoxIsClosed()

    await userEvent->TestingLibraryEvent.click(screen->getByLabelTextExn("Drink"))

    expectListBoxIsOpened()
    expect(onChange)->toHaveBeenCalledTimes(0)

    let listboxElement = screen->getByRoleExn(#listbox)
    let (_section1, section2, _section3) = within(listboxElement)->getAllByRoleExn3(#presentation)

    let sectionTitleElement = within(section2)->getByTextExn("Wines")
    let groupElement = within(section2)->getByRoleExn(#group)

    expect(sectionTitleElement)->toBeVisible
    expect(groupElement)->toBeVisible

    let sectionTitleElementId = sectionTitleElement->DomElement.getAttribute("id")->Option.getExn
    let groupElementLabelledById =
      groupElement->DomElement.getAttribute("aria-labelledby")->Option.getExn

    expect(sectionTitleElementId)->toBe(groupElementLabelledById)

    let (option1, option2) = within(groupElement)->getAllByRoleExn2(#option)

    expect(option1)->toHaveAttributeValue("aria-disabled", "true")
    expect(option1)->toHaveAttributeValue("aria-selected", "false")
    expect(option1)->toHaveTextContent("Cheverny Rouge")
    expect(option1)->Vitest.not->toHaveFocus

    await userEvent->TestingLibraryEvent.click(option1)
    expectListBoxIsOpened()

    expect(option2)->toHaveAttributeValue("aria-selected", "false")
    expect(option2)->toHaveTextContent("Colombard Sauvignon Blanc")
    expect(option2)->Vitest.not->toHaveFocus
    expect(onChange)->toHaveBeenCalledTimes(0)

    await userEvent->TestingLibraryEvent.hover(option2)
    expect(option2)->toHaveFocus

    await userEvent->TestingLibraryEvent.click(option2)
    expectListBoxIsClosed()

    expect(triggerElement)->toHaveTextContent("DrinkColombard Sauvignon Blanc")
    expect(onChange)->toHaveBeenCalledTimes(1)
    expect(onChange)->toHaveBeenCalledWith1(Some("colombard-sauvignon-white"))

    await userEvent->TestingLibraryEvent.click(triggerElement)
    expectListBoxIsOpened()

    let option2SelectedElement =
      screen->getByRoleWithOptionsExn(#option, {name: "Colombard Sauvignon Blanc"})

    expect(option2SelectedElement)->toHaveAttributeValue("aria-selected", "true")
    expect(option2SelectedElement)->toHaveTextContent("Colombard Sauvignon Blanc")
    expect(option2SelectedElement)->toHaveFocus
    expect(onChange)->toHaveBeenCalledTimes(1)
  },
)

itPromise("should render with a placeholder when there is no value selected", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.title: "Wines",
      items: [
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
  ]

  let {rerender} = render(
    <TestableInputSelectField defaultValue=None sections onChange={onChange->fn} />,
  )

  let triggerElement = screen->getByRoleExn(#button)

  expect(triggerElement)->toBeVisible
  expect(triggerElement)->toHaveTextContent("Select")

  rerender(
    <TestableInputSelectField
      label="Custom" placeholder="Your selection" defaultValue=None sections onChange={onChange->fn}
    />,
  )->ignore

  let triggerElement = screen->getByRoleExn(#button)

  expect(triggerElement)->toHaveTextContent("CustomYour selection")
  expect(onChange)->toHaveBeenCalledTimes(0)
  expectListBoxIsClosed()
})

itPromise("should be possible to interact and navigate with keyboard when focused", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.title: "Wines",
      items: [
        {
          key: "cheverny-red",
          label: "Cheverny Rouge",
          value: Some("cheverny-red"),
          disabled: true,
        },
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
  ]

  render(
    <TestableInputSelectField
      label="Drink" defaultValue=Some("cibulle") sections onChange={onChange->fn}
    />,
  )->ignore

  let triggerElement = screen->getByRoleExn(#button)

  expect(triggerElement)->toBeVisible
  expect(triggerElement)->toHaveTextContent("La Cibulle")
  expect(onChange)->toHaveBeenCalledTimes(0)
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.keyboard("{arrowright}")
  expect(triggerElement)->toHaveTextContent("La Cibulle")
  expect(triggerElement)->Vitest.not->toHaveFocus
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.tab
  expect(triggerElement)->toHaveFocus
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.keyboard("{arrowright}")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.keyboard("{arrowleft}")
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(triggerElement)->toHaveTextContent("Colombard Sauvignon Blanc")

  onChange->mockClear

  await userEvent->TestingLibraryEvent.keyboard("{enter}")
  await userEvent->TestingLibraryEvent.keyboard("{arrowup}")
  expect(onChange)->toHaveBeenCalledTimes(0)
  expectListBoxIsOpened()

  await userEvent->TestingLibraryEvent.keyboard("{enter}")
  expect(onChange)->toHaveBeenCalledTimes(2) // FIXME - should have been called only once
  expect(triggerElement)->toHaveTextContent("La Cibulle")
  // expect(triggerElement)->toHaveFocus // FIXME - should have focus (RTL issue)
  expectListBoxIsClosed()

  onChange->mockClear

  await userEvent->TestingLibraryEvent.click(triggerElement)
  // expect(triggerElement)->toHaveFocus // FIXME - should have focus (RTL issue)
  expect(onChange)->toHaveBeenCalledTimes(0)
  expectListBoxIsOpened()

  // await userEvent->TestingLibraryEvent.keyboard("{escape}")
  // // expect(triggerElement)->toHaveFocus // FIXME - should have focus (RTL issue)
  // expect(onChange)->toHaveBeenCalledTimes(0)
  // expectListBoxIsClosed()

  // screen->debug

  // await userEvent->TestingLibraryEvent.click(triggerElement)
  // // expect(triggerElement)->toHaveFocus // FIXME - should have focus (RTL issue)
  // expect(onChange)->toHaveBeenCalledTimes(0)
  // expectListBoxIsOpened()

  // await userEvent->TestingLibraryEvent.tab
  // expect(triggerElement)->Vitest.not->toHaveFocus
  // expect(onChange)->toHaveBeenCalledTimes(0)
  // expectListBoxIsClosed()
})

itPromise("should render with custom trigger view", async () => {
  let sections = [
    {
      Select.title: "Wines",
      items: [
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
  ]

  let renderTriggerView = (~children, ~item as _, ~hovered, ~active, ~focused) =>
    <OverlayTriggerView
      label="Custom trigger" preset=#inputField({required: false}) hovered active focused>
      children
    </OverlayTriggerView>

  render(
    <TestableInputSelectField renderTriggerView defaultValue=Some("cibulle") sections />,
  )->ignore

  let triggerElement = screen->getByRoleExn(#button)
  let customTriggerElement = screen->getByTextExn("Custom trigger")

  expect(triggerElement)->toBeVisible
  expect(triggerElement)->toHaveTextContent("La Cibulle")
  expect(customTriggerElement)->toBeVisible
})

itPromise("should render with custom items content in list", async () => {
  let sections = [
    {
      Select.items: [
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: "colombard-sauvignon-white",
        },
      ],
    },
  ]

  let renderItemContent = ({Select.label: label}) =>
    <TextStyle> {("Wine: " ++ label)->React.string} </TextStyle>

  render(
    <TestableInputSelectField
      renderItemContent defaultValue="colombard-sauvignon-white" sections
    />,
  )->ignore

  let triggerElement = screen->getByRoleExn(#button)
  let customTriggerElement = screen->getByTextExn("Colombard Sauvignon Blanc")

  expect(triggerElement)->toBeVisible
  expect(triggerElement)->toHaveTextContent("Colombard Sauvignon Blanc")
  expect(customTriggerElement)->toBeVisible

  await userEvent->TestingLibraryEvent.click(triggerElement)

  let listboxElement = screen->getByRoleExn(#listbox)
  let optionElement = within(listboxElement)->getAllByRoleExn(#option)->Array.getExn(0)

  expect(optionElement)->toBeVisible
  expect(optionElement)->toHaveTextContent("Wine: Colombard Sauvignon Blanc")
})

itPromise("should render with a search input over 5 items", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.title: "Wines",
      items: [
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
  ]

  let {unmount} = render(
    <TestableInputSelectField defaultValue=Some("cibulle") sections onChange={onChange->fn} />,
  )

  let triggerElement = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(triggerElement)

  let inputSearchElement = screen->queryByPlaceholderText("Search")
  expectListBoxIsOpened()

  expect(triggerElement)->toBeVisible
  expect(inputSearchElement)->toBeNone

  unmount()

  let {unmount} = render(
    <TestableInputSelectField
      searchable=true defaultValue=Some("cibulle") sections onChange={onChange->fn}
    />,
  )

  let triggerElement = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

  expect(triggerElement)->toBeVisible
  expect(inputSearchElement)->toBeVisible
  expect(inputSearchElement)->toHaveFocus

  unmount()

  let sections = [
    {
      Select.items: [
        {
          key: "default",
          label: "All",
          value: None,
        },
      ],
    },
    {
      title: "Wines",
      items: [
        {
          key: "cheverny-red",
          label: "Cheverny Rouge",
          value: Some("cheverny-red"),
          disabled: true,
        },
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
    {
      title: "Juices",
      items: [
        {
          key: "apple-juice",
          label: "Apple Juice",
          value: Some("apple-juice"),
        },
        {
          key: "carrot-juice",
          label: "Carrot jus",
          value: Some("carrot-juice"),
        },
      ],
    },
  ]

  let {unmount} = render(
    <TestableInputSelectField defaultValue=Some("cibulle") sections onChange={onChange->fn} />,
  )

  let triggerElement = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

  expect(triggerElement)->toBeVisible
  expect(inputSearchElement)->toBeVisible
  expect(inputSearchElement)->toHaveFocus

  unmount()

  render(
    <TestableInputSelectField
      searchable=false defaultValue=Some("cibulle") sections onChange={onChange->fn}
    />,
  )->ignore

  let triggerElement = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  let inputSearchElement = screen->queryByPlaceholderText("Search")

  expect(triggerElement)->toBeVisible
  expect(inputSearchElement)->toBeNone
})

itPromise("should filter out upon search items list except `sticky` items", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.items: [
        {
          key: "default",
          label: "All",
          value: None,
          sticky: true,
        },
      ],
    },
    {
      title: "Wines",
      items: [
        {
          key: "cheverny-red",
          label: "Cheverny Rouge",
          value: Some("cheverny-red"),
          disabled: true,
        },
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
    {
      title: "Juices",
      items: [
        {
          key: "apple-juice",
          label: "Apple juice",
          value: Some("apple-juice"),
        },
        {
          key: "carrot-juice",
          label: "Carrot juice",
          value: Some("carrot-juice"),
        },
      ],
    },
  ]

  render(<TestableInputSelectField defaultValue=None sections onChange={onChange->fn} />)->ignore

  let triggerElement = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

  expect(triggerElement)->toBeVisible
  expect(inputSearchElement)->toBeVisible
  expect(inputSearchElement)->toHaveFocus
  expect(inputSearchElement)->toHaveDisplayValue("")

  let listboxElement = screen->getByRoleExn(#listbox)
  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(optionElements)->toHaveLength(6)

  await userEvent->TestingLibraryEvent.type_(inputSearchElement, "test")

  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(optionElements)->toHaveLength(1)
  expect(optionElements->Array.getExn(0))->toHaveTextContent("All")

  await userEvent->TestingLibraryEvent.clear(inputSearchElement)

  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(optionElements)->toHaveLength(6)

  await userEvent->TestingLibraryEvent.type_(inputSearchElement, "jui")

  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(optionElements)->toHaveLength(3)
  expect(optionElements->Array.getExn(0))->toHaveTextContent("All")
  expect(optionElements->Array.getExn(1))->toHaveTextContent("Apple juice")
  expect(optionElements->Array.getExn(2))->toHaveTextContent("Carrot juice")

  expectListBoxIsOpened()
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.keyboard("{arrowdown}")
  await userEvent->TestingLibraryEvent.keyboard("{enter}")

  expectListBoxIsClosed()
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(triggerElement)->toHaveTextContent("Carrot juice")

  onChange->mockClear

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

  await userEvent->TestingLibraryEvent.type_(inputSearchElement, "CHEVERNY")

  let listboxElement = screen->getByRoleExn(#listbox)
  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(optionElements)->toHaveLength(2)
  expect(optionElements->Array.getExn(0))->toHaveTextContent("All")
  expect(optionElements->Array.getExn(1))->toHaveTextContent("Cheverny Rouge")

  let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn

  expect(inputSearchElement)->toHaveDisplayValue("CHEVERNY")

  let (_focusButtonElement, clearButtonElement) =
    within(inputSearchElement->DomElement.closest("div")->Option.getExn)->getAllByRoleExn2(#button)

  await userEvent->TestingLibraryEvent.click(clearButtonElement)
  expectListBoxIsOpened()

  let inputSearchElement = screen->queryByPlaceholderText("Search")->Option.getExn
  let listboxElement = screen->getByRoleExn(#listbox)
  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(inputSearchElement)->toHaveDisplayValue("")
  expect(optionElements)->toHaveLength(6)

  expect(onChange)->toHaveBeenCalledTimes(0)
})

// NOTE - ARIA `descrbibed-by` not supported yet in ListBox option
itPromise("should display item description in list if given", async () => {
  let onChange = fn1(ignore)
  let sections = [
    {
      Select.title: "Comparer à",
      items: [
        {
          key: "previous-period",
          label: "Previous period",
          value: Some("previous-period"),
          description: "June 1st 2022 - June 8th 2022",
        },
        {
          key: "previous-year",
          label: "Previous year",
          value: Some("previous-year"),
          description: "June 9th 2021 - June 16th 2021",
        },
      ],
    },
  ]

  render(<TestableInputSelectField defaultValue=None sections onChange={onChange->fn} />)->ignore

  let triggerElement = screen->getByRoleExn(#button)

  await userEvent->TestingLibraryEvent.click(triggerElement)
  expectListBoxIsOpened()

  let listboxElement = screen->getByRoleExn(#listbox)
  let optionElements = within(listboxElement)->getAllByRoleExn(#option)

  expect(optionElements)->toHaveLength(2)

  expect(optionElements->Array.getExn(0))->toHaveTextContent(
    "Previous period June 1st 2022 - June 8th 2022",
  )
  expect(optionElements->Array.getExn(1))->toHaveTextContent(
    "Previous year June 9th 2021 - June 16th 2021",
  )
})

itPromise("should display an error message in `field` preset", async () => {
  let defaultValue = Some("cibulle")
  let sections = [
    {
      Select.title: "Wines",
      items: [
        {
          key: "colombard-sauvignon-white",
          label: "Colombard Sauvignon Blanc",
          value: Some("colombard-sauvignon-white"),
        },
      ],
    },
    {
      title: "Beers",
      items: [
        {
          key: "cibulle",
          label: "La Cibulle",
          value: Some("cibulle"),
        },
      ],
    },
  ]

  render(
    <TestableInputSelectField
      required=false errorMessage="Custom error message" defaultValue sections
    />,
  )->ignore

  expect(screen->queryByText("Custom error message"))->toBeDefined
})

open Vitest
open TestingLibraryReact

let nowTimestamp = Js.Date.now()
let fakeTimestamp = Js.Date.makeWithYMD(~year=2022., ~month=4., ~date=0., ())->Js.Date.getTime

let mockDate: float => unit = %raw(`(timestamp) => global.Date.now = () => timestamp`)
let setDate: float => unit = %raw(`timestamp => global.Date.now = () => timestamp`)
let resetDate: unit => unit = %raw(`() => global.Date.now = () => nowTimestamp`)

// NOTE - https://github.com/react-dates/react-dates/issues/1426
let originalGetComputedStyle = %raw(`window.getComputedStyle`)
let fixGetComputedStyle = %raw(`(...args) => {
  const cssStyleDeclaration = originalGetComputedStyle(args[0], args[1])

  cssStyleDeclaration.setProperty('padding-left', '0')
  cssStyleDeclaration.setProperty('padding-right', '0')
  cssStyleDeclaration.setProperty('padding-top', '0')
  cssStyleDeclaration.setProperty('padding-bottom', '0')
  cssStyleDeclaration.setProperty('margin-left', '0')
  cssStyleDeclaration.setProperty('margin-right', '0')
  cssStyleDeclaration.setProperty('margin-top', '0')
  cssStyleDeclaration.setProperty('margin-bottom', '0')
  cssStyleDeclaration.setProperty('border-left-width', '0')
  cssStyleDeclaration.setProperty('border-right-width', '0')
  cssStyleDeclaration.setProperty('border-top-width', '0')
  cssStyleDeclaration.setProperty('border-bottom-width', '0')

  return cssStyleDeclaration
}`)
let windowSpy = ref(None)

beforeAll(() => {
  let spy =
    spyOn0(WebAPI.window->Obj.magic, "getComputedStyle")->mockImplementation1(fixGetComputedStyle)
  windowSpy := Some(spy)
})
afterAll(() => windowSpy.contents->mockRestore)

test("formatDateRange", () => {
  let firstDate = Js.Date.makeWithYMD(~year=2024., ~month=11., ~date=31., ())
  let secondDate = Js.Date.makeWithYMD(~year=2025., ~month=11., ~date=31., ())
  expect(SelectDateRangeFilter.formatDateRange((firstDate, secondDate)))->toStrictEqual(
    "Dec 31, 2024 → Dec 31, 2025",
  )
})

describe("StandardPreset", () => {
  module StandardPreset = SelectDateRangeFilter.StandardPreset

  let {toDateRange, toString, fromPartialDateRange, isEqual} = module(StandardPreset)

  test("isEqual", () => {
    expect(Today->isEqual(Today))->toStrictEqual(true)
    expect(Yesterday->isEqual(Yesterday))->toStrictEqual(true)
    expect(CurrentWeek->isEqual(CurrentWeek))->toStrictEqual(true)
    expect(LastWeek->isEqual(LastWeek))->toStrictEqual(true)
    expect(CurrentMonth->isEqual(CurrentMonth))->toStrictEqual(true)
    expect(LastMonth->isEqual(LastMonth))->toStrictEqual(true)
    expect(CurrentYear->isEqual(CurrentYear))->toStrictEqual(true)
    expect(LastYear->isEqual(LastYear))->toStrictEqual(true)
    expect(Custom((None, None))->isEqual(Custom((None, None))))->toStrictEqual(true)
    expect(NotDefined->isEqual(NotDefined))->toStrictEqual(true)
    expect(CurrentMonth->isEqual(Today))->toStrictEqual(false)
  })

  test("toDateRange", () => {
    let (todayStartDate, todayEndDate) = Today->toDateRange->Option.getExn
    expect(todayStartDate->Js.Date.getTime)->toBeLessThan(todayEndDate->Js.Date.getTime)
    expect(Js.Date.now())->toBeGreaterThan(todayStartDate->Js.Date.getTime)
    expect(Js.Date.now())->toBeLessThan(todayEndDate->Js.Date.getTime)
    expect(todayStartDate->Js.Date.getDay)->toBe(Js.Date.make()->Js.Date.getDay)
    expect(todayEndDate->Js.Date.getDay)->toBe(Js.Date.make()->Js.Date.getDay)
    expect(todayStartDate->DateHelpers.startOfDay->Js.Date.getTime)->toBe(
      todayStartDate->Js.Date.getTime,
    )
    expect(todayEndDate->DateHelpers.endOfDay->Js.Date.getTime)->toBe(todayEndDate->Js.Date.getTime)

    let (yesterdayStartDate, yesterdayEndDate) = Yesterday->toDateRange->Option.getExn
    expect(yesterdayStartDate->Js.Date.getTime)->toBeLessThan(yesterdayEndDate->Js.Date.getTime)
    expect(yesterdayEndDate->Js.Date.getTime +. 1.)->toBe(todayStartDate->Js.Date.getTime)
    expect(yesterdayStartDate->DateHelpers.startOfDay->Js.Date.getTime)->toBe(
      yesterdayStartDate->Js.Date.getTime,
    )
    expect(yesterdayEndDate->DateHelpers.endOfDay->Js.Date.getTime)->toBe(
      yesterdayEndDate->Js.Date.getTime,
    )

    let (currentWeekStartDate, currentWeekEndDate) = CurrentWeek->toDateRange->Option.getExn
    expect(currentWeekStartDate->Js.Date.getTime)->toBeLessThan(currentWeekEndDate->Js.Date.getTime)
    expect(Js.Date.now())->toBeGreaterThan(currentWeekStartDate->Js.Date.getTime)
    expect(Js.Date.now())->toBeLessThan(currentWeekEndDate->Js.Date.getTime)
    expect(currentWeekStartDate->Js.Date.getDay)->toBe(1.) // Monday
    expect(currentWeekEndDate->Js.Date.getDay)->toBe(Js.Date.make()->Js.Date.getDay)
    expect(currentWeekEndDate->Js.Date.getDay)->toBe(
      Js.Math.min_float(6., Js.Date.make()->Js.Date.getDay),
    )
    expect(currentWeekStartDate->DateHelpers.startOfWeek->Js.Date.getTime)->toBe(
      currentWeekStartDate->Js.Date.getTime,
    )
    expect(currentWeekEndDate->DateHelpers.endOfDay->Js.Date.getTime)->toBe(
      currentWeekEndDate->Js.Date.getTime,
    )

    let (lastWeekStartDate, lastWeekEndDate) = LastWeek->toDateRange->Option.getExn
    expect(lastWeekStartDate->Js.Date.getTime)->toBeLessThan(lastWeekEndDate->Js.Date.getTime)
    expect(lastWeekEndDate->Js.Date.getTime +. 1.)->toBe(currentWeekStartDate->Js.Date.getTime)
    expect(lastWeekStartDate->Js.Date.getDay)->toBe(1.) // Monday
    expect(lastWeekEndDate->Js.Date.getDay)->toBe(0.) // Sunday
    expect(lastWeekStartDate->DateHelpers.startOfWeek->Js.Date.getTime)->toBe(
      lastWeekStartDate->Js.Date.getTime,
    )
    expect(lastWeekEndDate->DateHelpers.endOfWeek->Js.Date.getTime)->toBe(
      lastWeekEndDate->Js.Date.getTime,
    )

    let (currentMonthStartDate, currentMonthEndDate) = CurrentMonth->toDateRange->Option.getExn
    expect(currentMonthStartDate->Js.Date.getTime)->toBeLessThan(
      currentMonthEndDate->Js.Date.getTime,
    )
    expect(Js.Date.now())->toBeGreaterThan(currentMonthStartDate->Js.Date.getTime)
    expect(Js.Date.now())->toBeLessThan(currentMonthEndDate->Js.Date.getTime)
    expect(currentMonthStartDate->Js.Date.getUTCMonth)->toBe(
      currentMonthEndDate->Js.Date.getUTCMonth,
    )
    expect(currentMonthStartDate->Js.Date.getUTCFullYear)->toBe(
      currentMonthEndDate->Js.Date.getUTCFullYear,
    )
    expect(currentMonthStartDate->Js.Date.getUTCMonth)->toBe(Js.Date.make()->Js.Date.getUTCMonth)
    expect(currentMonthStartDate->DateHelpers.startOfMonth->Js.Date.getTime)->toBe(
      currentMonthStartDate->Js.Date.getTime,
    )
    expect(Js.Date.make()->DateHelpers.endOfDay->Js.Date.getTime)->toBe(
      currentMonthEndDate->Js.Date.getTime,
    )

    let (lastMonthStartDate, lastMonthEndDate) = LastMonth->toDateRange->Option.getExn
    expect(lastMonthStartDate->Js.Date.getTime)->toBeLessThan(lastMonthEndDate->Js.Date.getTime)
    expect(lastMonthEndDate->Js.Date.getTime +. 1.)->toBe(currentMonthStartDate->Js.Date.getTime)
    expect(lastMonthStartDate->Js.Date.getUTCMonth)->toBe(lastMonthEndDate->Js.Date.getUTCMonth)
    expect(lastMonthStartDate->Js.Date.getUTCFullYear)->toBe(
      lastMonthEndDate->Js.Date.getUTCFullYear,
    )
    expect(lastMonthStartDate->DateHelpers.startOfMonth->Js.Date.getTime)->toBe(
      lastMonthStartDate->Js.Date.getTime,
    )
    expect(lastMonthEndDate->DateHelpers.endOfMonth->Js.Date.getTime)->toBe(
      lastMonthEndDate->Js.Date.getTime,
    )

    let (currentYearStartDate, currentYearEndDate) = CurrentYear->toDateRange->Option.getExn
    expect(currentYearStartDate->Js.Date.getTime)->toBeLessThan(currentYearEndDate->Js.Date.getTime)
    expect(currentYearStartDate->Js.Date.getUTCFullYear)->toBe(
      currentYearEndDate->Js.Date.getUTCFullYear,
    )
    expect(Js.Date.now())->toBeGreaterThan(currentYearStartDate->Js.Date.getTime)
    expect(Js.Date.now())->toBeLessThan(currentYearEndDate->Js.Date.getTime)
    expect(currentYearStartDate->Js.Date.getUTCFullYear)->toBe(
      Js.Date.make()->Js.Date.getUTCFullYear,
    )
    expect(currentYearStartDate->Js.Date.toISOString)->toMatch("01-01T00:00:00.000Z")
    expect(Js.Date.make()->DateHelpers.endOfDay->Js.Date.getTime)->toBe(
      currentYearEndDate->Js.Date.getTime,
    )

    let (lastYearStartDate, lastYearEndDate) = LastYear->toDateRange->Option.getExn
    expect(lastYearStartDate->Js.Date.getTime)->toBeLessThan(lastYearEndDate->Js.Date.getTime)
    expect(lastYearStartDate->Js.Date.getUTCFullYear)->toBe(lastYearEndDate->Js.Date.getUTCFullYear)
    expect(lastYearEndDate->Js.Date.getTime +. 1.)->toBe(currentYearStartDate->Js.Date.getTime)
    expect(lastYearStartDate->Js.Date.getUTCFullYear)->toBe(
      Js.Date.make()->Js.Date.getUTCFullYear -. 1.,
    )
    expect(lastYearStartDate->Js.Date.toISOString)->toMatch("-01-01T00:00:00.000Z")
    expect(lastYearEndDate->Js.Date.toISOString)->toMatch("-12-31T23:59:59.999Z")

    expect(Custom((None, None))->toDateRange)->toBe(None)
    expect(Custom((Some(Js.Date.fromFloat(0.)), None))->toDateRange)->toBe(None)
    expect(
      Custom((Some(Js.Date.fromFloat(0.)), Some(Js.Date.fromFloat(1.))))->toDateRange,
    )->toStrictEqual(Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.))))
    expect(NotDefined->toDateRange)->toBe(None)
  })

  describe("fromPartialDateRange", () => {
    let setDate = () =>
      setDate(Js.Date.makeWithYMD(~year=2022., ~month=3., ~date=17., ())->Js.Date.getTime)

    beforeAll(setDate)
    afterAll(resetDate)

    let toDateRange = preset => {
      setDate()
      let dateRange = preset->toDateRange
      resetDate()
      dateRange
    }

    testEach2([
      (Today->toDateRange, StandardPreset.Today),
      (Yesterday->toDateRange, Yesterday),
      (CurrentWeek->toDateRange, CurrentWeek),
      (LastWeek->toDateRange, LastWeek),
      (CurrentMonth->toDateRange, CurrentMonth),
      (LastMonth->toDateRange, LastMonth),
      (CurrentYear->toDateRange, CurrentYear),
      (LastYear->toDateRange, LastYear),
      (
        Custom((Some(Js.Date.fromFloat(0.)), Some(Js.Date.fromFloat(1.))))->toDateRange,
        Custom(Some(Js.Date.fromFloat(0.)), Some(Js.Date.fromFloat(1.))),
      ),
      (Custom(Some(Js.Date.fromFloat(0.)), None)->toDateRange, NotDefined),
      (Custom((None, None))->toDateRange, NotDefined),
      (None, NotDefined),
    ])(.
      "should return preset",
      (dateRange, preset) =>
        expect(
          fromPartialDateRange(
            switch dateRange {
            | Some((startDate, endDate)) => (Some(startDate), Some(endDate))
            | None => (None, None)
            },
            [
              Today,
              Yesterday,
              CurrentWeek,
              LastWeek,
              CurrentMonth,
              LastMonth,
              CurrentYear,
              LastYear,
            ],
          ),
        )->toStrictEqual(preset),
    )
  })

  test("toString", () => {
    expect(Today->toString(~notDefinedPlaceholder=""))->toBe("Today")
    expect(Yesterday->toString(~notDefinedPlaceholder=""))->toBe("Yesterday")
    expect(CurrentWeek->toString(~notDefinedPlaceholder=""))->toBe("Current week")
    expect(LastWeek->toString(~notDefinedPlaceholder=""))->toBe("Last week")
    expect(CurrentMonth->toString(~notDefinedPlaceholder=""))->toBe("Current month")
    expect(LastMonth->toString(~notDefinedPlaceholder=""))->toBe("Last month")
    expect(CurrentYear->toString(~notDefinedPlaceholder=""))->toBe("Current year")
    expect(LastYear->toString(~notDefinedPlaceholder=""))->toBe("Last year")
    expect(Custom((None, None))->toString(~notDefinedPlaceholder=""))->toBe("Custom")
    expect(toString(~notDefinedPlaceholder="", Custom((None, None))))->toBe("Custom")
    expect(toString(Custom((None, None)), ~notDefinedPlaceholder="Pick a date range"))->toBe(
      "Custom",
    )
    expect(toString(NotDefined, ~notDefinedPlaceholder="Pick a date range"))->toBe(
      "Pick a date range",
    )
    expect(toString(NotDefined, ~notDefinedPlaceholder="Please, pick a date range"))->toBe(
      "Please, pick a date range",
    )
    expect(toString(Today, ~notDefinedPlaceholder="Please, pick a date range"))->toBe("Today")
    expect(
      toString(
        Custom(Some(Js.Date.fromFloat(0.)), None),
        ~notDefinedPlaceholder="Please, pick a date range",
      ),
    )->toBe(`Jan 1, 1970 →  …`)
    expect(
      toString(
        Custom(Some(Js.Date.fromFloat(0.)), Some(Js.Date.fromFloat(1024.))),
        ~notDefinedPlaceholder="Please, pick a date range",
      ),
    )->toBe(`Jan 1, 1970`)
    expect(
      toString(
        Custom(Some(Js.Date.fromFloat(0.)), Some(Js.Date.fromFloat(204800000.))),
        ~notDefinedPlaceholder="Please, pick a date range",
      ),
    )->toBe(`Jan 1, 1970 → Jan 3, 1970`)
  })
})

test("dateRangetoPartialDateRange", () => {
  let {dateRangetoPartialDateRange} = module(SelectDateRangeFilter)
  expect(
    dateRangetoPartialDateRange(Some((Js.Date.fromFloat(0.), Js.Date.fromFloat(1.)))),
  )->toStrictEqual((Some(Js.Date.fromFloat(0.)), Some(Js.Date.fromFloat(1.))))
  expect(dateRangetoPartialDateRange(None))->toStrictEqual((None, None))
})

test("defaultFocusedInput", () => {
  let {defaultFocusedInput} = module(SelectDateRangeFilter)
  expect(defaultFocusedInput)->toMatchSnapshot
})

let presets =
  SelectDateRangeFilter.standardPresetsArray->Array.map(preset => (
    preset,
    preset->SelectDateRangeFilter.StandardPreset.toString(~notDefinedPlaceholder=""),
  ))

module Canvas = {
  open Style

  @react.component
  let make = (~children) =>
    <View style={style(~width=500.->dp, ~height=500.->dp, ())}> {children} </View>
}

it("should render", () => {
  let onChange = fn1(ignore)

  render(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      value=?{None}
      triggerLabelDisplay=#showPreset
    />,
  )->ignore

  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
})

itPromise("should open and close the popover", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      value=?{None}
      triggerLabelDisplay=#showPreset
    />,
  )->ignore

  let trigger = screen->getByRoleWithOptionsExn(#button, {name: "Pick a date range"})

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
})

testEachPromise2(presets)(."should detect %s preset on mount", async (preset, text) => {
  mockDate(fakeTimestamp)

  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let value = SelectDateRangeFilter.StandardPreset.toDateRange(preset)

  render(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      ?value
      triggerLabelDisplay=#showPreset
    />,
  )->ignore

  let trigger = screen->getByRoleWithOptionsExn(#button, {name: text})

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn(text))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(getByTextWithContainerExn(trigger, text))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  // TODO - should check active radio checkbox preset
  // but the current component have not good accessibility.

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(getByTextWithContainerExn(trigger, text))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  resetDate()
})

testEachPromise2(presets)(."should detect %s preset on change", async (preset, text) => {
  mockDate(fakeTimestamp)

  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)
  let value = SelectDateRangeFilter.StandardPreset.toDateRange(preset)

  let {rerender} = render(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      value=?{None}
      triggerLabelDisplay=#showPreset
    />,
  )

  let trigger = screen->getByRoleWithOptionsExn(#button, {name: "Pick a date range"})

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(getByTextWithContainerExn(trigger, "Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(getByTextWithContainerExn(trigger, "Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  rerender(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      ?value
      triggerLabelDisplay=#showPreset
    />,
  )->ignore

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(getByTextWithContainerExn(trigger, text))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(getByTextWithContainerExn(trigger, text))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  // TODO - should check active radio checkbox preset

  resetDate()
})

testEachPromise2(presets)(."should pick %s preset", async (preset, text) => {
  mockDate(fakeTimestamp)

  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(
    <Canvas>
      <SelectDateRangeFilter
        preset=module(SelectDateRangeFilter.StandardPreset)
        presets=SelectDateRangeFilter.standardPresetsArray
        onChange={onChange->fn}
        value=?{None}
        triggerLabelDisplay=#showPreset
      />
    </Canvas>,
  )->ignore

  let trigger = screen->getByRoleWithOptionsExn(#button, {name: "Pick a date range"})

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
  expect(getByTextWithContainerExn(screen->getByRoleExn(#dialog), text))->toBeVisible

  let presetButton = getByTextWithContainerExn(screen->getByRoleExn(#dialog), text)

  await userEvent->TestingLibraryEvent.click(presetButton)

  expect(getByTextWithContainerExn(trigger, text))->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(SelectDateRangeFilter.StandardPreset.toDateRange(preset))

  mockClear(onChange)

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(getByTextWithContainerExn(trigger, text))->toBeVisible
  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  resetDate()
})

todo("should re render with new prop value")

itPromise("should pick a custom date range", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      value=?{None}
      triggerLabelDisplay=#showPreset
    />,
  )->ignore

  let trigger = screen->getByRoleWithOptionsExn(#button, {name: "Pick a date range"})

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  let dialog = screen->getByRoleExn(#dialog)

  expect(trigger)->toBeVisible
  expect(dialog)->toBeVisible
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  let previousMonthButton =
    screen->getByLabelTextExn("Move backward to switch to the previous month.")

  expect(previousMonthButton)->toBeVisible

  // NOTE - the following selector doesn't work
  // td[aria-label="Choose xx, xx 10, xx as your check-in date. It’s available"]
  let tenthOfPreviousMonth =
    screen->getAllByTextWithOptionsExn("10", {selector: "td"})->Array.getExn(0)

  // NOTE - the following selector doesn't work
  // td[aria-label="Choose xx, xx 10, xx as your check-in date. It’s available"]
  let eighteenthOfPreviousMonth =
    screen->getAllByTextWithOptionsExn("18", {selector: "td"})->Array.getExn(0)

  await userEvent->TestingLibraryEvent.click(tenthOfPreviousMonth)

  expect(screen->getByRoleExn(#dialog))->toBeVisible

  await userEvent->TestingLibraryEvent.click(eighteenthOfPreviousMonth)

  expect(screen->getByRoleExn(#dialog))->toBeVisible

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(screen->queryByRole(#dialog))->toBeNone

  let nowPreviousMonth = DateHelpers.subMonths(Js.Date.make(), 1.)
  let startDate = DateHelpers.startOfDay(Js.Date.fromFloat(nowPreviousMonth->Js.Date.setDate(10.)))
  let endDate = DateHelpers.endOfDay(Js.Date.fromFloat(nowPreviousMonth->Js.Date.setDate(18.)))

  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(Some(startDate, endDate))

  let dateRangeLabel = SelectDateRangeFilter.StandardPreset.toString(
    Custom((Some(startDate), Some(endDate))),
    ~notDefinedPlaceholder="",
  )

  expect(screen->queryByText("Pick a date range"))->toBeNone
  expect(
    screen->getByRoleWithOptionsExn(#button, {name: dateRangeLabel, expanded: false}),
  )->toBeVisible

  onChange->mockClear

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(
    screen->getByRoleWithOptionsExn(#button, {name: dateRangeLabel, expanded: true, hidden: true}),
  )->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
})

itPromise("should pick a custom date", async () => {
  let userEvent = TestingLibraryEvent.setup()
  let onChange = fn1(ignore)

  render(
    <SelectDateRangeFilter
      preset=module(SelectDateRangeFilter.StandardPreset)
      presets=SelectDateRangeFilter.standardPresetsArray
      onChange={onChange->fn}
      value=?{None}
      triggerLabelDisplay=#showPreset
    />,
  )->ignore

  let trigger = screen->getByRoleWithOptionsExn(#button, {name: "Pick a date range"})

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#dialog))->toBeNone
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(trigger)

  let dialog = screen->getByRoleExn(#dialog)

  expect(trigger)->toBeVisible
  expect(dialog)->toBeVisible
  expect(screen->getByTextExn("Pick a date range"))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  let previousMonthButton =
    screen->getByLabelTextExn("Move backward to switch to the previous month.")

  expect(previousMonthButton)->toBeVisible

  // NOTE - the following selector doesn't work
  // td[aria-label="Choose xx, xx 10, xx as your check-in date. It’s available"]
  let tenthOfPreviousMonth =
    screen->getAllByTextWithOptionsExn("10", {selector: "td"})->Array.getExn(0)

  await userEvent->TestingLibraryEvent.click(tenthOfPreviousMonth)

  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(tenthOfPreviousMonth)

  expect(screen->getByRoleExn(#dialog))->toBeVisible

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(screen->queryByRole(#dialog))->toBeNone

  let nowPreviousMonth = DateHelpers.subMonths(Js.Date.make(), 1.)
  let startDate = DateHelpers.startOfDay(Js.Date.fromFloat(nowPreviousMonth->Js.Date.setDate(10.)))
  let endDate = DateHelpers.endOfDay(Js.Date.fromFloat(nowPreviousMonth->Js.Date.setDate(10.)))

  expect(onChange)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledWith1(Some(startDate, endDate))

  onChange->mockClear

  let dateRangeLabel = SelectDateRangeFilter.StandardPreset.toString(
    Custom((Some(startDate), Some(endDate))),
    ~notDefinedPlaceholder="",
  )

  expect(screen->queryByText("Pick a date range"))->toBeNone
  expect(
    screen->getByRoleWithOptionsExn(#button, {name: dateRangeLabel, expanded: false}),
  )->toBeVisible

  await userEvent->TestingLibraryEvent.click(trigger)

  expect(screen->getByRoleExn(#dialog))->toBeVisible
  expect(
    screen->getByRoleWithOptionsExn(#button, {name: dateRangeLabel, expanded: true, hidden: true}),
  )->toBeVisible
  expect(onChange)->toHaveBeenCalledTimes(0)
})

todo("should reset (...)")

open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

testPromise("component", async () => {
  let onRequestClear = fn0()

  let {rerender} = render(
    <InputSearch
      loading=false onRequestClear={onRequestClear->fn} value="my value" onChange={_ => ()}
    />,
  )

  let inputElement = screen->getByLabelTextExn("Input")

  expect(inputElement)->toBeVisible
  expect(inputElement)->toHaveDisplayValue("my value")
  expect(onRequestClear)->toHaveBeenCalledTimes(0)

  let (_focusButtonElement, clearButtonElement) = screen->getAllByRoleExn2(#button)

  await userEvent->TestingLibraryEvent.click(clearButtonElement)

  expect(clearButtonElement)->toBeVisible
  expect(onRequestClear)->toHaveBeenCalledTimes(1)

  rerender(
    <InputSearch
      loading=false
      onRequestClear={onRequestClear->fn}
      disabled=true
      value="my value"
      onChange={_ => ()}
    />,
  )->ignore

  let (_focusButtonElement, clearButtonElement) = screen->getAllByRoleExn2(#button)

  expect(inputElement)->toHaveDisplayValue("my value")
  expect(clearButtonElement)->Vitest.not->toBeDefined

  rerender(
    <InputSearch value="" loading=false onRequestClear={onRequestClear->fn} onChange={_ => ()} />,
  )->ignore

  let (_focusButtonElement, clearButtonElement) = screen->getAllByRoleExn2(#button)

  expect(inputElement)->toHaveDisplayValue("")
  expect(clearButtonElement)->Vitest.not->toBeDefined

  rerender(
    <InputSearch loading=true onRequestClear={onRequestClear->fn} onChange={_ => ()} />,
  )->ignore

  let containerElement = inputElement->WebAPI.DomElement.closest("div")->Option.getExn
  let spinner = within(containerElement)->getByLabelTextExn("spinner")

  expect(inputElement)->toBeVisible
  expect(spinner)->toBeVisible
  expect(within(spinner)->getByRoleExn(#img))->toHaveAttributeValue("alt", "spinner")
})

open Vitest
open TestingLibraryReact

module TestableTooltip = {
  @react.component
  let make = (~text, ~disabled=?, ~delay=?, ~closeDelay=?) =>
    <Tooltip ?disabled ?delay ?closeDelay content={<Tooltip.Span text />}>
      <Text> {"trigger"->React.string} </Text>
    </Tooltip>
}

let userEvent = TestingLibraryEvent.setup()

itPromise("should display and hide trigger with delay", async () => {
  let text = "some tooltip"
  let {container} = render(<TestableTooltip text delay=500 />)
  let trigger = screen->getByTextExn("trigger")

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone

  await userEvent->TestingLibraryEvent.hover(container) // hacky user event
  await userEvent->TestingLibraryEvent.hover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone

  await waitFor(() => expect(screen->getByRoleExn(#tooltip))->toBeVisible)

  await userEvent->TestingLibraryEvent.unhover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#tooltip))->toBeVisible

  await waitFor(() => expect(screen->queryByRole(#tooltip))->toBeNone)
})

itPromise("should display and hide trigger without delay", async () => {
  let text = "some tooltip"
  let {container} = render(<TestableTooltip text delay=0 closeDelay=0 />)
  let trigger = screen->getByTextExn("trigger")

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone

  await userEvent->TestingLibraryEvent.hover(container) // hacky user event
  await userEvent->TestingLibraryEvent.hover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#tooltip))->toBeVisible

  await userEvent->TestingLibraryEvent.unhover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone
})

itPromise("should not display and hide trigger when it's disabled", async () => {
  let text = "some tooltip"
  let _ = render(<TestableTooltip text delay=0 disabled=true />)
  let trigger = screen->getByTextExn("trigger")

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone

  await userEvent->TestingLibraryEvent.hover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone

  await userEvent->TestingLibraryEvent.unhover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone
})

itPromise("should close with no delay", async () => {
  let text = "some tooltip"
  let _ = render(<TestableTooltip text delay=0 closeDelay=0 />)
  let trigger = screen->getByTextExn("trigger")

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone

  await userEvent->TestingLibraryEvent.hover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->getByRoleExn(#tooltip))->toBeVisible

  await userEvent->TestingLibraryEvent.unhover(trigger)

  expect(trigger)->toBeVisible
  expect(screen->queryByRole(#tooltip))->toBeNone
})

todo("with custom tooltip child component")

import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import { View, ViewProps } from '../../src/primitives/View'
import { style, hairlineWidth } from '../../src/primitives/Style'

const Template: StoryFn<ViewProps> = (props: ViewProps) => <View {...props} />

export default {
  title: 'Primitives/View',
  component: View,
} as Meta<typeof View>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  style: style({ width: 100, height: 125, backgroundColor: 'tomato' }),
}

export const CustomStyle = Template.bind({})
CustomStyle.storyName = 'With custom style'
CustomStyle.args = {
  children: (
    <View
      style={style({
        borderStyle: 'dashed',
        borderWidth: hairlineWidth,
        borderColor: 'blue',
        backgroundColor: 'white',
      })}
    >
      text
    </View>
  ),
  style: style({
    width: 150,
    height: 145,
    backgroundColor: 'purple',
    color: 'black',
    fontWeight: '_600',
    fontSize: 24,
  }),
}

import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  OpeningButton,
  OpeningButtonProps,
} from '../../../src/resources/actions/OpeningButton'
import { View } from '../../../src/primitives/View'
import { TextStyle } from '../../../src/resources/typography/TextStyle'

const Template: StoryFn<OpeningButtonProps> = (
  openingButtonprops: Partial<Pick<OpeningButtonProps, 'onPress'>> &
    Omit<OpeningButtonProps, 'onPress'>,
) => (
  <OpeningButton onPress={(_) => void 0} {...openingButtonprops}>
    <View>
      <TextStyle>My content</TextStyle>
    </View>
  </OpeningButton>
)

export default {
  title: 'Resources/Actions/OpeningButton',
  component: OpeningButton,
} as Meta<typeof OpeningButton>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  disabled: false,
  opened: false,
}

export const Opened = Template.bind({})
Opened.storyName = 'Opened'
Opened.args = {
  disabled: false,
  opened: true,
}

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = {
  disabled: true,
  opened: false,
}

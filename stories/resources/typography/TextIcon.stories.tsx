import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  TextIcon,
  TextIconProps,
} from '../../../src/resources/typography/TextIcon'
import { IconName } from '../../../src/resources/images-and-icons/Icon'

const Template: StoryFn<TextIconProps> = (props: TextIconProps) => (
  <TextIcon {...props} />
)

export default {
  title: 'Resources/Typography/TextIcon',
  component: TextIcon,
} as Meta<typeof TextIcon>

const icon: IconName = 'search'
const children = 'My button'
const defaultProps = { icon, children }

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { ...defaultProps }

export const NoMargin = Template.bind({})
NoMargin.storyName = 'With no margin'
NoMargin.args = { ...defaultProps, noMargin: true }

export const SmallFontSize = Template.bind({})
SmallFontSize.storyName = 'With small font size'
SmallFontSize.args = { ...defaultProps, size: 'xsmall' }

export const BigFontSize = Template.bind({})
BigFontSize.storyName = 'With big font size'
BigFontSize.args = { ...defaultProps, size: 'xlarge' }

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = { ...defaultProps, disabled: true }

import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  Tooltip,
  TooltipProps,
  TooltipSpan,
  TooltipBr,
} from '../../../src/resources/overlays/Tooltip'

import { Icon } from '../../../src/resources/images-and-icons/Icon'

const Template: StoryFn<Partial<TooltipProps>> = (
  props: Partial<TooltipProps>,
) => (
  <div style={{ display: 'flex', flexDirection: 'row' }}>
    <Tooltip
      content={<TooltipSpan text="this box is the\ntooltip message." />}
      {...props}
    >
      <Icon name="clipboard" />
    </Tooltip>
    <Tooltip content={<TooltipSpan text="and another one!" />} {...props}>
      <Icon name="back" />
    </Tooltip>
  </div>
)

export default {
  title: 'Resources/Overlays/Tooltip',
  component: Tooltip,
  argTypes: { onChange: { action: 'trigger hovered' } },
} as Meta<typeof Tooltip>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const WithoutArrow = Template.bind({})
WithoutArrow.storyName = 'Without an overlay arrow'
WithoutArrow.args = { arrowed: false }

export const CustomPlacement = Template.bind({})
CustomPlacement.storyName = 'Custom placement (end)'
CustomPlacement.args = { placement: 'end' }

export const NoDelay = Template.bind({})
NoDelay.storyName = 'Without delay'
NoDelay.args = { delay: 0 }

export const NoCloseDelay = Template.bind({})
NoCloseDelay.storyName = 'Without close delay'
NoCloseDelay.args = { closeDelay: 0 }

export const WithOffsets = Template.bind({})
WithOffsets.storyName = 'With offsets'
WithOffsets.args = { offset: -6, crossOffset: 10 }

export const Disabled = Template.bind({})
Disabled.storyName = 'Disabled'
Disabled.args = { disabled: true }

export const TooltipContent = Template.bind({})
TooltipContent.storyName = 'With Tooltip.Span and Tooltip.Br composition'
TooltipContent.args = {
  content: (
    <>
      <TooltipSpan text="Faded italic label:" tone="faded" italic />
      <TooltipBr />
      <TooltipSpan text="This is a short and" />
      <TooltipSpan text="bold green" tone="success" bold />
      <TooltipSpan text=" text content." />
      <TooltipBr />
      <TooltipBr space="large" />
      <TooltipSpan text="The end." tone="faded" bold />
    </>
  ),
}

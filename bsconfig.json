{"name": "pos", "reason": {"react-jsx": 3}, "package-specs": {"module": "es6", "in-source": true}, "bsc-flags": ["-bs-super-errors", "-open Belt"], "warnings": {"number": "-3-4-9-20-30-40-41-42-102-103+26+27+32+33+34+60", "error": "+A-3-23-26-27-32-33-34-60"}, "bs-dependencies": ["@reasonml-community/graphql-ppx", "@rescript/react", "@wino/accounting", "rescript-apollo-client", "rescript-future", "rescript-react-native"], "sources": [{"dir": "src", "subdirs": true}, {"dir": "tests", "type": "dev", "subdirs": true}], "suffix": ".bs.js", "ppx-flags": ["lenses-ppx/ppx", "@reasonml-community/graphql-ppx/ppx"], "graphql": {"schema": "graphql_schema_gateway.json", "apolloMode": true, "extendMutation": "ApolloClient.GraphQL_PPX.ExtendMutation", "extendQuery": "ApolloClient.GraphQL_PPX.ExtendQuery", "extendSubscription": "ApolloClient.GraphQL_PPX.ExtendSubscription", "templateTagReturnType": "ApolloClient.GraphQL_PPX.templateTagReturnType", "templateTagImport": "gql", "templateTagLocation": "@apollo/client", "templateTagIsFunction": true, "custom-fields": {"Datetime": "Scalar.Datetime", "Text": "Scalar.Text", "CKU": "Scalar.CKU"}}, "gentypeconfig": {"importPath": "relative", "language": "typescript", "shims": {"ReactNative": "ReactNative", "RescriptPervasives": "RescriptPervasives", "ReactDOM": "ReactDOM", "JsxDOM": "JsxDOM"}, "generatedFileExtension": ".gen", "module": "es6"}}
open Intl
open Style

let styles = StyleSheet.create({
  "root": style(~flex=1., ~justifyContent=#center, ~alignItems=#center, ~marginTop=-100.->dp, ()),
})

@react.component
let make = (~text=t("Loading...")) =>
  <View style={styles["root"]}>
    <Box spaceY=#small>
      <Spinner size=40. />
    </Box>
    <Stack>
      <Box spaceY=#xsmall>
        <Title level=#3 align=#center> {text->React.string} </Title>
      </Box>
      <Box spaceX=#xhuge>
        <TextStyle variation=#normal align=#center> {t("Please wait.")->React.string} </TextStyle>
      </Box>
    </Stack>
  </View>

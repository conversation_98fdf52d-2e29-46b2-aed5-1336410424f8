open Intl

let nonApplicableStringLiteral = "—"

let nonApplicableValueCellElement =
  <Tooltip
    content={<Tooltip.Span text={t("Please select a shop beforehand.")} />} delay=0 placement=#top>
    <TextStyle size=#xsmall variation=#normal>
      {nonApplicableStringLiteral->React.string}
    </TextStyle>
  </Tooltip>

module CatalogListMultiShopsQuery = %graphql(`
  query CatalogListMultiShopsQuery($search: String, $filterBy: InputVariantsDistinctOnCkuQueryFilter, $orderBy: [InputVariantsDistinctOnCkuOrderBy!], $before: String, $after: String, $first: Int, $last: Int) {
   variantsDistinctOnCku(search: $search, filterBy: $filterBy, orderBy: $orderBy, before: $before, after: $after, first: $first, last: $last) {
      pageInfo {
        startCursor
        endCursor
      }
      edges {
        node {
          cku
          id
          createdAt
          name
          product {
            id
            name
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            wineType @ppxOmitFutureValue
            whiteWineType @ppxOmitFutureValue
            beerType
            producer
            designation
            family
            region
            country
            tax { value }
          }
          stockKeepingUnit
          internalCode
          priceLookUpCode
          supplier { companyName }
          alcoholVolume
          purchasedPrice
          formattedCategory
          bulk
          capacityUnit
          maxStockThreshold
          minStockThreshold
          stockOrderTriggerThreshold
          stock {
            formattedShopsNames
            formattedQuantity
            state @ppxOmitFutureValue
          }
          formattedStatus @ppxOmitFutureValue
        }
      }
      totalCount
    }
  }
`)

module CatalogListSingleShopQuery = %graphql(`
  query CatalogListSingleShopQuery($search: String, $filterBy: InputVariantsQueryFilter, $orderBy: [InputVariantsOrderBy!], $before: String, $after: String, $first: Int, $last: Int) {
   variants(search: $search, filterBy: $filterBy, orderBy: $orderBy, before: $before, after: $after, first: $first, last: $last) {
      pageInfo {
        startCursor
        endCursor
      }
      totalCount
      edges {
        node {
          cku
          id
          createdAt
          name
          product {
            id
            name
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            wineType @ppxOmitFutureValue
            whiteWineType @ppxOmitFutureValue
            beerType
            producer
            designation
            family
            region
            country
            tax { value }
          }
          purchasedPrice
          variantPrices {
            edges {
              node {
                id
                valueIncludingTax
                valueExcludingTax
                price { id }
              }
            }
          }
          orderProducts(first: 1, filterBy: {
            receptionFinishedAt: { _after: "1970-01-01T00:00:00Z" }
          }) {
            edges {
              node {
                quantity
                totalLocalDiscounts
                fees
              }
            }
          }
          stockKeepingUnit
          internalCode
          priceLookUpCode
          supplier { companyName }
          alcoholVolume
          formattedCategory
          bulk
          capacityUnit
          maxStockThreshold
          minStockThreshold
          stockOrderTriggerThreshold
          stock {
            formattedQuantity
            state @ppxOmitFutureValue
            formattedShopsNames
          }
          formattedStatus @ppxOmitFutureValue
        }
      }
    }
  }
`)

module CatalogListPricelist = {
  module SingleShopPricesQuery = %graphql(`
    query PricesQuery($filterBy: InputPricesQueryFilter) {
      prices(first: 50, filterBy: $filterBy) {
        edges {
          node {
            id
            name
            enableByDefault
            taxIncluded
          }
        }
      }
    }
  `)

  type t = {
    id: string,
    name: string,
    enableByDefault: bool,
    taxIncluded: bool,
  }

  let singleShopQueryVariables = (~shopIds) =>
    SingleShopPricesQuery.makeVariables(
      ~filterBy=SingleShopPricesQuery.makeInputObjectInputPricesQueryFilter(
        ~shopIds=SingleShopPricesQuery.makeInputObjectInFilter(~_in=shopIds, ()),
        (),
      ),
      (),
    )

  let singleShopQueryResult = queryResult =>
    queryResult.SingleShopPricesQuery.prices.edges->Array.map(edge => {
      id: edge.node.id,
      name: edge.node.name,
      enableByDefault: edge.node.enableByDefault,
      taxIncluded: edge.node.taxIncluded,
    })

  let getCurrentPricelist = (pricelists, ~currentPriceId) =>
    pricelists
    ->Array.getBy(pricelist =>
      switch currentPriceId {
      | Some(currentPriceId) => pricelist.id === currentPriceId
      | _ => false
      }
    )
    ->Option.orElse(pricelists[0])
}

module CatalogListTableRow = {
  type feesList = {
    transportAmount: float,
    taxesAmount: float,
    otherAmount: float,
  }
  type retailPrice = {
    valueExcludingTax: float,
    valueIncludingTax: float,
  }
  type t = {
    cku: string,
    id: string,
    createdAt: Js.Date.t,
    productId: string,
    name: string,
    productName: string,
    productKind: CatalogProduct.Kind.t,
    productTaxRate: float,
    designation: option<string>,
    family: option<string>,
    wineType?: CatalogProduct.WineType.t,
    whiteWineType?: CatalogProduct.WhiteWineType.t,
    beerType?: string,
    color: option<CatalogProduct.Color.t>,
    producer: option<string>,
    categoryName: string,
    supplierName: option<string>,
    region: option<string>,
    country: option<string>,
    feesList: feesList,
    purchasePrice: float,
    purchasePriceDiscount: float,
    retailPrice: option<retailPrice>,
    stockKeepingUnit: option<string>,
    priceLookUpCode: option<int>,
    internalCode: option<string>,
    stockQuantity: option<string>,
    stockState: option<Product.Stock.t>,
    formattedShopsNames: option<string>,
    status: option<CatalogProduct.Status.t>,
    bulkUnit: option<string>,
    maxStockThreshold: float,
    minStockThreshold: float,
    stockOrderTriggerThreshold: float,
    bulk: bool,
    alcoholVolume: option<float>,
  }

  let keyExtractor = ({id}) => id

  let decodeFeesJsonToFeesList = feesJson => {
    let initialFeesList = {transportAmount: 0., taxesAmount: 0., otherAmount: 0.}
    switch Json.decodeArray(feesJson) {
    | Some(jsonFees) =>
      Array.reduce(jsonFees, initialFeesList, (acc, feeJson) => {
        let kind = feeJson->Json.decodeDict->Json.flatDecodeDictFieldString("kind")
        let amount = feeJson->Json.decodeDict->Json.flatDecodeDictFieldFloat("amount")
        switch (kind, amount) {
        | (Some("Transport"), Some(transportAmount)) => {
            ...acc,
            transportAmount: PriceCalculator.sumManyCharges([acc.transportAmount, transportAmount]),
          }
        | (Some("Taxes"), Some(taxesAmount)) => {
            ...acc,
            taxesAmount: PriceCalculator.sumManyCharges([acc.taxesAmount, taxesAmount]),
          }
        | (Some("Other"), Some(otherAmount)) => {
            ...acc,
            otherAmount: PriceCalculator.sumManyCharges([acc.otherAmount, otherAmount]),
          }
        | _ => acc
        }
      })
    | None => initialFeesList
    }
  }

  let computeNetPurchasePrice = (value, ~discount) =>
    PriceCalculator.Purchase.toNetValue(value, ~discount)

  let computeFullPurchasePrice = (~purchasePrice, ~taxesFeeAmount, ~otherFeeAmount) => {
    let charges = PriceCalculator.sumManyCharges([taxesFeeAmount, otherFeeAmount])
    PriceCalculator.Purchase.toValueWithCharges(purchasePrice, ~charges)
  }

  let computeAcquisitionCost = (
    ~purchasePrice,
    ~transportFeeAmount,
    ~taxesFeeAmount,
    ~otherFeeAmount,
  ) => {
    let charges = PriceCalculator.sumManyCharges([
      transportFeeAmount,
      taxesFeeAmount,
      otherFeeAmount,
    ])
    PriceCalculator.Purchase.toValueWithCharges(purchasePrice, ~charges)
  }
}

module CatalogListNotificationBanner = {
  @react.component
  let make = React.memo((~notification, ~onRequestClose) =>
    switch notification {
    | Some(result) =>
      <Box spaceTop=#medium>
        {switch result {
        | Ok(message) => <Banner textStatus=Success(message) onRequestClose />
        | Error(message) => <Banner textStatus=Danger(message) onRequestClose />
        }}
      </Box>
    | None => React.null
    }
  )
}

module CatalogListPricingSettings = {
  module PurchasePriceType = {
    type t = Gross | Net

    let toString = value =>
      switch value {
      | Gross => "gross"
      | Net => "net"
      }
    let fromString = value =>
      switch value {
      | "gross" => Ok(Gross)
      | "net" => Ok(Net)
      | _ => Error()
      }
  }

  module PriceDifferentialIndicator = {
    open PriceCalculator.Retail.Rate
    type t = PriceCalculator.Retail.Rate.t

    let toString = value =>
      switch value {
      | Coefficient => "coefficient"
      | MarkupRate => "markup-rate"
      | MarginRate => "margin-rate"
      }
    let fromString = value =>
      switch value {
      | "coefficient" => Ok(Coefficient)
      | "markup-rate" => Ok(MarkupRate)
      | "margin-rate" => Ok(MarginRate)
      | _ => Error()
      }
    let toLabel = value =>
      switch value {
      | Coefficient => t("Coefficient")
      | MarkupRate => t("Markup rate")
      | MarginRate => t("Margin rate")
      }
    let toShortLabel = value =>
      switch value {
      | Coefficient => t("Coefficient")
      | MarkupRate => t("Markup rt")
      | MarginRate => t("Margin rt")
      }
  }

  module RetailPriceCalculatingMethod = {
    type t = PurchasePrice | FullPurchasePrice | AcquisitionCost

    let toLabel = value =>
      switch value {
      | PurchasePrice => t("Purchase price")
      | FullPurchasePrice => t("Full purchase price")
      | AcquisitionCost => t("Acquisition cost")
      }
    let toString = value =>
      switch value {
      | PurchasePrice => "purchase-price"
      | FullPurchasePrice => "full-purchase-price"
      | AcquisitionCost => "acquisition-cost"
      }
    let fromString = value =>
      switch value {
      | "purchase-price" => Ok(PurchasePrice)
      | "full-purchase-price" => Ok(FullPurchasePrice)
      | "acquisition-cost" => Ok(AcquisitionCost)
      | _ => Error()
      }
  }

  type t = {
    purchasePriceType: PurchasePriceType.t,
    priceDifferentialIndicator: PriceDifferentialIndicator.t,
    retailPriceCalculatingMethod: RetailPriceCalculatingMethod.t,
  }

  let initialValue = {
    purchasePriceType: Gross,
    priceDifferentialIndicator: Coefficient,
    retailPriceCalculatingMethod: PurchasePrice,
  }

  module UserPreferences = {
    let jsonCodec = JsonCodec.object3(
      ({purchasePriceType, priceDifferentialIndicator, retailPriceCalculatingMethod}) => (
        purchasePriceType->PurchasePriceType.toString,
        priceDifferentialIndicator->PriceDifferentialIndicator.toString,
        retailPriceCalculatingMethod->RetailPriceCalculatingMethod.toString,
      ),
      ((purchasePriceType, priceDifferentialIndicator, retailPriceCalculatingMethod)) =>
        switch (
          purchasePriceType->PurchasePriceType.fromString,
          priceDifferentialIndicator->PriceDifferentialIndicator.fromString,
          retailPriceCalculatingMethod->RetailPriceCalculatingMethod.fromString,
        ) {
        | (
            Ok(purchasePriceType),
            Ok(priceDifferentialIndicator),
            Ok(retailPriceCalculatingMethod),
          ) =>
          Ok({
            purchasePriceType,
            priceDifferentialIndicator,
            retailPriceCalculatingMethod,
          })
        | _ => Error(#SyntaxError("Could not decode values"))
        },
      JsonCodec.field("purchasePriceType", JsonCodec.string),
      JsonCodec.field("priceDifferentialIndicator", JsonCodec.string),
      JsonCodec.field("retailPriceCalculatingMethod", JsonCodec.string),
    )

    let encoder = state => state->JsonCodec.encodeWith(jsonCodec)
    let decoder = json => json->JsonCodec.decodeWith(jsonCodec)

    let read = () => UserPreferences.read(~key=#"catalog-page-settings", ~decoder)
    let store = state => UserPreferences.store(state, ~key=#"catalog-page-settings", ~encoder)
  }
}

module CatalogListSettingsPopoverButton = {
  let priceDifferentialIndicatorItems = [
    {
      RadioCardGroup.value: Coefficient->CatalogListPricingSettings.PriceDifferentialIndicator.toString,
      title: t("catalog_list.pricing_settings.section_1.radio_1.title"),
      description: t("catalog_list.pricing_settings.section_1.radio_1.description"),
    },
    {
      value: MarginRate->CatalogListPricingSettings.PriceDifferentialIndicator.toString,
      title: t("catalog_list.pricing_settings.section_1.radio_2.title"),
      description: t("catalog_list.pricing_settings.section_1.radio_2.description"),
    },
    {
      value: MarkupRate->CatalogListPricingSettings.PriceDifferentialIndicator.toString,
      title: t("catalog_list.pricing_settings.section_1.radio_3.title"),
      description: t("catalog_list.pricing_settings.section_1.radio_3.description"),
    },
  ]
  let purchasePriceTypeItems = [
    {
      RadioCardGroup.value: Gross->CatalogListPricingSettings.PurchasePriceType.toString,
      title: t("Gross"),
      description: t("catalog_list.pricing_settings.section_2.radio_1.description"),
    },
    {
      value: Net->CatalogListPricingSettings.PurchasePriceType.toString,
      title: t("Net"),
      description: t("catalog_list.pricing_settings.section_2.radio_2.description"),
    },
  ]
  let retailPriceCalculatingMethodItems = [
    {
      RadioCardGroup.value: PurchasePrice->CatalogListPricingSettings.RetailPriceCalculatingMethod.toString,
      title: PurchasePrice->CatalogListPricingSettings.RetailPriceCalculatingMethod.toLabel,
      tooltip: t("catalog_list.pricing_settings.section_3.radio_1.tooltip"),
      description: t("catalog_list.pricing_settings.section_3.radio_1.description"),
    },
    {
      value: FullPurchasePrice->CatalogListPricingSettings.RetailPriceCalculatingMethod.toString,
      title: FullPurchasePrice->CatalogListPricingSettings.RetailPriceCalculatingMethod.toLabel,
      tooltip: t("catalog_list.pricing_settings.section_3.radio_2.tooltip"),
      description: t("catalog_list.pricing_settings.section_3.radio_2.description"),
    },
    {
      value: AcquisitionCost->CatalogListPricingSettings.RetailPriceCalculatingMethod.toString,
      title: AcquisitionCost->CatalogListPricingSettings.RetailPriceCalculatingMethod.toLabel,
      tooltip: t("catalog_list.pricing_settings.section_3.radio_3.tooltip"),
      description: t("catalog_list.pricing_settings.section_3.radio_3.description"),
    },
  ]

  @react.component
  let make = React.memo((
    ~onChange,
    ~settingsUserPreferencesRead=CatalogListPricingSettings.UserPreferences.read,
    ~settingsUserPreferencesStore=CatalogListPricingSettings.UserPreferences.store,
  ) => {
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
    let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())
    let (initialSettings, setInitialSettings) = React.useState(() =>
      settingsUserPreferencesRead()->Option.getWithDefault(CatalogListPricingSettings.initialValue)
    )
    let (settings, setSettings) = React.useState(() => initialSettings)
    let captureEvent = SessionTracker.useCaptureEvent()

    React.useEffect1(() => {
      onChange(initialSettings)
      None
    }, [initialSettings])

    let resetSettings = () => setSettings(_ => initialSettings)

    // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
    let onCommitRequestClose = popover.onRequestClose
    let popover = {
      let onRequestClose = popover.onRequestClose
      let onRequestToggle = popover.onRequestToggle
      {
        ...popover,
        onRequestToggle: () => {
          if popover.opened {
            resetSettings()
            onRequestToggle()
          }
        },
        onRequestClose: () => {
          if !triggerHovered {
            resetSettings()
            onRequestClose()
          }
        },
      }
    }

    let onCommitSettings = React.useCallback1(() => {
      setInitialSettings(_ => settings)
      settingsUserPreferencesStore(settings)
      onCommitRequestClose()
      captureEvent(#catalog_settings_click_save)
    }, [settings])

    let priceDifferentialIndicatorValue =
      settings.priceDifferentialIndicator->CatalogListPricingSettings.PriceDifferentialIndicator.toString

    let onChangePriceDifferentialIndicatorValue = value =>
      setSettings(prev => {
        ...prev,
        priceDifferentialIndicator: value
        ->CatalogListPricingSettings.PriceDifferentialIndicator.fromString
        ->Result.getExn,
      })

    let purchasePriceTypeValue =
      settings.purchasePriceType->CatalogListPricingSettings.PurchasePriceType.toString

    let onChangePurchasePriceTypeValue = value =>
      setSettings(prev => {
        ...prev,
        purchasePriceType: value
        ->CatalogListPricingSettings.PurchasePriceType.fromString
        ->Result.getExn,
      })

    let retailPriceCalculatingMethodValue =
      settings.retailPriceCalculatingMethod->CatalogListPricingSettings.RetailPriceCalculatingMethod.toString

    let onChangeRetailPriceCalculatingMethodValue = value =>
      setSettings(prev => {
        ...prev,
        retailPriceCalculatingMethod: value
        ->CatalogListPricingSettings.RetailPriceCalculatingMethod.fromString
        ->Result.getExn,
      })

    <>
      <ShortIconButton
        name=#settings
        bordered=true
        focused=popover.opened
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        action={Callback(popover.onRequestToggle)}
      />
      {if popover.opened {
        <Popover
          triggerRef=popoverTriggerRef
          state=popover
          modal=false
          placement=#"bottom start"
          animation=#fadeTranslation
          offset=4.>
          <Dialog
            ariaProps=popoverAriaProps.overlayProps
            title={t("Settings")}
            commitButtonText={t("Save settings")}
            commitDisabled={settings == initialSettings}
            onCommit=onCommitSettings
            onRequestClose=popover.onRequestClose>
            <Dialog.Section
              title={t("catalog_list.pricing_settings.section_1.title")}
              tooltip={<>
                <Tooltip.Span text={t("catalog_list.pricing_settings.section_1.tooltip")} />
              </>}>
              <RadioCardGroup
                value=priceDifferentialIndicatorValue
                items=priceDifferentialIndicatorItems
                onChange=onChangePriceDifferentialIndicatorValue
              />
              <DraftBanner
                text={t("catalog_list.pricing_settings.section_1.banner.text")} variation={#info}
              />
            </Dialog.Section>
            <Dialog.Section
              title={t("catalog_list.pricing_settings.section_2.title")}
              tooltip={<>
                <Tooltip.Span text={t("catalog_list.pricing_settings.section_2.tooltip")} />
              </>}>
              <RadioCardGroup
                value=purchasePriceTypeValue
                items=purchasePriceTypeItems
                onChange=onChangePurchasePriceTypeValue
              />
            </Dialog.Section>
            <Dialog.Section
              title={t("catalog_list.pricing_settings.section_3.title")}
              tooltip={<>
                <Tooltip.Span text={t("catalog_list.pricing_settings.section_3.tooltip")} />
              </>}>
              <RadioCardGroup
                orientation=#vertical
                value=retailPriceCalculatingMethodValue
                items=retailPriceCalculatingMethodItems
                onChange=onChangeRetailPriceCalculatingMethodValue
              />
            </Dialog.Section>
          </Dialog>
        </Popover>
      } else {
        React.null
      }}
    </>
  })
}

module CatalogListInventoryExportShortIconButton = {
  let endpoint = Env.sheetUrl() ++ "/inventory-export"

  let encodeRequestBodyJson = (
    ~shopIds,
    ~variantsActive=?,
    ~variantsArchived=?,
    ~categoryId=?,
    ~supplierId=?,
    ~producer=?,
    ~stock=?,
    (),
  ) => {
    let filterBy = Js.Dict.empty()

    filterBy->Js.Dict.set(
      "shopIds",
      Js.Dict.fromArray([
        ("_in", shopIds->Array.map(shopId => shopId->Json.encodeString)->Json.encodeArray),
      ])->Json.encodeDict,
    )
    switch variantsActive {
    | Some(active) =>
      filterBy->Js.Dict.set(
        "active",
        Js.Dict.fromArray([("_equals", active->Json.encodeBoolean)])->Json.encodeDict,
      )
    | None => ()
    }
    switch variantsArchived {
    | Some(archived) =>
      filterBy->Js.Dict.set("archived", (archived ? "ONLY" : "EXCLUDED")->Json.encodeString)
    | None => filterBy->Js.Dict.set("archived", "INCLUDED"->Json.encodeString)
    }
    switch categoryId {
    | Some(categoryId) =>
      filterBy->Js.Dict.set(
        "categoryId",
        Js.Dict.fromArray([
          (
            "_equals",
            switch categoryId->Js.Nullable.toOption {
            | Some(categoryId) => categoryId->Json.encodeString
            | None => Json.encodedNull
            },
          ),
        ])->Json.encodeDict,
      )
    | None => ()
    }
    switch supplierId {
    | Some(supplierId) =>
      filterBy->Js.Dict.set(
        "supplierId",
        Js.Dict.fromArray([
          (
            "_equals",
            switch supplierId->Js.Nullable.toOption {
            | Some(supplierId) => supplierId->Json.encodeString
            | None => Json.encodedNull
            },
          ),
        ])->Json.encodeDict,
      )
    | None => ()
    }
    switch producer {
    | Some(producer) =>
      filterBy->Js.Dict.set(
        "producer",
        Js.Dict.fromArray([("_equals", producer->Json.encodeString)])->Json.encodeDict,
      )
    | None => ()
    }
    switch stock {
    | Some({CatalogStockRangeSelect.min: ?min, ?max}) =>
      filterBy->Js.Dict.set(
        "stock",
        Js.Dict.fromArray([
          switch min {
          | Some(min) => ("_min", min->Json.encodeNumber)
          | None => ("_min", Json.encodedNull)
          },
          switch max {
          | Some(max) => ("_max", max->Json.encodeNumber)
          | None => ("_max", Json.encodedNull)
          },
        ])->Json.encodeDict,
      )
    | None => ()
    }

    Js.Dict.fromArray([
      ("filterBy", filterBy->Json.encodeDict),
      ("timeZone", Intl.timeZone->Json.encodeString),
    ])->Json.encodeDict
  }

  @react.component
  let make = (
    ~tooltip,
    ~shopIds,
    ~activeFilters,
    ~variantsActive=?,
    ~variantsArchived=?,
    ~categoryId=?,
    ~supplierId=?,
    ~producer=?,
    ~stock=?,
    ~onRequestErrorNotification,
  ) => {
    let request = () =>
      Request.make(
        endpoint,
        ~method=#POST,
        ~bodyJson=encodeRequestBodyJson(
          ~shopIds,
          ~variantsActive?,
          ~variantsArchived?,
          ~categoryId?,
          ~supplierId?,
          ~producer?,
          ~stock?,
          (),
        ),
      )

    let onFailure = error => {
      let errorMessage =
        error->RequestOpenStorageUrlButton.failureErrorToString(~exportName="inventory")
      onRequestErrorNotification(errorMessage)
    }

    let operableRequest = Ok(request)

    <RequestOpenStorageUrlButton
      variation=#compact statusLight=activeFilters text=tooltip operableRequest onFailure
    />
  }
}

module CatalogListPriceLookUpExportMenuItem = {
  let endpoint = Env.sheetUrl() ++ "/variants-price-look-up-codes"

  let encodeRequestBodyJson = (~shopId, ~variantsActive=?, ~variantsArchived=?, ()) => {
    let body = Js.Dict.empty()

    body->Js.Dict.set("shopId", shopId->Json.encodeString)
    switch variantsActive {
    | Some(variantsActive) =>
      body->Js.Dict.set("variantsActive", variantsActive->Json.encodeBoolean)
    | None => ()
    }
    switch variantsArchived {
    | Some(variantsArchived) =>
      body->Js.Dict.set("variantsArchived", variantsArchived->Json.encodeBoolean)
    | None => ()
    }

    body->Json.encodeDict
  }

  @react.component
  let make = (
    ~text,
    ~shopId,
    ~variantsActive=?,
    ~variantsArchived=?,
    ~onRequestErrorNotification,
  ) => {
    let {onRequestClose} = Popover.useState()

    let request = () =>
      Request.make(
        endpoint,
        ~method=#POST,
        ~bodyJson=encodeRequestBodyJson(
          ~shopId=shopId->Option.getWithDefault(""),
          ~variantsActive?,
          ~variantsArchived?,
          (),
        ),
      )

    let onSuccess = _ => onRequestClose()
    let onFailure = error => {
      let errorMessage =
        error->RequestOpenStorageUrlMenuItem.failureErrorToString(~exportName="PLU codes")
      onRequestErrorNotification(errorMessage)
      onRequestClose()
    }

    let operableRequest = switch shopId {
    | Some(_) => Ok(request)
    | None => Error(t("Please select a shop beforehand with the filter."))
    }

    <RequestOpenStorageUrlMenuItem text operableRequest onSuccess onFailure />
  }
}

module CatalogListCentralizeRequestMenuItem = {
  let endpoint = Env.gatewayUrl() ++ "/variants-centralization"

  type queryResult = {
    issuesCount: int,
    patchesCount: int,
    fileUrl: option<Url.t>,
  }

  let encodeRequestBodyJson = () => Js.Dict.empty()->Json.encodeDict

  let decodeRequestResponseJson = json => {
    let decodedIssuesCount =
      json->Json.decodeDict->Json.flatDecodeDictField("issuesCount", Json.decodeNumber)
    let decodedPatchesCount =
      json->Json.decodeDict->Json.flatDecodeDictField("patchesCount", Json.decodeNumber)
    let decodedFileUrl =
      json
      ->Json.decodeDict
      ->Json.flatDecodeDictField("file", Json.decodeDict)
      ->Json.flatDecodeDictFieldString("url")

    switch (decodedIssuesCount, decodedPatchesCount) {
    | (Some(issues), Some(patches)) =>
      Ok({
        issuesCount: issues->Float.toInt,
        patchesCount: patches->Float.toInt,
        fileUrl: decodedFileUrl->Option.map(Url.make),
      })
    | _ => Error(Request.MalformedResponse)
    }
  }

  module ReportModal = {
    let renderPatchesText = (~patchesCount) =>
      <TextStyle variation=#success weight=#medium>
        {template(
          "▪ " ++
          t(
            isPlural(patchesCount)
              ? "{{productCount}} new products have been successfully centralized."
              : "1 new product has been successfully centralized.",
          ),
          ~values={"productCount": patchesCount->Int.toString},
          (),
        )->React.string}
      </TextStyle>

    let renderIssuesText = (~issuesCount) =>
      <TextStyle variation=#negative weight=#medium>
        {template(
          "▪ " ++
          t(
            isPlural(issuesCount)
              ? "{{productCount}} errors detected, some products could not be centralized."
              : "1 error detected, a product could not be centralized.",
          ),
          ~values={"productCount": issuesCount->Int.toString},
          (),
        )->React.string}
      </TextStyle>

    module NoIssueNoPatchView = {
      @react.component
      let make = React.memo((~onPressHelpCenterText, ~onRequestClose) =>
        <Stack space=#xxlarge>
          <Stack space=#medium>
            <Stack>
              <TextStyle>
                {t(
                  "Following the launch of the catalog centralization operation, here are the results obtained:",
                )->React.string}
              </TextStyle>
              <Box spaceLeft=#small>
                <TextStyle weight=#medium>
                  {("▪ " ++ t("no new product has been centralized."))->React.string}
                </TextStyle>
              </Box>
            </Stack>
            <Inline space=#none>
              <TextStyle>
                {(t("To know more about the catalog centralization") ++ ", ")->React.string}
              </TextStyle>
              <TextAction
                text={t("please visit the help center.")}
                onPress=onPressHelpCenterText
                highlighted=true
              />
            </Inline>
          </Stack>
          <Inline grow=true align=#end>
            <Button size=#large variation=#neutral onPress={_ => onRequestClose()}>
              {t("Close")->React.string}
            </Button>
          </Inline>
        </Stack>
      )
    }

    module IssueNoPatchView = {
      @react.component
      let make = React.memo((~issuesCount, ~onPressHelpCenterText, ~onRequestDownloadFile) =>
        <Stack space=#xxlarge>
          <Stack space=#medium>
            <Stack>
              <TextStyle>
                {t(
                  "Following the launch of the catalog centralization operation, here are the results obtained:",
                )->React.string}
              </TextStyle>
              <Box spaceLeft=#small>
                <TextStyle weight=#medium>
                  {("▪ " ++ t("no new product has been centralized."))->React.string}
                </TextStyle>
                {renderIssuesText(~issuesCount)}
              </Box>
            </Stack>
            <TextStyle>
              {t(
                "The products could not be centralized because identical SKUs were detected within the same store. Click on the \"Download errors reporting\" button to retrieve the list of products to correct.",
              )->React.string}
            </TextStyle>
            <Inline space=#none>
              <TextStyle>
                {(t("To find out what corrections need to be made") ++ ", ")->React.string}
              </TextStyle>
              <TextAction
                text={t("please visit the help center.")}
                onPress=onPressHelpCenterText
                highlighted=true
              />
            </Inline>
          </Stack>
          <Inline grow=true align=#end>
            <Button size=#large variation=#primary onPress={_ => onRequestDownloadFile()}>
              {t("Download errors reporting")->React.string}
            </Button>
          </Inline>
        </Stack>
      )
    }

    module NoIssuePatchView = {
      @react.component
      let make = React.memo((~patchesCount, ~onPressHelpCenterText, ~onRequestClose) =>
        <Stack space=#xxlarge>
          <Stack space=#xxlarge>
            <Stack space=#medium>
              <Stack>
                <TextStyle>
                  {t(
                    "Following the launch of the catalog centralization operation, here are the results obtained:",
                  )->React.string}
                </TextStyle>
                <Box spaceLeft=#small> {renderPatchesText(~patchesCount)} </Box>
              </Stack>
              <Inline space=#none>
                <TextStyle>
                  {(t("To know more about the catalog centralization") ++ ", ")->React.string}
                </TextStyle>
                <TextAction
                  text={t("please visit the help center.")}
                  onPress=onPressHelpCenterText
                  highlighted=true
                />
              </Inline>
            </Stack>
            <Inline grow=true align=#end>
              <Button size=#large variation=#neutral onPress={_ => onRequestClose()}>
                {t("Close")->React.string}
              </Button>
            </Inline>
          </Stack>
        </Stack>
      )
    }

    module IssuePatchView = {
      @react.component
      let make = React.memo((
        ~issuesCount,
        ~patchesCount,
        ~onPressHelpCenterText,
        ~onRequestDownloadFile,
      ) =>
        <Stack space=#xxlarge>
          <Stack space=#xxlarge>
            <Stack space=#medium>
              <Stack>
                <TextStyle>
                  {t(
                    "Following the launch of the catalog centralization operation, here are the results obtained:",
                  )->React.string}
                </TextStyle>
                <Box spaceLeft=#small>
                  {renderPatchesText(~patchesCount)}
                  {renderIssuesText(~issuesCount)}
                </Box>
              </Stack>
              <TextStyle>
                {t(
                  "The products could not be centralized because identical SKUs were detected within the same store. Click on the \"Download errors reporting\" button to retrieve the list of products to correct.",
                )->React.string}
              </TextStyle>
              <Inline space=#none>
                <TextStyle>
                  {(t("To find out what corrections need to be made") ++ ", ")->React.string}
                </TextStyle>
                <TextAction
                  text={t("please visit the help center.")}
                  onPress=onPressHelpCenterText
                  highlighted=true
                />
              </Inline>
            </Stack>
            <Inline grow=true align=#end>
              <Button size=#large variation=#primary onPress={_ => onRequestDownloadFile()}>
                {t("Download errors reporting")->React.string}
              </Button>
            </Inline>
          </Stack>
        </Stack>
      )
    }

    @react.component
    let make = React.memo((
      ~issuesCount,
      ~patchesCount,
      ~fileUrl,
      ~onRequestErrorNotification,
      ~onRequestClose,
    ) => {
      let (opened, setOpened) = React.useState(() => true)

      let onPressHelpCenterText = () => HelpCenter.showArticle(HelpCenter.howCentralizeCatalog)

      let onRequestClose = () => {
        setOpened(_ => false)
        onRequestClose()
      }

      let onRequestDownloadFile = () =>
        fileUrl->Option.forEach(url =>
          url
          ->TriggerDownload.fromUrl
          ->Future.mapError(
            () =>
              onRequestErrorNotification(
                t("An issue when attempting downloading the file occurred."),
              ),
          )
          ->Future.get(_ => onRequestClose())
        )

      <Modal
        title={t("Centralize catalogs")} variation=#secondary hideFooter=true opened onRequestClose>
        <Box spaceX=#xlarge spaceY=#xlarge>
          {switch (issuesCount > 0, patchesCount > 0) {
          | (false, false) => <NoIssueNoPatchView onPressHelpCenterText onRequestClose />
          | (false, true) => <NoIssuePatchView patchesCount onPressHelpCenterText onRequestClose />
          | (true, false) =>
            <IssueNoPatchView issuesCount onPressHelpCenterText onRequestDownloadFile />
          | (true, true) =>
            <IssuePatchView issuesCount patchesCount onPressHelpCenterText onRequestDownloadFile />
          }}
        </Box>
      </Modal>
    })
  }

  @react.component
  let make = (~text, ~onRequestErrorNotification) => {
    let {onRequestClose} = Popover.useState()
    let (asyncResult, setAsyncResult) = React.useState(() => AsyncData.NotAsked)

    React.useEffect1(() => {
      switch asyncResult {
      | Done(Error(_)) => onRequestErrorNotification(Request.serverErrorMessage)
      | _ => ()
      }
      None
    }, [asyncResult])

    let request = () =>
      Request.make(endpoint, ~method=#POST, ~bodyJson=encodeRequestBodyJson())->Future.map(json =>
        json->Result.flatMap(decodeRequestResponseJson)
      )

    let onChange = asyncResult =>
      switch asyncResult {
      | AsyncData.Done(Ok({patchesCount})) =>
        // NOTE - patching may not apply immediately, esimating the delay based on patchesCount
        // this alternative is pending a real async task manager solution on backend.
        let heuristicTimeout = 1000 + patchesCount * 175

        Js.Global.setTimeout(() => {
          setAsyncResult(_ => asyncResult)
        }, heuristicTimeout)->ignore

      | Done(Error(_)) => setAsyncResult(_ => asyncResult)
      | _ => setAsyncResult(_ => asyncResult)
      }

    let operableRequest = Ok(request)

    <>
      <SpinnerModal title=text opened={asyncResult->AsyncResult.isBusy} />
      <RequestMenuItem text operableRequest onChange />
      {switch asyncResult {
      | Done(Ok({issuesCount, patchesCount, fileUrl})) =>
        <ReportModal issuesCount patchesCount fileUrl onRequestErrorNotification onRequestClose />
      | _ => React.null
      }}
    </>
  }
}

module CatalogListExtraParamsPrice = {
  type t = {
    id: string,
    name: string,
  }

  let jsonCodec = JsonCodec.object2(
    ({id, name}) => (id, name),
    ((priceId, priceName)) => Ok({id: priceId, name: priceName}),
    JsonCodec.field("priceId", JsonCodec.string),
    JsonCodec.field("priceName", JsonCodec.string),
  )
}

module CatalogListTableHeaderActionPricelistSelect = {
  let selectByPricelists = (pricelists: array<CatalogListPricelist.t>, ~currentPrice) => {
    let pricelist = Option.orElse(
      pricelists->Array.getBy(current =>
        switch currentPrice {
        | Some({CatalogListExtraParamsPrice.id: priceId}) => priceId === current.id
        | None => false
        }
      ),
      pricelists->Array.getBy(current =>
        switch currentPrice {
        | Some({name: priceName}) =>
          priceName->Js.String2.toLowerCase === current.name->Js.String2.toLowerCase
        | None => false
        }
      ),
    )
    let defaultPricelist = Option.orElse(
      pricelists->Array.getBy(pricelist => pricelist.enableByDefault),
      pricelists[0],
    )
    pricelist->Option.orElse(defaultPricelist)
  }

  @react.component
  let make = React.memo((
    ~shopId,
    ~pricelistsAsyncResult,
    ~price as initialPrice,
    ~onRequestPriceChange,
  ) => {
    let (selectedPrice, setSelectedPrice) = React.useState(() => None)
    let captureEvent = SessionTracker.useCaptureEvent()

    ReactUpdateEffect.use1(() => {
      setSelectedPrice(_ => None)
      None
    }, [shopId])

    ReactUpdateEffect.use1(() => {
      switch pricelistsAsyncResult {
      | AsyncData.Done(Ok(pricelists)) if selectedPrice->Option.isNone =>
        let pricelist = selectByPricelists(pricelists, ~currentPrice=initialPrice)
        setSelectedPrice(_ => pricelist)
      | _ => ()
      }
      None
    }, [pricelistsAsyncResult])

    ReactUpdateEffect.use1(() => {
      let initialPriceId = initialPrice->Option.mapWithDefault("", price => price.id)
      switch selectedPrice {
      | Some(pricelist) if pricelist.id !== initialPriceId =>
        let price = {CatalogListExtraParamsPrice.id: pricelist.id, name: pricelist.name}
        onRequestPriceChange(price)
      | _ => ()
      }
      None
    }, [selectedPrice])

    let sections = [
      {
        Select.items: switch pricelistsAsyncResult {
        | Done(Ok(pricelists)) =>
          let sortedPricelists =
            pricelists->SortArray.stableSortBy((next, current) =>
              current.enableByDefault !== next.enableByDefault ? 1 : 0
            )
          sortedPricelists->Array.map(item => {
            Select.key: item.id,
            label: item.name,
            value: Some(item),
          })
        | _ => []
        },
      },
    ]

    let renderTriggerView = (~children as _, ~item as _, ~hovered, ~active, ~focused as _) =>
      <Icon
        size=12.
        name=#settings_bold
        fill={hovered || active ? Colors.neutralColor90 : Colors.neutralColor50}
      />

    let renderItemContent = React.useCallback0(item => {
      let enableByDefault =
        item.Select.value->Option.mapWithDefault(
          false,
          value => value.CatalogListPricelist.enableByDefault,
        )
      let formattedLabel = item.label ++ (enableByDefault ? " *" : "")

      <Tooltip
        content={<Tooltip.Span text={t("By default")} />}
        placement=#start
        closeDelay=0
        disabled={!enableByDefault}>
        <Inline space=#normal align=#spaceBetween>
          <span> {formattedLabel->React.string} </span>
          {switch item.value {
          | Some({taxIncluded}) =>
            let taxTypename = t(taxIncluded ? "VAT incl." : "VAT excl.")
            <Badge variation=#neutral size=#small> {taxTypename->React.string} </Badge>
          | None => React.null
          }}
        </Inline>
      </Tooltip>
    })

    let handleChange = value => {
      setSelectedPrice(_ => value)
      captureEvent(#catalog_pricelist_select)
    }

    <Select
      preset=#inputField({required: false})
      label={t("Price list")}
      overlayNoResultLabel={t("No price list found")}
      renderTriggerView
      renderItemContent
      sections
      value=selectedPrice
      onChange=handleChange
    />
  })
}

module CatalogListPurchasePriceCell = {
  open CatalogListPricingSettings.RetailPriceCalculatingMethod

  module Mutation = %graphql(`
    mutation updatePurchasePrice($id: ID!, $input: InputUpdateVariant!) {
      updateVariant(id: $id, input: $input) {
        id
        purchasedPrice
      }
    }
  `)

  let useMutation = (~variantId) => {
    let (mutate, _) = Mutation.use()
    (~purchasePrice) => {
      let input = Mutation.makeInputObjectInputUpdateVariant(~purchasedPrice=purchasePrice, ())
      mutate(Mutation.makeVariables(~id=variantId, ~input, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => ())
      ->Future.mapError(_ => ())
    }
  }

  let amountInputNumberAppender = InputNumberField.Custom(#EUR->Intl.toCurrencySymbol)
  let priceInputNumberAppender = (~bulkUnit) => InputNumberField.Custom(
    #EUR->Intl.toCurrencySymbol ++ bulkUnit->Option.mapWithDefault("", unit => ` / ${unit}`),
  )

  module PurchasePriceCalculatingMethodDialogSections = {
    @react.component
    let make = React.memo((~priceIsNet, ~bulkUnit, ~value, ~onChange) => {
      let priceInputAppender = priceInputNumberAppender(~bulkUnit)

      <Dialog.Section title={t("catalog_list.purchase_price_cell.dialog.section_final")}>
        <Stack space=#medium>
          <Box spaceTop=#xxsmall>
            <InputNumberField
              label={priceIsNet ? t("Net purchase price") : t("Gross purchase price")}
              appender=priceInputAppender
              minValue=0.
              precision=3
              strongStyle=true
              autoFocused=true
              value
              onChange
            />
          </Box>
        </Stack>
      </Dialog.Section>
    })
  }

  module FullPurchasePriceCalculatingMethodDialogSections = {
    @react.component
    let make = React.memo((
      ~priceIsNet,
      ~feesList,
      ~bulkUnit,
      ~value as purchasePrice,
      ~onChange,
    ) => {
      let {CatalogListTableRow.taxesAmount: taxesFeeAmount, otherAmount: otherFeeAmount} = feesList

      let fullPurchasePrice = CatalogListTableRow.computeFullPurchasePrice(
        ~purchasePrice,
        ~taxesFeeAmount,
        ~otherFeeAmount,
      )

      let onFullPurchasePriceChange = fullPurchasePrice => {
        let charges = PriceCalculator.sumManyCharges([taxesFeeAmount, otherFeeAmount])
        onChange(PriceCalculator.Purchase.fromValueWithCharges(fullPurchasePrice, ~charges))
      }

      let priceInputAppender = priceInputNumberAppender(~bulkUnit)
      let chargesSectionDefaultCollapsed = taxesFeeAmount === 0. && otherFeeAmount === 0.

      <>
        <Dialog.Section
          title={t("catalog_list.purchase_price_cell.dialog.section_before_charges")}
          collapsable=true
          defaultCollapsed=true>
          <Box spaceTop=#xxsmall>
            <InputNumberField
              label={t(
                "catalog_list.purchase_price_cell.dialog.section_before_charges.input_label" ++ (
                  priceIsNet ? ".net" : ".gross"
                ),
              )}
              appender=priceInputAppender
              minValue=0.
              precision=3
              value=purchasePrice
              onChange
            />
          </Box>
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.price_cell.dialog.section_charges")}
          collapsable=true
          defaultCollapsed=chargesSectionDefaultCollapsed>
          <Box spaceTop=#xxsmall>
            <Group spaceX=#normal>
              <InputNumberField
                label={t(
                  "catalog_list.purchase_price_cell.dialog.section_charges.taxes_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                precision=3
                hideStepper=true
                value=taxesFeeAmount
                onChange={_ => ()}
              />
              <InputNumberField
                label={t(
                  "catalog_list.purchase_price_cell.dialog.section_charges.other_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                precision=3
                hideStepper=true
                value=otherFeeAmount
                onChange={_ => ()}
              />
            </Group>
          </Box>
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.purchase_price_cell.dialog.section_final")} collapsable=true>
          <Box spaceTop=#xxsmall>
            <InputNumberField
              label={t(
                "catalog_list.purchase_price_cell.full_purchase_price_calculating_method_dialog.section_final.input_label" ++ (
                  priceIsNet ? ".net" : ".gross"
                ),
              )}
              appender=priceInputAppender
              minValue={fullPurchasePrice -. purchasePrice}
              precision=3
              strongStyle=true
              autoFocused=true
              value=fullPurchasePrice
              onChange=onFullPurchasePriceChange
            />
          </Box>
        </Dialog.Section>
      </>
    })
  }

  module AcquisitionCostPriceCalculatingMethodDialogSections = {
    @react.component
    let make = React.memo((
      ~priceIsNet,
      ~feesList,
      ~bulkUnit,
      ~value as purchasePrice,
      ~onChange,
    ) => {
      let {
        CatalogListTableRow.transportAmount: transportFeeAmount,
        taxesAmount: taxesFeeAmount,
        otherAmount: otherFeeAmount,
      } = feesList

      let onAcquisitionCostChange = acquisitionCost => {
        let charges = PriceCalculator.sumManyCharges([
          transportFeeAmount,
          taxesFeeAmount,
          otherFeeAmount,
        ])
        onChange(PriceCalculator.Purchase.fromValueWithCharges(acquisitionCost, ~charges))
      }

      let acquisitionCost = CatalogListTableRow.computeAcquisitionCost(
        ~purchasePrice,
        ~transportFeeAmount,
        ~taxesFeeAmount,
        ~otherFeeAmount,
      )

      let priceInputAppender = priceInputNumberAppender(~bulkUnit)
      let chargesSectionDefaultCollapsed =
        transportFeeAmount === 0. && taxesFeeAmount === 0. && otherFeeAmount === 0.

      <>
        <Dialog.Section
          title={t("catalog_list.purchase_price_cell.dialog.section_before_charges")}
          collapsable=true
          defaultCollapsed=true>
          <Box spaceTop=#xxsmall>
            <InputNumberField
              label={t(
                "catalog_list.purchase_price_cell.dialog.section_before_charges.input_label" ++ (
                  priceIsNet ? ".net" : ".gross"
                ),
              )}
              appender=priceInputAppender
              minValue=0.
              precision=3
              value=purchasePrice
              onChange
            />
          </Box>
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.price_cell.dialog.section_charges")}
          collapsable=true
          defaultCollapsed=chargesSectionDefaultCollapsed>
          <Box spaceTop=#xxsmall>
            <Group spaceX=#normal>
              <InputNumberField
                label={t(
                  "catalog_list.purchase_price_cell.dialog.section_charges.transport_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                precision=3
                hideStepper=true
                value=transportFeeAmount
                onChange={_ => ()}
              />
              <InputNumberField
                label={t(
                  "catalog_list.purchase_price_cell.dialog.section_charges.taxes_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                precision=3
                hideStepper=true
                value=taxesFeeAmount
                onChange={_ => ()}
              />
              <InputNumberField
                label={t(
                  "catalog_list.purchase_price_cell.dialog.section_charges.other_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                precision=3
                hideStepper=true
                value=otherFeeAmount
                onChange={_ => ()}
              />
            </Group>
          </Box>
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.purchase_price_cell.dialog.section_final")} collapsable=true>
          <Box spaceTop=#xxsmall>
            <InputNumberField
              label={t(
                "catalog_list.purchase_price_cell.acquisition_cost_calculating_method_dialog.section_final.input_label" ++ (
                  priceIsNet ? ".net" : ".gross"
                ),
              )}
              appender=priceInputAppender
              minValue={acquisitionCost -. purchasePrice}
              precision=3
              autoFocused=true
              strongStyle=true
              value=acquisitionCost
              onChange=onAcquisitionCostChange
            />
          </Box>
        </Dialog.Section>
      </>
    })
  }

  @react.component
  let make = React.memo((
    ~variantId,
    ~bulkUnit,
    ~feesList,
    ~discount,
    ~purchasePriceType,
    ~calculatingMethod,
    ~value as initialValue,
    ~useMutation=useMutation,
  ) => {
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
    let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())

    let (mutationLoading, setMutationLoading) = React.useState(() => false)
    let mutate = useMutation(~variantId)

    // TODO - UX: handle discount exceeding purchasePrice case
    let (initialValue, priceIsNet) = switch purchasePriceType {
    | CatalogListPricingSettings.PurchasePriceType.Gross => (initialValue, false)
    | Net => (CatalogListTableRow.computeNetPurchasePrice(initialValue, ~discount), true)
    }

    let (value, setValue) = React.useState(() => initialValue)

    ReactUpdateEffect.use1(() => {
      if popover.opened {
        setValue(_ => initialValue)
      }
      None
    }, [popover.opened])

    // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
    let onCommitRequestClose = popover.onRequestClose
    let popover = {
      ...popover,
      onRequestClose: () =>
        if !triggerHovered {
          popover.onRequestClose()
        },
    }

    let onCommit = React.useCallback2(() => {
      if value !== initialValue {
        setMutationLoading(_ => true)
        let rawPurchasePrice = priceIsNet
          ? PriceCalculator.Purchase.fromNetValue(value, ~discount)
          : value
        mutate(~purchasePrice=rawPurchasePrice)->Future.get(
          _ => {
            setMutationLoading(_ => false)
            onCommitRequestClose()
          },
        )
      } else {
        onCommitRequestClose()
      }
    }, (value, initialValue))

    let dialogTitle =
      calculatingMethod->CatalogListPricingSettings.RetailPriceCalculatingMethod.toLabel

    let formattedValue = {
      let computedInitialValue = switch calculatingMethod {
      | PurchasePrice => initialValue
      | FullPurchasePrice =>
        CatalogListTableRow.computeFullPurchasePrice(
          ~purchasePrice=initialValue,
          ~taxesFeeAmount=feesList.CatalogListTableRow.taxesAmount,
          ~otherFeeAmount=feesList.otherAmount,
        )
      | AcquisitionCost =>
        CatalogListTableRow.computeAcquisitionCost(
          ~purchasePrice=initialValue,
          ~taxesFeeAmount=feesList.CatalogListTableRow.taxesAmount,
          ~otherFeeAmount=feesList.otherAmount,
          ~transportFeeAmount=feesList.transportAmount,
        )
      }
      computedInitialValue->Intl.currencyFormat(
        ~currency=#EUR,
        ~minimumFractionDigits=3,
        ~maximumFractionDigits=3,
      )
    }

    <>
      <OpeningButton
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        opened=popover.opened
        onPress={_ => popover.onRequestToggle()}>
        <TextStyle size=#xsmall> {formattedValue->React.string} </TextStyle>
      </OpeningButton>
      <Popover triggerRef=popoverTriggerRef state=popover modal=false placement=#"bottom end">
        <Dialog
          title=dialogTitle
          width=385.
          ariaProps=popoverAriaProps.overlayProps
          commitButtonText={t("catalog_list.purchase_price_cell.dialog.commit_button_text")}
          commitLoading=mutationLoading
          onCommit
          onRequestClose=popover.onRequestClose>
          {switch calculatingMethod {
          | PurchasePrice =>
            <PurchasePriceCalculatingMethodDialogSections
              priceIsNet bulkUnit value onChange={value => setValue(_ => value)}
            />
          | FullPurchasePrice =>
            <FullPurchasePriceCalculatingMethodDialogSections
              priceIsNet feesList bulkUnit value onChange={value => setValue(_ => value)}
            />
          | AcquisitionCost =>
            <AcquisitionCostPriceCalculatingMethodDialogSections
              priceIsNet feesList bulkUnit value onChange={value => setValue(_ => value)}
            />
          }}
        </Dialog>
      </Popover>
    </>
  })
}

module CatalogListRetailPriceCell = {
  open StyleX
  open CatalogListPricingSettings.RetailPriceCalculatingMethod

  module InputContainer = {
    let styles = StyleX.create({
      "CatalogListRetailPriceCell_InputContainer_root": style(
        ~display=#flex,
        ~flexDirection=#column,
        ~padding=Spaces.normalPx,
        ~borderRadius=Spaces.xsmallPx,
        ~backgroundColor=Colors.neutralColor10,
        (),
      ),
    })
    let styleProps = () => StyleX.props([styles["CatalogListRetailPriceCell_InputContainer_root"]])

    @react.component
    let make = React.memo((~children) => {
      let {?style, ?className} = styleProps()
      <div ?style ?className> children </div>
    })
  }

  module PriceDifferentialIndicatorSection = {
    @react.component
    let make = React.memo((
      ~rateType as kind,
      ~taxRate,
      ~purchasePrice,
      ~priceExcludingTaxValue,
      ~totalChargesAmount as charges=0.,
      ~onChange,
    ) => {
      let value =
        priceExcludingTaxValue->PriceCalculator.Retail.Rate.apply(
          ~kind,
          ~charges,
          ~purchasePrice,
          ~taxRate,
        )

      let onChange = value => {
        switch value->Option.mapWithDefault(
          None,
          PriceCalculator.Retail.Rate.undo(~kind, ~charges, ~purchasePrice, ~taxRate),
        ) {
        | Some(priceValue) => onChange(priceValue)
        | None => ()
        }
      }

      <Dialog.Section>
        <InputOptionalNumberField
          label={CatalogListPricingSettings.PriceDifferentialIndicator.toLabel(kind)}
          disabled={purchasePrice === 0.}
          placeholder={t("N/C")}
          appender=?{kind !== Coefficient ? Some(Percent) : None}
          minValue={kind !== Coefficient ? 0. : 1.}
          maxValue=?{kind === MarginRate ? Some(99.) : None}
          minPrecision=0
          precision=4
          value
          onChange
        />
      </Dialog.Section>
    })
  }

  module Mutation = %graphql(`
    mutation createOrUpdateVariantPrice($input: inputCreateOrUpdateVariantPriceTypeDef!) {
      createOrUpdateVariantPrice(input: $input) {
        id
        valueExcludingTax
        valueIncludingTax
        price { id }
        variant {
          id
          variantPrices {
            edges {
              node {
                id
              }
            }
          }
        }
      }
    }
  `)

  let useMutation = (~variantId, ~priceId, ~shopId) => {
    let (mutate, _) = Mutation.use()
    (~valueIncludingTax, ~valueExcludingTax) => {
      let input = Mutation.makeInputObjectinputCreateOrUpdateVariantPriceTypeDef(
        ~variantId,
        ~priceId,
        ~shopId,
        ~valueIncludingTax,
        ~valueExcludingTax,
        (),
      )
      mutate(Mutation.makeVariables(~input, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => ())
      ->Future.mapError(_ => ())
    }
  }

  let amountInputNumberAppender = InputNumberField.Custom(#EUR->Intl.toCurrencySymbol)
  let priceInputNumberAppender = (~bulkUnit) => InputNumberField.Custom(
    #EUR->Intl.toCurrencySymbol ++ bulkUnit->Option.mapWithDefault("", unit => ` / ${unit}`),
  )

  open PriceCalculator.Retail

  module PurchasePriceCalculatingMethodDialogSections = {
    @react.component
    let make = React.memo((
      ~feesList,
      ~bulkUnit,
      ~rateType,
      ~taxIncluded,
      ~taxRate,
      ~purchasePrice,
      ~value as priceExcludingTaxValue,
      ~onChange,
    ) => {
      // Charges section n°2
      let {
        CatalogListTableRow.transportAmount: transportFeeAmountValue,
        taxesAmount: taxesFeeAmountValue,
        otherAmount: otherFeeAmountValue,
      } = feesList
      let totalChargesAmount as charges = PriceCalculator.sumManyCharges([
        transportFeeAmountValue,
        taxesFeeAmountValue,
        otherFeeAmountValue,
      ])

      // Before charges section n°1
      let priceExcludingChargesAndTaxValue = priceExcludingTaxValue->Charges.remove(~charges)
      let priceIncludingChargesAndTaxValue = priceExcludingChargesAndTaxValue->Tax.add(~taxRate)

      let onPriceExcludingChargesAndTaxChange = value => onChange(value->Charges.addBack(~charges))
      let onPriceIncludingChargesAndTaxChange = value =>
        onChange(value->Tax.removeBack(~taxRate)->Charges.addBack(~charges))

      // Final section n°3
      let marginExcludingChargesAndTaxValue =
        priceExcludingChargesAndTaxValue->Margin.compute(~purchasePrice)
      let priceIncludingTaxValue = priceExcludingTaxValue->Tax.add(~taxRate)

      let onPriceExcludingTaxChange = value => onChange(value)
      let onPriceIncludingTaxChange = value => onChange(value->Tax.removeBack(~taxRate))

      let chargesSectionDefaultCollapsed =
        transportFeeAmountValue === 0. && taxesFeeAmountValue === 0. && otherFeeAmountValue === 0.

      <>
        <PriceDifferentialIndicatorSection
          rateType taxRate purchasePrice priceExcludingTaxValue totalChargesAmount onChange
        />
        <Dialog.Section
          title={t("catalog_list.retail_price_cell.dialog.section_before_charges")}
          collapsable=true
          defaultCollapsed=true>
          <Box spaceTop=#xsmall>
            <InputNumberField
              label={t(
                "catalog_list.retail_price_cell.dialog.section_before_charges.price_excluding_tax.input_label",
              )}
              appender={priceInputNumberAppender(~bulkUnit)}
              minValue=0.
              precision=3
              value=priceExcludingChargesAndTaxValue
              onChange=onPriceExcludingChargesAndTaxChange
            />
          </Box>
          {if taxIncluded {
            <InputNumberField
              label={t(
                "catalog_list.retail_price_cell.dialog.section_before_charges.price_including_tax.input_label",
              )}
              appender={priceInputNumberAppender(~bulkUnit)}
              strongStyle=true
              minValue=0.
              precision=3
              value=priceIncludingChargesAndTaxValue
              onChange=onPriceIncludingChargesAndTaxChange
            />
          } else {
            React.null
          }}
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.price_cell.dialog.section_charges")}
          collapsable=true
          defaultCollapsed=chargesSectionDefaultCollapsed>
          <Box spaceTop=#xxsmall>
            <Group spaceX=#normal>
              <InputNumberField
                label={t(
                  "catalog_list.retail_price_cell.dialog.section_charges.transport_fee.input_label.shorten",
                )}
                appender=amountInputNumberAppender
                disabled=true
                hideStepper=true
                precision=3
                value=transportFeeAmountValue
                onChange={_ => ()}
              />
              <InputNumberField
                label={t(
                  "catalog_list.retail_price_cell.dialog.section_charges.taxes_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                hideStepper=true
                precision=3
                value=taxesFeeAmountValue
                onChange={_ => ()}
              />
              <InputNumberField
                label={t(
                  "catalog_list.retail_price_cell.dialog.section_charges.other_fee.input_label",
                )}
                appender=amountInputNumberAppender
                disabled=true
                hideStepper=true
                precision=3
                value=otherFeeAmountValue
                onChange={_ => ()}
              />
            </Group>
          </Box>
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.retail_price_cell.dialog.section_final")} collapsable=true>
          {if taxIncluded {
            <Box spaceTop=#xsmall>
              <InputNumberField
                label={t(
                  "catalog_list.retail_price_cell.dialog.section_final.price_excluding_tax.input_label",
                )}
                appender={priceInputNumberAppender(~bulkUnit)}
                strongStyle={!taxIncluded}
                minValue=0.
                precision=2
                value=priceExcludingTaxValue
                onChange=onPriceExcludingTaxChange
              />
            </Box>
          } else {
            React.null
          }}
          <InputContainer>
            <Group
              grid=["auto", bulkUnit->Option.isSome ? "165px" : "120px"] spaceX=#normal wrap=false>
              <InputNumberField
                label={t(
                  taxIncluded
                    ? "catalog_list.retail_price_cell.dialog.section_final.price_including_tax.input_label"
                    : "catalog_list.retail_price_cell.dialog.section_final.price_excluding_tax.input_label",
                )}
                appender={priceInputNumberAppender(~bulkUnit)}
                strongStyle=true
                autoFocused=true
                minValue=0.
                precision=2
                value=priceIncludingTaxValue
                onChange=onPriceIncludingTaxChange
              />
              <InputNumberField
                label={t("Margin")}
                appender={priceInputNumberAppender(~bulkUnit)}
                disabled=true
                strongStyle=true
                hideStepper=true
                precision=2
                value=marginExcludingChargesAndTaxValue
                onChange={_ => ()}
              />
            </Group>
          </InputContainer>
        </Dialog.Section>
      </>
    })
  }

  module FullPurchasePriceCalculatingMethodDialogSections = {
    @react.component
    let make = React.memo((
      ~feesList,
      ~bulkUnit,
      ~rateType,
      ~taxIncluded,
      ~taxRate,
      ~purchasePrice,
      ~value as priceExcludingTaxValue,
      ~onChange,
    ) => {
      // Charges section n°2
      let {CatalogListTableRow.transportAmount: transportFeeAmountValue} = feesList
      let totalChargesAmount as charges = transportFeeAmountValue

      // Before charges section n°1
      let priceExcludingChargesAndTaxValue = priceExcludingTaxValue->Charges.remove(~charges)
      let priceIncludingChargesAndTaxValue = priceExcludingChargesAndTaxValue->Tax.add(~taxRate)

      let onPriceExcludingChargesAndTaxChange = value => onChange(value->Charges.addBack(~charges))
      let onPriceIncludingChargesAndTaxChange = value =>
        onChange(value->Tax.removeBack(~taxRate)->Charges.addBack(~charges))

      // Final section n°3
      let marginExcludingChargesAndTaxValue =
        priceExcludingChargesAndTaxValue->Margin.compute(~purchasePrice)
      let priceIncludingTaxValue = priceExcludingTaxValue->Tax.add(~taxRate)

      let onPriceExcludingTaxChange = value => onChange(value)
      let onPriceIncludingTaxChange = value => onChange(value->Tax.removeBack(~taxRate))

      let chargesSectionDefaultCollapsed = transportFeeAmountValue === 0.

      <>
        <PriceDifferentialIndicatorSection
          rateType taxRate purchasePrice priceExcludingTaxValue totalChargesAmount onChange
        />
        <Dialog.Section
          title={t("catalog_list.retail_price_cell.dialog.section_before_charges")}
          collapsable=true
          defaultCollapsed=true>
          <Box spaceTop=#xsmall>
            <InputNumberField
              label={t(
                "catalog_list.retail_price_cell.dialog.section_before_charges.price_excluding_tax.input_label",
              )}
              appender={priceInputNumberAppender(~bulkUnit)}
              minValue=0.
              precision=3
              value=priceExcludingChargesAndTaxValue
              onChange=onPriceExcludingChargesAndTaxChange
            />
          </Box>
          {if taxIncluded {
            <InputNumberField
              label={t(
                "catalog_list.retail_price_cell.dialog.section_before_charges.price_including_tax.input_label",
              )}
              appender={priceInputNumberAppender(~bulkUnit)}
              strongStyle=true
              minValue=0.
              precision=3
              value=priceIncludingChargesAndTaxValue
              onChange=onPriceIncludingChargesAndTaxChange
            />
          } else {
            React.null
          }}
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.price_cell.dialog.section_charges")}
          collapsable=true
          defaultCollapsed=chargesSectionDefaultCollapsed>
          <Box spaceTop=#xxsmall>
            <InputNumberField
              label={t(
                "catalog_list.retail_price_cell.dialog.section_charges.transport_fee.input_label",
              )}
              appender=amountInputNumberAppender
              disabled=true
              hideStepper=true
              precision=3
              value=transportFeeAmountValue
              onChange={_ => ()}
            />
          </Box>
        </Dialog.Section>
        <Dialog.Section
          title={t("catalog_list.retail_price_cell.dialog.section_final")} collapsable=true>
          {if taxIncluded {
            <Box spaceTop=#xsmall>
              <InputNumberField
                label={t(
                  "catalog_list.retail_price_cell.dialog.section_final.price_excluding_tax.input_label",
                )}
                appender={priceInputNumberAppender(~bulkUnit)}
                strongStyle={!taxIncluded}
                minValue=0.
                precision=2
                value=priceExcludingTaxValue
                onChange=onPriceExcludingTaxChange
              />
            </Box>
          } else {
            React.null
          }}
          <InputContainer>
            <Group
              grid=["auto", bulkUnit->Option.isSome ? "165px" : "120px"] spaceX=#normal wrap=false>
              <InputNumberField
                label={t(
                  taxIncluded
                    ? "catalog_list.retail_price_cell.dialog.section_final.price_including_tax.input_label"
                    : "catalog_list.retail_price_cell.dialog.section_final.price_excluding_tax.input_label",
                )}
                appender={priceInputNumberAppender(~bulkUnit)}
                strongStyle=true
                autoFocused=true
                minValue=0.
                precision=2
                value=priceIncludingTaxValue
                onChange=onPriceIncludingTaxChange
              />
              <InputNumberField
                label={t("Margin")}
                appender={priceInputNumberAppender(~bulkUnit)}
                disabled=true
                strongStyle=true
                hideStepper=true
                precision=2
                value=marginExcludingChargesAndTaxValue
                onChange={_ => ()}
              />
            </Group>
          </InputContainer>
        </Dialog.Section>
      </>
    })
  }

  module AcquisitionCostCalculatingMethodDialogSection = {
    @react.component
    let make = React.memo((
      ~bulkUnit,
      ~rateType,
      ~taxIncluded,
      ~taxRate,
      ~purchasePrice,
      ~value as priceExcludingTaxValue,
      ~onChange,
    ) => {
      // Final section n°1
      let marginExcludingTaxValue = priceExcludingTaxValue->Margin.compute(~purchasePrice)
      let priceIncludingTaxValue = priceExcludingTaxValue->Tax.add(~taxRate)

      let onPriceExcludingTaxChange = value => onChange(value)
      let onPriceIncludingTaxChange = value => onChange(value->Tax.removeBack(~taxRate))

      <>
        <PriceDifferentialIndicatorSection
          rateType taxRate purchasePrice priceExcludingTaxValue onChange
        />
        <Dialog.Section title={t("catalog_list.retail_price_cell.dialog.section_final")}>
          {if taxIncluded {
            <Box spaceTop=#xsmall>
              <InputNumberField
                label={t(
                  "catalog_list.retail_price_cell.dialog.section_final.price_excluding_tax.input_label",
                )}
                appender={priceInputNumberAppender(~bulkUnit)}
                strongStyle={!taxIncluded}
                minValue=0.
                precision=2
                value=priceExcludingTaxValue
                onChange=onPriceExcludingTaxChange
              />
            </Box>
          } else {
            React.null
          }}
          <InputContainer>
            <Group
              grid=["auto", bulkUnit->Option.isSome ? "165px" : "120px"] spaceX=#normal wrap=false>
              <InputNumberField
                label={t(
                  taxIncluded
                    ? "catalog_list.retail_price_cell.dialog.section_final.price_including_tax.input_label"
                    : "catalog_list.retail_price_cell.dialog.section_final.price_excluding_tax.input_label",
                )}
                appender={priceInputNumberAppender(~bulkUnit)}
                strongStyle=true
                autoFocused=true
                minValue=0.
                precision=2
                value=priceIncludingTaxValue
                onChange=onPriceIncludingTaxChange
              />
              <InputNumberField
                label={t("Margin")}
                appender={priceInputNumberAppender(~bulkUnit)}
                disabled=true
                strongStyle=true
                hideStepper=true
                precision=2
                value=marginExcludingTaxValue
                onChange={_ => ()}
              />
            </Group>
          </InputContainer>
        </Dialog.Section>
      </>
    })
  }

  let styles = StyleX.create({
    "root": style(~display=#flex, ~flexDirection=#column, ~gap=Spaces.xxsmallPx, ()),
  })
  let styleProps = () => StyleX.props([styles["root"]])

  @react.component
  let make = React.memo((
    ~variantId,
    ~shopId,
    ~pricelist,
    ~bulkUnit,
    ~taxRate,
    ~feesList,
    ~rateType,
    ~calculatingMethod,
    ~purchasePrice,
    ~value as initialValue,
    ~useMutation=useMutation,
  ) => {
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
    let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())
    let captureEvent = SessionTracker.useCaptureEvent()

    let {CatalogListPricelist.id: priceId, name: pricelistName, taxIncluded} = pricelist

    let (mutationLoading, setMutationLoading) = React.useState(() => false)
    let mutate = useMutation(~variantId, ~priceId, ~shopId)

    let priceExists = initialValue->Option.isSome
    let {
      valueExcludingTax: initialValueExcludingTax,
      valueIncludingTax: initialValueIncludingTax,
    } = initialValue->Option.getWithDefault({
      CatalogListTableRow.valueExcludingTax: 0.,
      valueIncludingTax: 0.,
    })

    let (value, setValue) = React.useState(() => initialValueExcludingTax)

    ReactUpdateEffect.use1(() => {
      if popover.opened {
        setValue(_ => initialValueExcludingTax)
      }
      None
    }, [popover.opened])

    // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
    let onCommitRequestClose = popover.onRequestClose
    let popover = {
      ...popover,
      onRequestClose: () =>
        if !triggerHovered {
          popover.onRequestClose()
        },
    }

    let onCommit = React.useCallback2(() => {
      let valueExcludingTax = value
      let valueIncludingTax = valueExcludingTax->Tax.add(~taxRate)

      if valueExcludingTax !== initialValueExcludingTax {
        setMutationLoading(_ => true)
        mutate(~valueExcludingTax, ~valueIncludingTax)->Future.get(
          _ => {
            setMutationLoading(_ => false)
            onCommitRequestClose()
          },
        )
        captureEvent(#catalog_retailprice_click_save)
        if (
          feesList.CatalogListTableRow.transportAmount !== 0. ||
          feesList.taxesAmount !== 0. ||
          feesList.otherAmount !== 0.
        ) {
          captureEvent(#catalog_retailprice_with_charges_click_save)
        }
      } else {
        onCommitRequestClose()
      }
    }, (value, initialValueExcludingTax))

    let {?style, ?className} = styleProps()

    let dialogTitleStartElement = {
      let taxTypename = t(taxIncluded ? "VAT incl." : "VAT excl.")
      <Badge variation=#neutral size=#small> {taxTypename->React.string} </Badge>
    }

    let formattedRetailPriceExcludingTax =
      initialValueExcludingTax->Intl.currencyFormat(~currency=#EUR)
    let formattedRetailPriceIncludingTax =
      initialValueIncludingTax->Intl.currencyFormat(~currency=#EUR)

    let taxRate = taxIncluded ? taxRate : 0.

    <>
      <OpeningButton
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        opened=popover.opened
        onPress={_ => popover.onRequestToggle()}>
        {if taxIncluded && priceExists {
          <div ?style ?className>
            <TextStyle size=#xxsmall weight=#strong>
              {formattedRetailPriceIncludingTax->React.string}
            </TextStyle>
            <TextStyle size=#tiny variation=#normal lineHeight=#xnormal>
              {(formattedRetailPriceExcludingTax ++ " " ++ t("excl. tax"))->React.string}
            </TextStyle>
          </div>
        } else if priceExists {
          <TextStyle size=#xxsmall weight=#strong>
            {formattedRetailPriceExcludingTax->React.string}
          </TextStyle>
        } else {
          <TextStyle variation=#subdued size=#xsmall> {t("Undefined")->React.string} </TextStyle>
        }}
      </OpeningButton>
      <Popover triggerRef=popoverTriggerRef state=popover modal=false placement=#"bottom end">
        <Dialog
          title=pricelistName
          titleStartElement=dialogTitleStartElement
          width=385.
          ariaProps=popoverAriaProps.overlayProps
          commitLoading=mutationLoading
          commitButtonText={t("catalog_list.retail_price_cell.dialog.commit_button_text")}
          onCommit
          onRequestClose=popover.onRequestClose>
          {switch calculatingMethod {
          | PurchasePrice =>
            <PurchasePriceCalculatingMethodDialogSections
              feesList
              bulkUnit
              rateType
              purchasePrice
              taxIncluded
              taxRate
              value
              onChange={value => setValue(_ => value)}
            />
          | FullPurchasePrice =>
            <FullPurchasePriceCalculatingMethodDialogSections
              feesList
              bulkUnit
              rateType
              purchasePrice
              taxIncluded
              taxRate
              value
              onChange={value => setValue(_ => value)}
            />
          | AcquisitionCost =>
            <AcquisitionCostCalculatingMethodDialogSection
              bulkUnit
              rateType
              purchasePrice
              taxIncluded
              taxRate
              value
              onChange={value => setValue(_ => value)}
            />
          }}
        </Dialog>
      </Popover>
    </>
  })
}

module CatalogListPriceDifferentialIndicatorCell = {
  let coefficientIcon =
    <Svg width="12" height="12" viewBox="0 0 12 12">
      <Svg.Path
        d="M2.39354 9.182C2.27638 9.29916 2.27638 9.48911 2.39354 9.60626C2.5107 9.72342 2.70065 9.72342 2.8178 9.60626L5.99978 6.42429L9.18176 9.60627C9.29891 9.72342 9.48886 9.72342 9.60602 9.60627C9.72318 9.48911 9.72318 9.29916 9.60602 9.182L6.42404 6.00003L9.60603 2.81804C9.72319 2.70088 9.72319 2.51093 9.60603 2.39378C9.48887 2.27662 9.29892 2.27662 9.18177 2.39378L5.99978 5.57576L2.8178 2.39378C2.70064 2.27662 2.51069 2.27662 2.39353 2.39378C2.27637 2.51093 2.27637 2.70088 2.39353 2.81804L5.57552 6.00003L2.39354 9.182Z"
        fill=Colors.neutralColor55
      />
    </Svg>

  @react.component
  let make = (
    ~rateType,
    ~taxRate,
    ~purchasePrice,
    ~retailPriceExcludingTax,
    ~totalChargesAmount,
  ) => {
    let rate =
      retailPriceExcludingTax->PriceCalculator.Retail.Rate.apply(
        ~kind=rateType,
        ~charges=totalChargesAmount,
        ~purchasePrice,
        ~taxRate,
      )
    let formattedRate =
      rate->Option.map(rate =>
        rate->Intl.decimalFormat(~minimumFractionDigits=0, ~maximumFractionDigits=2) ++ (
            rateType === Coefficient ? "" : " %"
          )
      )

    switch (rateType, formattedRate) {
    | (Coefficient, Some(formattedRate)) =>
      <Inline space=#xsmall>
        {if rate->Option.isSome {
          <Offset width=12. height=12. top={-2.5}> {coefficientIcon} </Offset>
        } else {
          React.null
        }}
        <TextStyle size=#xsmall> {formattedRate->React.string} </TextStyle>
      </Inline>
    | (MarkupRate | MarginRate, Some(formattedRate)) =>
      <TextStyle size=#xsmall> {formattedRate->React.string} </TextStyle>
    | _ => <TextStyle variation=#subdued size=#xsmall> {Intl.t("N/C")->React.string} </TextStyle>
    }
  }
}

module CatalogListExtraParams = {
  type t = {price: CatalogListExtraParamsPrice.t}

  let jsonCodec = JsonCodec.object1(
    ({price}) => price,
    price => Ok({price: price}),
    JsonCodec.field("price", CatalogListExtraParamsPrice.jsonCodec),
  )
}

module CatalogListFilters = {
  type t = {
    shop: option<Auth.shop>,
    status: option<CatalogProduct.Status.t>,
    category: option<CatalogCategorySelectFilter.parentCategory>,
    supplier: option<SupplierSelect.supplier>,
    producer: option<string>,
    stock: option<CatalogStockRangeSelect.stockRange>,
  }

  let getStatuses = (~shopsIndependentKind) =>
    shopsIndependentKind
      ? [CatalogProduct.Status.Unarchived, Archived]
      : [Active, Inactive, Archived]

  let getUpdatedStatus = (~statuses, ~currentStatus) =>
    switch (currentStatus, statuses[0]) {
    | (Some(currentStatus), Some(defaultStatus)) =>
      Some(statuses->Array.some(status => status === currentStatus) ? currentStatus : defaultStatus)
    | (None, _) | (_, None) => None
    }

  let supplier = JsonCodec.object2(
    ({SupplierSelect.id: id, name}) => (
      id->Js.Nullable.toOption->Option.getWithDefault("null"),
      name,
    ),
    ((id, name)) => Ok({
      id: id === "null" ? Js.Nullable.null : id->Js.Nullable.return,
      name,
    }),
    JsonCodec.field("id", JsonCodec.string),
    JsonCodec.field("name", JsonCodec.string),
  )
  let category = JsonCodec.object2(
    ({CatalogCategorySelectFilter.id: id, name}) => (
      id->Js.Nullable.toOption->Option.getWithDefault("null"),
      name,
    ),
    ((id, name)) => Ok({
      id: id === "null" ? Js.Nullable.null : id->Js.Nullable.return,
      name,
    }),
    JsonCodec.field("id", JsonCodec.string),
    JsonCodec.field("name", JsonCodec.string),
  )
  let stock = JsonCodec.object2(
    ({CatalogStockRangeSelect.min: ?min, ?max}) => (min, max),
    ((min, max)) => Ok({
      ?min,
      ?max,
    }),
    JsonCodec.field("min", JsonCodec.float)->JsonCodec.optional,
    JsonCodec.field("max", JsonCodec.float)->JsonCodec.optional,
  )
  let jsonCodec = (~shops) =>
    JsonCodec.object6(
      ({shop, status, category, supplier, producer, stock}) => (
        shop->Option.map(shop => shop.id),
        status->Option.map(CatalogProduct.Status.toString),
        category,
        supplier,
        producer,
        stock,
      ),
      ((shopId, status, category, supplier, producer, stock)) => Ok({
        shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
        status: switch status->Option.map(CatalogProduct.Status.fromString) {
        | Some(Ok(status)) => Some(status)
        | _ => None
        },
        category,
        supplier,
        producer,
        stock,
      }),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("status", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("category", category)->JsonCodec.optional,
      JsonCodec.field("supplier", supplier)->JsonCodec.optional,
      JsonCodec.field("producer", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("stock", stock)->JsonCodec.optional,
    )

  module RegionSelect = {
    module Query = %graphql(`
      query productRegions($country: String) {
        productRegions(country: $country)
      }
    `)

    @react.component
    let make = (~disabled, ~value, ~onChange) => {
      let results = Query.use(Query.makeVariables(~country="France", ()))

      let items = switch results {
      | {data: Some({productRegions})} =>
        productRegions
        ->Array.keep(suggestion => suggestion !== "")
        ->Array.map(productRegions => {
          Select.label: productRegions,
          key: productRegions,
          value: Some(productRegions),
        })
      | _ => []
      }
      let sections = {
        let defaultItem = {
          Select.key: "default",
          label: t("All"),
          value: None,
          sticky: true,
        }
        [{Select.items: [defaultItem]}, {title: t("Regions"), items}]
      }

      <Tooltip
        content={<Tooltip.Span text={t("Please select a shop beforehand.")} />}
        delay=0
        placement=#top
        disabled={!disabled}>
        <Select
          label={t("Regions")} preset=#filter size=#compact disabled sections value onChange
        />
      </Tooltip>
    }
  }
}

module CatalogListSorting = {
  type direction = ReactStately.Table.sortDirection
  type t =
    | Name(direction)
    | Producer(direction)
    | PurchasePrice(direction)
    | CreatedAt(direction)

  let toSortDescriptor = sort =>
    switch sort {
    | Name(direction) => {ReactStately.Table.column: "reference", direction}
    | Producer(direction) => {column: "producer", direction}
    | PurchasePrice(direction) => {column: "purchase-price", direction}
    | CreatedAt(direction) => {column: "created-at", direction}
    }

  let fromSortDescriptor = sort =>
    switch sort {
    | {ReactStately.Table.column: "reference", direction} => Ok(Name(direction))
    | {column: "producer", direction} => Ok(Producer(direction))
    | {column: "purchase-price", direction} => Ok(PurchasePrice(direction))
    | {column: "created-at", direction} => Ok(CreatedAt(direction))
    | {column: _} => Error()
    }

  let jsonCodec = JsonCodec.object2(
    sort => (toSortDescriptor(sort).column, (toSortDescriptor(sort).direction :> string)),
    ((column, direction)) => {
      let direction = direction === "descending" ? #descending : #ascending
      switch fromSortDescriptor({column, direction}) {
      | Error() => Error(#SyntaxError("Invalid sort descriptor"))
      | Ok(ok) => Ok(ok)
      }
    },
    JsonCodec.field("column", JsonCodec.string),
    JsonCodec.field("direction", JsonCodec.string),
  )
}

module CatalogListSheetExportInput = {
  type t = {
    shopId: option<string>,
    variantsActive: option<bool>,
    variantsArchived: option<bool>,
    categoryId: option<Js.Nullable.t<string>>,
    supplierId: option<Js.Nullable.t<string>>,
    producer: option<string>,
    stock: option<CatalogStockRangeSelect.stockRange>,
  }

  let makeFromFilters = filters => {
    let {CatalogListFilters.shop: shop, status, category, supplier, producer, stock} = filters
    let shopId = shop->Option.map(({id}) => id)

    let variantsActive = switch status {
    | Some(Active) => Some(true)
    | Some(Inactive) => Some(false)
    | Some(Archived | Unarchived) | None => None
    }
    let variantsArchived = switch status {
    | Some(Archived) => Some(true)
    | Some(Unarchived | Inactive | Active) => Some(false)
    | None => None
    }
    let categoryId = category->Option.map(category => category.id)
    let supplierId = supplier->Option.map(supplier => supplier.id)

    {shopId, variantsActive, variantsArchived, categoryId, supplierId, producer, stock}
  }
}

let catalogListMultiShopsTableRowsFromQueryResult = queryResult =>
  queryResult.CatalogListMultiShopsQuery.variantsDistinctOnCku.edges->Array.map(edge => {
    CatalogListTableRow.cku: edge.node.cku,
    id: edge.node.id,
    createdAt: edge.node.createdAt,
    productId: edge.node.product.id,
    productKind: edge.node.product.kind,
    color: edge.node.product.color,
    producer: edge.node.product.producer,
    designation: edge.node.product.designation,
    family: edge.node.product.family,
    wineType: ?edge.node.product.wineType,
    whiteWineType: ?edge.node.product.whiteWineType,
    beerType: ?edge.node.product.beerType,
    name: edge.node.name,
    productName: edge.node.product.name,
    productTaxRate: edge.node.product.tax.value,
    region: edge.node.product.region,
    country: edge.node.product.country,
    supplierName: edge.node.supplier->Option.map(supplier => supplier.companyName),
    categoryName: edge.node.formattedCategory->Option.getWithDefault(nonApplicableStringLiteral),
    feesList: {transportAmount: 0., taxesAmount: 0., otherAmount: 0.},
    purchasePrice: edge.node.purchasedPrice->Option.getWithDefault(0.),
    purchasePriceDiscount: 0.,
    retailPrice: None,
    bulkUnit: switch (edge.node.capacityUnit, edge.node.bulk->Option.getWithDefault(false)) {
    | (Some(unit), true) => Some(unit)
    | _ => None
    },
    stockKeepingUnit: edge.node.stockKeepingUnit,
    priceLookUpCode: edge.node.priceLookUpCode,
    internalCode: edge.node.internalCode,
    stockQuantity: edge.node.stock.formattedQuantity,
    stockState: edge.node.stock.state,
    formattedShopsNames: edge.node.stock.formattedShopsNames,
    status: switch edge.node.formattedStatus {
    | #ACTIVE => Some(Active)
    | #INACTIVE => Some(Inactive)
    | #ARCHIVED => Some(Archived)
    | #VARIABLE => None
    },
    maxStockThreshold: edge.node.maxStockThreshold->Option.getWithDefault(0)->Float.fromInt,
    minStockThreshold: edge.node.minStockThreshold->Option.getWithDefault(0)->Float.fromInt,
    stockOrderTriggerThreshold: edge.node.stockOrderTriggerThreshold
    ->Option.getWithDefault(0)
    ->Float.fromInt,
    bulk: edge.node.bulk->Option.getWithDefault(false),
    alcoholVolume: edge.node.alcoholVolume,
  })

let catalogListSingleShopTableRowsFromQueryResult = (queryResult, ~currentPriceId) =>
  queryResult.CatalogListSingleShopQuery.variants.edges->Array.map(edge => {
    CatalogListTableRow.cku: edge.node.cku,
    id: edge.node.id,
    createdAt: edge.node.createdAt,
    productId: edge.node.product.id,
    productKind: edge.node.product.kind,
    color: edge.node.product.color,
    producer: edge.node.product.producer,
    designation: edge.node.product.designation,
    family: edge.node.product.family,
    wineType: ?edge.node.product.wineType,
    whiteWineType: ?edge.node.product.whiteWineType,
    beerType: ?edge.node.product.beerType,
    name: edge.node.name,
    productName: edge.node.product.name,
    productTaxRate: edge.node.product.tax.value,
    region: edge.node.product.region,
    country: edge.node.product.country,
    supplierName: edge.node.supplier->Option.map(supplier => supplier.companyName),
    categoryName: edge.node.formattedCategory->Option.getWithDefault(nonApplicableStringLiteral),
    feesList: {
      let latestOrderProduct = edge.node.orderProducts.edges[0]->Option.map(edge => edge.node)
      switch latestOrderProduct {
      | Some({fees: feesJson}) => CatalogListTableRow.decodeFeesJsonToFeesList(feesJson)
      | None => {transportAmount: 0., taxesAmount: 0., otherAmount: 0.}
      }
    },
    purchasePrice: edge.node.purchasedPrice->Option.getWithDefault(0.),
    purchasePriceDiscount: {
      let latestOrderProduct = edge.node.orderProducts.edges[0]->Option.map(edge => edge.node)
      switch latestOrderProduct {
      | Some({quantity, totalLocalDiscounts}) => totalLocalDiscounts /. Int.toFloat(quantity)
      | _ => 0.
      }
    },
    retailPrice: edge.node.variantPrices.edges
    ->Array.getBy(variantPrice =>
      switch (variantPrice.node.price, currentPriceId) {
      | (Some({id}), Some(currentPriceId)) => id === currentPriceId
      | _ => false
      }
    )
    ->Option.map(variantPrice => {
      CatalogListTableRow.valueIncludingTax: variantPrice.node.valueIncludingTax,
      valueExcludingTax: variantPrice.node.valueExcludingTax,
    }),
    bulkUnit: switch (edge.node.capacityUnit, edge.node.bulk->Option.getWithDefault(false)) {
    | (Some(unit), true) => Some(unit)
    | _ => None
    },
    stockKeepingUnit: edge.node.stockKeepingUnit,
    priceLookUpCode: edge.node.priceLookUpCode,
    internalCode: edge.node.internalCode,
    stockQuantity: edge.node.stock.formattedQuantity,
    stockState: edge.node.stock.state,
    formattedShopsNames: edge.node.stock.formattedShopsNames,
    status: switch edge.node.formattedStatus {
    | #ACTIVE => Some(Active)
    | #INACTIVE => Some(Inactive)
    | #ARCHIVED => Some(Archived)
    | #VARIABLE => None
    },
    maxStockThreshold: edge.node.maxStockThreshold->Option.getWithDefault(0)->Float.fromInt,
    minStockThreshold: edge.node.minStockThreshold->Option.getWithDefault(0)->Float.fromInt,
    stockOrderTriggerThreshold: edge.node.stockOrderTriggerThreshold
    ->Option.getWithDefault(0)
    ->Float.fromInt,
    bulk: edge.node.bulk->Option.getWithDefault(false),
    alcoholVolume: edge.node.alcoholVolume,
  })

let catalogListMultiShopsQueryVariablesFilterBy = ({CatalogListFilters.shop: shop, status}) => {
  CatalogListMultiShopsQuery.shopIds: switch shop {
  | Some({id: shopId}) =>
    Some(CatalogListMultiShopsQuery.makeInputObjectInFilter(~_in=[shopId], ()))
  | None => None
  },
  active: switch status {
  | Some(Active) =>
    Some(CatalogListMultiShopsQuery.makeInputObjectBooleanEqualsFilter(~_equals=true, ()))
  | Some(Inactive) =>
    Some(CatalogListMultiShopsQuery.makeInputObjectBooleanEqualsFilter(~_equals=false, ()))
  | Some(Archived | Unarchived) | None => None
  },
  archived: switch status {
  | Some(Archived) => Some(#ONLY)
  | Some(Active | Inactive | Unarchived) => Some(#EXCLUDED)
  | None => Some(#INCLUDED)
  },
}

let catalogListMultiShopsQueryVariablesOrderBy = sort => {
  let toRawDirection = direction =>
    switch direction {
    | #ascending => #ASC
    | #descending => #DESC
    }
  let emptyOrderBy = {
    CatalogListMultiShopsQuery.name: None,
    producer: None,
    purchasedPrice: None,
    createdAt: None,
    active: None,
  }
  switch sort {
  | CatalogListSorting.Name(direction) => {...emptyOrderBy, name: Some(toRawDirection(direction))}
  | Producer(direction) => {...emptyOrderBy, producer: Some(toRawDirection(direction))}
  | PurchasePrice(direction) => {...emptyOrderBy, purchasedPrice: Some(toRawDirection(direction))}
  | CreatedAt(direction) => {...emptyOrderBy, createdAt: Some(toRawDirection(direction))}
  }
}

let catalogListSingleShopQueryVariablesFilterBy = ({
  CatalogListFilters.shop: shop,
  status,
  category,
  supplier,
  producer,
  stock,
}) => {
  CatalogListSingleShopQuery.shopIds: switch shop {
  | Some({id: shopId}) =>
    Some(CatalogListSingleShopQuery.makeInputObjectInFilter(~_in=[shopId], ()))
  | None => None
  },
  active: switch status {
  | Some(Active) =>
    Some(CatalogListSingleShopQuery.makeInputObjectBooleanEqualsFilter(~_equals=true, ()))
  | Some(Inactive) =>
    Some(CatalogListSingleShopQuery.makeInputObjectBooleanEqualsFilter(~_equals=false, ()))
  | Some(Archived | Unarchived) | None => None
  },
  archived: switch status {
  | Some(Archived) => Some(#ONLY)
  | Some(Active | Inactive | Unarchived) => Some(#EXCLUDED)
  | None => Some(#INCLUDED)
  },
  categoryId: switch category {
  | Some(category) =>
    Some(
      CatalogListSingleShopQuery.makeInputObjectNullableStringEqualsFilter(
        ~_equals=switch category.id->Js.Nullable.toOption {
        | Some(categoryId) => categoryId
        | None => %raw(`null`)
        },
        (),
      ),
    )
  | None => None
  },
  supplierId: switch supplier {
  | Some(supplier) =>
    Some(
      CatalogListSingleShopQuery.makeInputObjectNullableStringEqualsFilter(
        ~_equals=switch supplier.id->Js.Nullable.toOption {
        | Some(supplierId) => supplierId
        | None => %raw(`null`)
        },
        (),
      ),
    )
  | None => None
  },
  producer: switch producer {
  | Some(producer) =>
    Some(CatalogListSingleShopQuery.makeInputObjectStringEqualsFilter(~_equals=producer, ()))
  | None => None
  },
  stock: switch stock {
  | Some({?min, ?max}) =>
    Some(CatalogListSingleShopQuery.makeInputObjectNumberRangeFilter(~_min=?min, ~_max=?max, ()))
  | None => None
  },
  stockKeepingUnit: None,
  ean13: None,
  createdAt: None,
  updatedAt: None,
}

let catalogListSingleShopQueryVariablesOrderBy = sort => {
  let toRawDirection = direction =>
    switch direction {
    | #ascending => #ASC
    | #descending => #DESC
    }
  let emptyOrderBy = {
    CatalogListSingleShopQuery.name: None,
    producer: None,
    purchasedPrice: None,
    createdAt: None,
    active: None,
  }
  switch sort {
  | CatalogListSorting.Name(direction) => {...emptyOrderBy, name: Some(toRawDirection(direction))}
  | Producer(direction) => {...emptyOrderBy, producer: Some(toRawDirection(direction))}
  | PurchasePrice(direction) => {...emptyOrderBy, purchasedPrice: Some(toRawDirection(direction))}
  | CreatedAt(direction) => {...emptyOrderBy, createdAt: Some(toRawDirection(direction))}
  }
}

let catalogListMultiShopsQueryVariables = ({
  ResourceList.connectionArguments: {?first, ?last, ?before, ?after},
  ?searchQuery,
  filters,
  sorting,
}) => {
  CatalogListMultiShopsQuery.first,
  last,
  before,
  after,
  search: searchQuery,
  filterBy: Some(catalogListMultiShopsQueryVariablesFilterBy(filters)),
  orderBy: Some([catalogListMultiShopsQueryVariablesOrderBy(sorting)]),
}

let catalogListSingleShopQueryVariables = ({
  ResourceList.connectionArguments: {?first, ?last, ?before, ?after},
  ?searchQuery,
  filters,
  sorting,
}) => {
  CatalogListSingleShopQuery.first,
  last,
  before,
  after,
  search: searchQuery,
  filterBy: Some(catalogListSingleShopQueryVariablesFilterBy(filters)),
  orderBy: Some([catalogListSingleShopQueryVariablesOrderBy(sorting)]),
}

@react.component
let make = (~shopsIndependentKind, ~variantIdfromLabelEditSettings) => {
  let shops = Auth.useShops()
  let authScope = Auth.useScope()
  let activeShop = Auth.useActiveShop()

  let statuses = CatalogListFilters.getStatuses(~shopsIndependentKind)
  let organisationAccount = switch Auth.useScope() {
  | Organisation(_) => true
  | Single(_) => false
  }

  let initialFilters = {
    CatalogListFilters.shop: activeShop,
    status: statuses[0],
    category: None,
    supplier: None,
    producer: None,
    stock: None,
  }
  let initialSorting = CatalogListSorting.Name(#ascending)
  let initialState = ResourceList.initialState(
    ~first=ResourceList.defaultEdgesPerPage,
    ~filters=initialFilters,
    ~sorting=initialSorting,
    (),
  )
  let filtersJsonCodec = CatalogListFilters.jsonCodec(~shops)
  let sortingJsonCodec = CatalogListSorting.jsonCodec
  let extraJsonCodec = CatalogListExtraParams.jsonCodec
  let resourceListPropState = ResourceList.use(
    ~initialState,
    ~filtersJsonCodec,
    ~sortingJsonCodec,
    ~extraJsonCodec,
    (),
  )
  let (state, dispatch) = resourceListPropState

  let (printerStatusResult, setPrinterStatusResult) = React.useState(_ => None)
  let (_notification, setNotification) = React.useState(() => None)
  let (pricingSettings, setPricingSettings) = React.useState(() =>
    CatalogListPricingSettings.initialValue
  )

  let queryMultiShopsAsyncResult =
    CatalogListMultiShopsQuery.use(
      catalogListMultiShopsQueryVariables(state),
      ~skip=state.filters.shop->Option.isSome,
      ~fetchPolicy=CacheAndNetwork,
    )->ApolloHelpers.queryResultToAsyncResult

  let querySingleShopAsyncResult =
    CatalogListSingleShopQuery.use(
      catalogListSingleShopQueryVariables(state),
      ~skip=state.filters.shop->Option.isNone,
      ~fetchPolicy=CacheAndNetwork,
    )->ApolloHelpers.queryResultToAsyncResult

  let pricelistsAsyncResult =
    CatalogListPricelist.SingleShopPricesQuery.use(
      CatalogListPricelist.singleShopQueryVariables(
        ~shopIds=state.filters.shop->Option.mapWithDefault([], shop => [shop.id]),
      ),
      ~skip=state.filters.shop->Option.isNone,
    )
    ->ApolloHelpers.queryResultToAsyncResult
    ->AsyncResult.mapOk(CatalogListPricelist.singleShopQueryResult)
    ->AsyncResult.mapError(_ => ())

  let currentPricelist = switch pricelistsAsyncResult {
  | Done(Ok(pricelists)) =>
    let currentPriceId = state.extra->Option.map(extra => extra.price.id)
    CatalogListPricelist.getCurrentPricelist(pricelists, ~currentPriceId)
  | _ => None
  }

  let onRequestPaginate = React.useCallback3(action =>
    switch (queryMultiShopsAsyncResult, querySingleShopAsyncResult) {
    | (Reloading(Ok({variantsDistinctOnCku: {totalCount, pageInfo: {startCursor, endCursor}}})), _)
    | (_, Reloading(Ok({variants: {totalCount, pageInfo: {startCursor, endCursor}}})))
    | (Done(Ok({variantsDistinctOnCku: {totalCount, pageInfo: {startCursor, endCursor}}})), _)
    | (_, Done(Ok({variants: {totalCount, pageInfo: {startCursor, endCursor}}}))) =>
      let totalPages = ResourceList.totalPages(totalCount, ResourceList.defaultEdgesPerPage)
      let nextPage = ResourceList.nextPage(~state, ~action, ~totalPages)
      let cursors = (startCursor, endCursor)
      switch nextPage {
      | Some(nextPage) => dispatch(ResourceList.Navigated({nextPage, totalCount, cursors}))
      | None => ()
      }
    | (NotAsked, _) | (Loading, _) | (Reloading(Error(_)), _) | (Done(Error(_)), _) => ()
    }
  , (state.currentPage, queryMultiShopsAsyncResult, querySingleShopAsyncResult))

  let onTableSortChange = sortDescriptor =>
    switch CatalogListSorting.fromSortDescriptor(sortDescriptor) {
    | Ok(sort) => SortingUpdated(sort)->dispatch
    | Error() => ()
    }

  React.useEffect0(() => {
    if state.filters.shop !== activeShop {
      dispatch(FiltersUpdated(prev => {...prev, shop: activeShop}))
    }
    None
  })

  React.useEffect1(() => {
    switch state.filters.shop {
    | Some({id: shopId}) =>
      let future = CatalogLabel.Print.DefaultPrinterRequest.make(~shopId)
      future->Future.map(result => setPrinterStatusResult(_ => Some(result)))->ignore
      Some(() => future->Future.cancel)
    | None => None
    }
  }, [state.filters.shop])

  ReactUpdateEffect.use1(() => {
    dispatch(FiltersUpdated(prev => {...prev, category: None, supplier: None}))
    None
  }, [state.filters.shop])

  ReactUpdateEffect.use1(() => {
    dispatch(
      FiltersUpdated(
        prev => {
          let currentStatus = prev.status
          let status = CatalogListFilters.getUpdatedStatus(~statuses, ~currentStatus)
          {...prev, status}
        },
      ),
    )
    None
  }, [shopsIndependentKind])

  let tableColumns = [
    {
      Table.key: "reference",
      name: t("Name and description"),
      allowsSorting: true,
      layout: {minWidth: 405.->#px, width: 35.->#pct, margin: #xxlarge, sticky: true},
      render: ({data}) => {
        let redirectRoute = CatalogRoutes.variantRoute(~cku=data.CatalogListTableRow.cku)
        let productKind = data.CatalogListTableRow.productKind
        let information = {
          CatalogProduct.Information.productName: data.productName,
          variantName: data.name,
          sku: ?data.stockKeepingUnit,
          plu: ?data.priceLookUpCode->Option.map(Int.toString),
          color: ?data.color,
          producerName: ?data.producer,
          designation: ?data.designation,
          productFamily: ?data.family,
          region: ?data.region,
          country: data.country->Option.getWithDefault(nonApplicableStringLiteral),
          categoryName: data.categoryName,
          supplierName: ?data.supplierName,
          alcoholVolume: ?data.alcoholVolume->Option.map(alcoholVolume =>
            alcoholVolume->Intl.decimalFormat(~maximumFractionDigits=1) ++ "°"
          ),
        }
        <ProductReferenceTableCell
          redirectRoute productKind information hideProducerAndSupplier=true
        />
      },
    },
    {
      key: "producer",
      name: t("Producer/Supplier"),
      allowsSorting: true,
      layout: {minWidth: 120.->#px, width: 20.->#pct, margin: #medium},
      render: ({data: {producer, supplierName}}) => {
        let formattedProducer = producer->Option.getWithDefault(nonApplicableStringLiteral)
        <div
          style={ReactDOMStyle.make(~display="flex", ~width="100%", ~flexDirection="column", ())}>
          <TextStyle size=#xsmall maxLines=1> {formattedProducer->React.string} </TextStyle>
          {switch supplierName {
          | Some(supplierName) =>
            <TextStyle size=#tiny maxLines=1 variation=#normal lineHeight=#normal>
              {supplierName->React.string}
            </TextStyle>
          | None =>
            <TextStyle size=#tiny maxLines=1 variation=#subdued lineHeight=#normal>
              {t("Not specified")->React.string}
            </TextStyle>
          }}
        </div>
      },
    },
    {
      key: "category",
      name: t("Category"),
      layout: {minWidth: 140.->#px, width: 16.->#pct, margin: #medium},
      render: ({data: {categoryName, productId}}) => <>
        // TODO - backend: to be reworked (should be an union?)
        {switch (categoryName, authScope) {
        | ("VARIABLE", _) =>
          <TextStyle variation=#subdued size=#xsmall> {t("Differing")->React.string} </TextStyle>
        | ("Non classé", _) =>
          <TextStyle variation=#subdued size=#xsmall>
            {t("Not classified")->React.string}
          </TextStyle>
        | (_, Single(_)) | (_, Organisation({activeShop: Some(_)})) =>
          <CatalogCategoryEditCell compact=true value=categoryName productId />
        | _ =>
          <div style={ReactDOMStyle.make(~display="flex", ~width="100%", ())}>
            <TextStyle size=#xsmall maxLines=1 direction=#rtl>
              {categoryName->React.string}
            </TextStyle>
          </div>
        }}
      </>,
    },
    {
      key: "stock",
      name: t("Stock"),
      layout: {
        minWidth: 85.->#px,
        width: 175.->#px,
        margin: #small,
      },
      render: ({
        data: {
          stockQuantity,
          stockState,
          formattedShopsNames,
          id,
          maxStockThreshold,
          minStockThreshold,
          stockOrderTriggerThreshold,
          bulk,
        },
      }) =>
        <CatalogVariantStockThresholdCell
          compact=true
          stockQuantity
          stockState
          formattedShopsNames
          id
          maxStockThreshold
          minStockThreshold
          stockOrderTriggerThreshold
          bulk
        />,
    },
    {
      key: "purchase-price",
      name: switch (
        pricingSettings.purchasePriceType,
        pricingSettings.retailPriceCalculatingMethod,
      ) {
      | (Gross, PurchasePrice) => t("Gross purchase price")
      | (Net, PurchasePrice) => t("Net purchase price")
      | (Gross, FullPurchasePrice) => t("Gross full PP")
      | (Net, FullPurchasePrice) => t("Net full PP")
      | (Gross, AcquisitionCost) => t("Gross acq. cost")
      | (Net, AcquisitionCost) => t("Net acq. cost")
      },
      allowsSorting: true,
      layout: {minWidth: 125.->#px, width: 170.->#px},
      render: ({data: {id, purchasePrice, purchasePriceDiscount, bulkUnit, feesList}}) =>
        if activeShop->Option.isSome {
          <CatalogListPurchasePriceCell
            variantId=id
            bulkUnit
            feesList
            discount=purchasePriceDiscount
            purchasePriceType=pricingSettings.purchasePriceType
            calculatingMethod=pricingSettings.retailPriceCalculatingMethod
            value=purchasePrice
          />
        } else {
          nonApplicableValueCellElement
        },
    },
    {
      key: "price-differential-indicator",
      name: pricingSettings.priceDifferentialIndicator->CatalogListPricingSettings.PriceDifferentialIndicator.toShortLabel,
      layout: {minWidth: 82.->#px, width: 160.->#px},
      render: ({
        data: {
          purchasePrice,
          purchasePriceDiscount: discount,
          feesList: {transportAmount, taxesAmount, otherAmount},
          retailPrice,
          productTaxRate,
        },
      }) =>
        if activeShop->Option.isSome {
          switch (retailPrice, currentPricelist) {
          | (Some({valueExcludingTax}), Some({taxIncluded})) =>
            let computedPurchasePrice = {
              let netOrGrossPurchasePrice = switch pricingSettings.purchasePriceType {
              | Net => CatalogListTableRow.computeNetPurchasePrice(purchasePrice, ~discount)
              | Gross => purchasePrice
              }
              switch pricingSettings.retailPriceCalculatingMethod {
              | PurchasePrice => netOrGrossPurchasePrice
              | FullPurchasePrice =>
                CatalogListTableRow.computeFullPurchasePrice(
                  ~purchasePrice=netOrGrossPurchasePrice,
                  ~taxesFeeAmount=taxesAmount,
                  ~otherFeeAmount=otherAmount,
                )
              | AcquisitionCost =>
                CatalogListTableRow.computeAcquisitionCost(
                  ~purchasePrice=netOrGrossPurchasePrice,
                  ~transportFeeAmount=transportAmount,
                  ~taxesFeeAmount=taxesAmount,
                  ~otherFeeAmount=otherAmount,
                )
              }
            }
            let totalChargesAmount = switch pricingSettings.retailPriceCalculatingMethod {
            | PurchasePrice =>
              PriceCalculator.sumManyCharges([transportAmount, taxesAmount, otherAmount])
            | FullPurchasePrice => transportAmount
            | AcquisitionCost => 0.
            }
            let taxRate = taxIncluded ? productTaxRate : 0.

            <CatalogListPriceDifferentialIndicatorCell
              rateType=pricingSettings.priceDifferentialIndicator
              purchasePrice=computedPurchasePrice
              retailPriceExcludingTax=valueExcludingTax
              totalChargesAmount
              taxRate
            />
          | _ => <TextStyle variation=#subdued size=#xsmall> {t("N/C")->React.string} </TextStyle>
          }
        } else {
          nonApplicableValueCellElement
        },
    },
    {
      key: "retail-price",
      name: switch state.extra {
      | Some({price: {name}}) => name
      | None => t("Retail price")
      },
      layout: {
        minWidth: activeShop->Option.isSome ? 110.->#px : 80.->#px,
        width: 170.->#px,
        headerActionComponent: switch state.filters.shop {
        | Some({id: shopId}) =>
          <CatalogListTableHeaderActionPricelistSelect
            shopId
            pricelistsAsyncResult
            price={state.extra->Option.map(extra => extra.price)}
            onRequestPriceChange={value => dispatch(ExtraUpdated({price: value}))}
          />
        | None => React.null
        },
      },
      render: ({
        data: {
          id,
          feesList: {transportAmount, taxesAmount, otherAmount} as feesList,
          purchasePrice,
          purchasePriceDiscount: discount,
          retailPrice,
          bulkUnit,
          productTaxRate,
        },
      }) => {
        switch (activeShop, currentPricelist) {
        | (Some({id: shopId}), Some(pricelist)) =>
          let computedPurchasePrice = {
            let netOrGrossPurchasePrice = switch pricingSettings.purchasePriceType {
            | Net => CatalogListTableRow.computeNetPurchasePrice(purchasePrice, ~discount)
            | Gross => purchasePrice
            }
            switch pricingSettings.retailPriceCalculatingMethod {
            | PurchasePrice => netOrGrossPurchasePrice
            | FullPurchasePrice =>
              CatalogListTableRow.computeFullPurchasePrice(
                ~purchasePrice=netOrGrossPurchasePrice,
                ~taxesFeeAmount=taxesAmount,
                ~otherFeeAmount=otherAmount,
              )
            | AcquisitionCost =>
              CatalogListTableRow.computeAcquisitionCost(
                ~purchasePrice=netOrGrossPurchasePrice,
                ~transportFeeAmount=transportAmount,
                ~taxesFeeAmount=taxesAmount,
                ~otherFeeAmount=otherAmount,
              )
            }
          }
          <CatalogListRetailPriceCell
            variantId=id
            shopId
            pricelist
            bulkUnit
            taxRate=productTaxRate
            feesList
            calculatingMethod=pricingSettings.retailPriceCalculatingMethod
            rateType=pricingSettings.priceDifferentialIndicator
            purchasePrice=computedPurchasePrice
            value=retailPrice
          />
        | (None, _) => nonApplicableValueCellElement
        | (_, None) => <Spinner size=18. />
        }
      },
    },
    {
      key: "created-at",
      name: t("Creation date"),
      allowsSorting: true,
      layout: {minWidth: 80.->#px, width: 140.->#px},
      render: ({data: {createdAt}}) => {
        let formattedDatetime =
          createdAt->Intl.dateTimeFormat(~year=#"2-digit", ~month=#"2-digit", ~day=#"2-digit")
        let formattedTime = switch (
          Intl.locale,
          createdAt->Intl.dateTimeFormat(~hour=#numeric, ~minute=#numeric),
        ) {
        | (#"fr-FR", time) => time->Js.String2.replace(":", "h")
        | (#"en-EN", time) => time
        }
        <div
          style={ReactDOMStyle.make(
            ~display="flex",
            ~width="100%",
            ~flexDirection="column",
            ~columnGap="2px",
            (),
          )}>
          <TextStyle size=#xxsmall maxLines=1> {formattedDatetime->React.string} </TextStyle>
          <TextStyle size=#tiny maxLines=1 variation=#normal lineHeight=#normal>
            {formattedTime->React.string}
          </TextStyle>
        </div>
      },
    },
    {
      key: "status",
      name: t("Status"),
      layout: {width: 110.->#px},
      render: ({data: {status}}) =>
        switch status {
        | Some(status) => <CatalogProductStatusBadge size=#medium value=Some(status) />
        | None =>
          <TextStyle variation=#subdued size=#xsmall> {t("Differing")->React.string} </TextStyle>
        },
    },
    {
      key: "quickprint-action",
      layout: {
        hidden: state.filters.shop->Option.isNone ||
          printerStatusResult->Option.mapWithDefault(true, result => result->Result.isError),
        minWidth: 40.->#px,
        width: 40.->#px,
        alignX: #flexEnd,
      },
      render: ({data: {id}}) =>
        switch state.filters.shop {
        | Some({id: shopId}) =>
          <CatalogLabelQuickPrintButton
            featureLocked=false
            fromEditRedirection={variantIdfromLabelEditSettings === Some(id)}
            variation=#vertical
            variantId=id
            shopId
            requestBarcodeCompletion=CatalogLabel.BarcodeCompletionRequest.make
            requestLabelsPrinting=CatalogLabel.Print.LabelsRequest.make
            onRequestNotification={value => setNotification(_ => Some(value))}
          />
        | None => React.null
        },
    },
    {
      key: "actions",
      layout: {
        minWidth: 40.->#px,
        width: 40.->#px,
        alignX: #flexEnd,
      },
      render: ({data: {cku, id, status}}) => <CatalogTableActions cku id ?status />,
    },
  ]

  let shopFilter = if organisationAccount {
    <Auth.SelectShopFilter
      variation=#secondary
      value=?state.filters.shop
      hideLabel=true
      truncateItemLabel=true
      onChange={shop => dispatch(FiltersUpdated(prev => {...prev, shop}))}
    />
  } else {
    React.null
  }

  let filters = [
    <Select
      label={t("Status")}
      preset=#filter
      size=#compact
      highlighted={shopsIndependentKind
        ? state.filters.status !== Some(Unarchived)
        : state.filters.status !== Some(Active)}
      sections={
        open Select
        let statuses = CatalogListFilters.getStatuses(~shopsIndependentKind)
        let defaultItem = {
          key: "default",
          label: t("All"),
          value: None,
        }
        let items = statuses->Array.map(value => {
          label: value->CatalogProduct.Status.toLabel,
          key: value->CatalogProduct.Status.toString,
          value: Some(value),
        })
        [{title: t("Statuses"), items: Array.concat(items, [defaultItem])}]
      }
      value=state.filters.status
      onChange={status => dispatch(FiltersUpdated(prev => {...prev, status}))}
    />,
    <CatalogCategorySelectFilter
      shopId={activeShop->Option.map(shop => shop.id)}
      value={state.filters.category}
      onChange={category => dispatch(FiltersUpdated(prev => {...prev, category}))}
    />,
    <SupplierSelect
      label={t("Supplier")}
      preset=#filter
      size=#compact
      showDefaultItem=true
      hideOverlayFooter=true
      shopId={activeShop->Option.map(shop => shop.id)}
      value={state.filters.supplier}
      onChange={supplier => dispatch(FiltersUpdated(prev => {...prev, supplier}))}
    />,
    <CatalogProducerSelect
      shopId={activeShop->Option.map(shop => shop.id)}
      value={state.filters.producer}
      onChange={producer => dispatch(FiltersUpdated(prev => {...prev, producer}))}
    />,
    <CatalogStockRangeSelect
      disabled={activeShop->Option.isNone}
      value={state.filters.stock}
      onChange={stock => dispatch(FiltersUpdated(prev => {...prev, stock}))}
    />,
  ]

  // NOTE - must update manually this array with existing filters
  let activeFiltersCount = {
    let {status, category, supplier, producer, stock} = state.filters
    [
      if shopsIndependentKind {
        switch status {
        | Some(Archived) | None => true
        | Some(Unarchived) | Some(_) => false
        }
      } else {
        switch status {
        | Some(Inactive | Archived) | None => true
        | Some(Active) | Some(_) => false
        }
      },
      category->Option.isSome,
      supplier->Option.isSome,
      producer->Option.isSome,
      stock->Option.isSome,
    ]
    ->Array.keep(active => active)
    ->Array.length
  }

  let handleFilterReset =
    activeFiltersCount > 0 ? Some(() => dispatch(FiltersUpdated(_ => initialState.filters))) : None

  let mainAction =
    <CatalogListSettingsPopoverButton onChange={value => setPricingSettings(_ => value)} />

  let actions = {
    let {
      shopId,
      variantsActive,
      variantsArchived,
      categoryId,
      supplierId,
      producer,
      stock,
    } = CatalogListSheetExportInput.makeFromFilters(state.filters)

    let shopIds = switch (shopId, Auth.useShops()) {
    | (_, []) => None
    | (Some(shopId), _) => Some([shopId])
    | (None, shops) => Some(shops->Array.map(shop => shop.id))
    }

    let makeMenuItemAction = (~productKind) => MenuItem.OpenLink(
      RouteWithQueryString(
        Catalog->LegacyRouter.routeToPathname ++ "/product/create",
        CatalogProductEditUrlQueryString.CreateProduct.encode({productKind: productKind}),
      ),
    )

    let onRequestErrorNotification = error => setNotification(_ => Some(Error(error)))

    <Inline space=#small>
      {switch shopIds {
      | Some(shopIds) =>
        <CatalogListInventoryExportShortIconButton
          tooltip={activeFiltersCount === 0
            ? t("Export inventory")
            : t("Export filtered inventory")}
          activeFilters={activeFiltersCount > 0}
          shopIds
          ?variantsActive
          ?variantsArchived
          ?categoryId
          ?supplierId
          ?producer
          ?stock
          onRequestErrorNotification
        />
      | None => React.null
      }}
      <ShortIconButton
        name=#printing_label
        tooltipText={t("Generate labels")}
        action=OpenLink(Route(Catalog->LegacyRouter.routeToPathname ++ "/labels/create"))
      />
      <Menu variation=#more_square disabled={shopIds->Option.isNone} overlayPriority=false>
        {<>
          <MenuItem
            size=#normal
            content=Text(t("Import inventory"))
            action=OpenLink(Route(Catalog->LegacyRouter.routeToPathname ++ "/inventory/import"))
          />
          <CatalogListPriceLookUpExportMenuItem
            text={t("Export PLU codes")}
            shopId
            ?variantsActive
            ?variantsArchived
            onRequestErrorNotification
          />
          {if organisationAccount {
            <CatalogListCentralizeRequestMenuItem
              text={t("Centralize catalogs")} onRequestErrorNotification
            />
          } else {
            React.null
          }}
        </>}
      </Menu>
      <Separator size=#large />
      <Menu buttonText={t("New reference")} buttonVariation=#primary buttonSize=#small>
        <MenuItem
          content=Text(#WINE->CatalogProduct.Kind.toLabel(~titleCase=true))
          action={makeMenuItemAction(~productKind=#WINE)}
        />
        <MenuItem
          content=Text(#SPIRITUOUS->CatalogProduct.Kind.toLabel(~titleCase=true))
          action={makeMenuItemAction(~productKind=#SPIRITUOUS)}
        />
        <MenuItem
          content=Text(#BEER->CatalogProduct.Kind.toLabel(~titleCase=true))
          action={makeMenuItemAction(~productKind=#BEER)}
        />
        <MenuItem
          content=Text(#SIMPLE->CatalogProduct.Kind.toLabel(~titleCase=true))
          action={makeMenuItemAction(~productKind=#SIMPLE)}
        />
      </Menu>
    </Inline>
  }

  let searchBar =
    <ResourceListPage.ResourceListSearchBar
      placeholder={t("Search") ++ "..."}
      value=?state.searchQuery
      onChange={searchQuery => dispatch(Searched(searchQuery))}
    />

  let tableEmptyState = switch state {
  | {
      currentPage: 1,
      searchQuery: "",
      filters: {shop, status: None, category: None, supplier: None, producer: None},
    } if shop === activeShop =>
    <EmptyState illustration=Illustration.create title={t("Welcome to the catalog space.")} />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => dispatch(Reset(initialState))}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  let tableKeyExtractor = CatalogListTableRow.keyExtractor

  let paginationCurrentPage = state.currentPage
  let (paginationTotalCount, paginationTotalPages) = switch (
    queryMultiShopsAsyncResult,
    querySingleShopAsyncResult,
  ) {
  | (Done(Ok({variantsDistinctOnCku: {totalCount}})), _)
  | (_, Done(Ok({variants: {totalCount}})))
  | (Reloading(Ok({variantsDistinctOnCku: {totalCount}})), _)
  | (_, Reloading(Ok({variants: {totalCount}}))) => (
      totalCount,
      ResourceList.totalPages(totalCount, ResourceList.defaultEdgesPerPage),
    )
  | _ => (0, 1)
  }
  let paginationLoading =
    AsyncResult.isReloading(queryMultiShopsAsyncResult) ||
    AsyncResult.isReloading(querySingleShopAsyncResult)
  let paginationLastDirection = switch state.connectionArguments {
  | {last: _} => ResourceListPagination.Backward
  | _ => Forward
  }

  let tableData = if state.filters.shop->Option.isNone {
    queryMultiShopsAsyncResult
    ->AsyncResult.mapOk(catalogListMultiShopsTableRowsFromQueryResult)
    ->AsyncResult.mapError(_ => ())
  } else {
    querySingleShopAsyncResult
    ->AsyncResult.mapOk(result =>
      result->catalogListSingleShopTableRowsFromQueryResult(
        ~currentPriceId=state.extra->Option.map(extra => extra.price.id),
      )
    )
    ->AsyncResult.mapError(_ => ())
  }

  <ResourceListPage
    title={t("Catalog")}
    shopFilter
    filters
    activeFiltersCount
    mainAction
    searchBar
    actions
    tableColumns
    tableData
    tableKeyExtractor
    tableEmptyState
    paginationCurrentPage
    paginationTotalCount
    paginationTotalPages
    paginationLoading
    paginationLastDirection
    tableSortDescriptor={CatalogListSorting.toSortDescriptor(state.sorting)}
    onTableSortChange
    onRequestPaginate
    onRequestResetFilters=?handleFilterReset
  />
}

let make = React.memo(make)

open Intl

module Config = CatalogProduct__Config

module AuthSelectShop = {
  @react.component
  let make = (~productShopIds) => {
    let shops = Auth.useShops()

    let disabledIds = shops->Array.keepMap(({id}) =>
      switch productShopIds->Array.getBy(current => current === id) {
      | Some(_) => None
      | _ => Some(id)
      }
    )

    <Auth.SelectShopFilter disabledIds />
  }
}

@react.component
let make = (
  ~cku,
  ~productShopIds,
  ~shopsProduct: array<Config.productInformation>,
  ~shopsProductVariants: array<array<Config.variant>>,
) => {
  let activeShop = Auth.useActiveShop()
  let notifier = Notifier.use()
  let scope = Auth.useScope()
  let someProduct = shopsProduct->Array.length > 0

  let renderActions = () =>
    switch (scope, shopsProduct[0], shopsProductVariants[0]) {
    | (Single(_), Some({id, status}), Some(variants)) =>
      let allVariantsArchived =
        {variants->Array.every(variant => variant.status === Some(Archived))}
      <CatalogProductPageActions cku id status allVariantsArchived />
    | _ => React.null
    }

  let renderHeaderActions = switch scope {
  | Organisation({shops}) =>
    Some(
      () =>
        <Inline align=#spaceBetween grow=true>
          <Inline space=#normal>
            {if shops->Array.length < 6 {
              <AuthSelectShop productShopIds />
            } else {
              React.null
            }}
            {switch someProduct {
            | false =>
              <TextStyle variation=#negative>
                {t("No product association to this shop has been recorded.")->React.string}
              </TextStyle>
            | _ => React.null
            }}
          </Inline>
          {switch (shopsProduct[0], shopsProductVariants[0]) {
          | (Some({id, status}), Some(variants)) =>
            let allVariantsArchived =
              {variants->Array.every(variant => variant.status === Some(Archived))}
            <CatalogProductPageActions cku id status allVariantsArchived />
          | _ => React.null
          }}
        </Inline>,
    )
  | Single(_) => None
  }

  let pageTitle = shopsProduct[0]->Option.mapWithDefault("", ({name}) => name)
  let renderTitleEnd = () =>
    switch (shopsProduct[0], scope) {
    | (Some(product), Organisation({activeShop: Some(_)}))
    | (Some(product), Single(_)) =>
      <CatalogProductStatusBadge value=product.status />
    | _ => React.null
    }

  <Page title=pageTitle renderTitleEnd renderActions ?renderHeaderActions>
    {if someProduct {
      <>
        <Notifier.Banner notifier />
        <Box spaceTop=#medium>
          <Stack space=#medium>
            <Columns space=#large>
              <Column width=#one_third>
                <Stack space=#large>
                  <CatalogProductInformationCard product={shopsProduct->Array.getExn(0)} />
                  <CatalogProductOrganisationTableCard shopsProduct />
                  {switch scope {
                  | Organisation({activeShop: None}) =>
                    <CatalogProductStatusTableCard shopsProduct />
                  | _ => React.null
                  }}
                  <CatalogProductNoteCard
                    productId={shopsProduct
                    ->Array.getBy(product =>
                      Some(product.shopId) === activeShop->Option.map(({id}) => id)
                    )
                    ->Option.map(product => product.id)}
                    internalNote={shopsProduct
                    ->Array.getBy(product =>
                      Some(product.shopId) === activeShop->Option.map(({id}) => id)
                    )
                    ->Option.mapWithDefault("", product => product.internalNote)}
                  />
                </Stack>
              </Column>
              <Column width=#three_quarter>
                <CatalogProductVariantTableCard shopsProductVariants />
              </Column>
            </Columns>
          </Stack>
        </Box>
      </>
    } else {
      React.null
    }}
  </Page>
}

let make = React.memo(make)

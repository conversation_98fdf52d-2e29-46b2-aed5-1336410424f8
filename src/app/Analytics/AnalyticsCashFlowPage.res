open Intl

let nonApplicableStringLiteral = "—"
let dayHoursShifted = 5.

module DailyCashFlowsQuery = %graphql(`
  query DailyCashFlows($shopId: String!, $date: Datetime!) {
    dailyShopCashFlows(shopId: $shopId, date: $date) {
      id
      createdAt
      amount
      kind @ppxOmitFutureValue
      reason
      staff { id name }
      device { id name slug }
    }
  }
`)

module DailyPaymentsQuery = %graphql(`
  query DailyPayments($shopId: String!, $date: Datetime!) {
    dailyShopPayments(shopId: $shopId, date: $date) @ppxOmitFutureValue {
      ...on InvoicePayment {
        invoiceId: id
        invoiceCreatedAt: date
        invoiceMethod: method @ppxOmitFutureValue
        invoiceAmountReceived: amountReceived
        invoiceAmountReturned: amountReturned
        device { id name slug }
        invoice {
          id
          name
          customerName
          staff { id name }
          salesReceipt { id name createdAt }
        }
      }
      ...on SalesReceiptPayment {
        salesReceiptId: id
        salesReceiptCreatedAt: createdAt
        salesReceiptMethod: method @ppxOmitFutureValue
        salesReceiptAmountReceived: amountReceived
        salesReceiptAmountReturned: amountReturned
        salesReceipt {
          id
          name
          customer { id lastName firstName companyName }
          staff { id name }
          device { id name slug }
          invoice { id name createdAt }
        }
      }
      ...on RefundReceiptPayment {
        refundReceiptId: id
        refundReceiptCreatedAt: createdAt
        refundReceiptMethod: method @ppxOmitFutureValue
        refundReceiptAmountReceived: amountReceived
        refundReceiptAmountReturned: amountReturned
        refundReceipt {
          id
          name
          customer { id lastName firstName companyName }
          staff { id name }
          device { id name slug }
        }
      }
    }
  }
`)

module PaymentMethodBadge = {
  @react.component
  let make = React.memo((~value, ~shortened=false, ~bordered=false) => {
    let colorSet = PaymentMethod.ColorSet.make(value)
    let paymentMethodLabel = shortened
      ? PaymentMethod.toShortLabel(value)
      : PaymentMethod.toLabel(value)
    let borderColor = bordered ? Some(colorSet.borderBadgeAndBorderSvg) : None
    <Badge
      backgroundColor=colorSet.backgroundBadge
      foregroundColor=colorSet.badgeTextAndFillSvg
      ?borderColor>
      {paymentMethodLabel->React.string}
    </Badge>
  })
}

module FlowType = {
  type t =
    | Receipt
    | ReceiptTransformed({
        paymentId: string,
        transformedInvoiceName: string,
        transformedInvoiceCreatedAt: Js.Date.t,
      })
    | Invoice
    | InvoiceTransformed({
        initialSalesReceiptId: string,
        initialSalesReceiptName: string,
        initialSalesReceiptCreatedAt: Js.Date.t,
      })
    | Cashflow

  let toLabel = value =>
    switch value {
    | Receipt | ReceiptTransformed(_) => t("Receipts & refunds")
    | Invoice | InvoiceTransformed(_) => t("Invoices & credit notes")
    | Cashflow => t("Cash activities")
    }

  let values = [Receipt, Invoice, Cashflow]
}

module TableRow = {
  type name = {
    value: string,
    count?: (int, int),
  }
  type device = {
    id: string,
    label: string,
  }
  type t = {
    id: string,
    flowType: FlowType.t,
    name: name,
    createdAt: Js.Date.t,
    amount: float,
    paymentMethod: PaymentMethod.t,
    customerNameOrReason: string,
    staffName: string,
    device: device,
  }

  let cashFlowsRowsFromQueryData = data => {
    data.DailyCashFlowsQuery.dailyShopCashFlows->Array.map(data => {
      id: data.id,
      flowType: Cashflow,
      name: switch data.kind {
      | #INFLOW => {value: t("Inflow")}
      | #OUTFLOW => {value: t("Outflow")}
      },
      createdAt: data.createdAt,
      amount: data.kind === #OUTFLOW ? -.data.amount : data.amount,
      paymentMethod: Cash,
      customerNameOrReason: data.reason,
      staffName: data.staff->Option.mapWithDefault(nonApplicableStringLiteral, ({name}) => name),
      device: {
        id: data.device.id,
        label: DeviceName.decode(~slug=data.device.slug, ~name=data.device.name)->DeviceName.format,
      },
    })
  }

  let safeSubstract = (a, b) => Big.minus(Big.fromFloat(a), Big.fromFloat(b))->Big.toFloat

  let customerName = (~firstName, ~lastName, ~companyName) =>
    switch (firstName, lastName, companyName) {
    | (_, _, Some(companyName)) => companyName
    | (Some(firstName), Some(lastName), _) => firstName ++ " " ++ lastName->Js.String2.toUpperCase
    | (_, Some(lastName), _) => lastName->Js.String2.toUpperCase
    | _ => nonApplicableStringLiteral
    }

  let paymentRowsFromQueryData = data =>
    data.DailyPaymentsQuery.dailyShopPayments->Array.map(data =>
      switch data {
      | #SalesReceiptPayment(payment) => {
          id: payment.salesReceiptId,
          flowType: switch payment.salesReceipt.invoice {
          | None => Receipt
          | Some({name, createdAt}) =>
            ReceiptTransformed({
              paymentId: payment.salesReceipt.id,
              transformedInvoiceName: name,
              transformedInvoiceCreatedAt: createdAt,
            })
          },
          name: {value: payment.salesReceipt.name},
          createdAt: payment.salesReceiptCreatedAt,
          amount: safeSubstract(
            payment.salesReceiptAmountReceived,
            payment.salesReceiptAmountReturned,
          ),
          paymentMethod: PaymentMethod.fromRawValue(payment.salesReceiptMethod),
          customerNameOrReason: switch payment.salesReceipt.customer {
          | Some({lastName, firstName, companyName}) =>
            customerName(~firstName, ~lastName, ~companyName)
          | _ => nonApplicableStringLiteral
          },
          staffName: payment.salesReceipt.staff.name,
          device: {
            id: payment.salesReceipt.device.id,
            label: DeviceName.decode(
              ~slug=payment.salesReceipt.device.slug,
              ~name=payment.salesReceipt.device.name,
            )->DeviceName.format,
          },
        }
      | #RefundReceiptPayment(payment) => {
          id: payment.refundReceiptId,
          flowType: Receipt,
          name: {value: payment.refundReceipt.name},
          createdAt: payment.refundReceiptCreatedAt,
          amount: safeSubstract(
            payment.refundReceiptAmountReceived,
            payment.refundReceiptAmountReturned,
          ),
          paymentMethod: PaymentMethod.fromRawValue(payment.refundReceiptMethod),
          customerNameOrReason: switch payment.refundReceipt.customer {
          | Some({lastName, firstName, companyName}) =>
            customerName(~firstName, ~lastName, ~companyName)
          | _ => nonApplicableStringLiteral
          },
          staffName: payment.refundReceipt.staff.name,
          device: {
            id: payment.refundReceipt.device.id,
            label: DeviceName.decode(
              ~slug=payment.refundReceipt.device.slug,
              ~name=payment.refundReceipt.device.name,
            )->DeviceName.format,
          },
        }
      | #InvoicePayment(payment) => {
          id: payment.invoiceId,
          flowType: switch payment.invoice.salesReceipt {
          | None => Invoice
          | Some({id, name, createdAt}) =>
            InvoiceTransformed({
              initialSalesReceiptId: id,
              initialSalesReceiptName: name,
              initialSalesReceiptCreatedAt: createdAt,
            })
          },
          name: {value: payment.invoice.name},
          createdAt: payment.invoiceCreatedAt,
          amount: safeSubstract(payment.invoiceAmountReceived, payment.invoiceAmountReturned),
          paymentMethod: PaymentMethod.fromRawValue(payment.invoiceMethod),
          customerNameOrReason: payment.invoice.customerName,
          staffName: payment.invoice.staff.name,
          device: {
            id: payment.device.id,
            label: DeviceName.decode(
              ~slug=payment.device.slug,
              ~name=payment.device.name,
            )->DeviceName.format,
          },
        }
      }
    )

  let mergeRowsByCreatedAt = (~cashFlowsRows, ~paymentsRows) => {
    Array.concat(cashFlowsRows, paymentsRows)
    ->SortArray.stableSortBy((next, current) =>
      Int.fromFloat(Js.Date.getTime(current.createdAt) -. Js.Date.getTime(next.createdAt))
    )
    ->Array.keep(row =>
      switch row {
      | {flowType: InvoiceTransformed({initialSalesReceiptCreatedAt}), createdAt} =>
        DateHelpers.isSameDay(initialSalesReceiptCreatedAt, createdAt)
      | _ => true
      }
    )
  }

  let addCountersToRows = rows => {
    let isRowsPaymentJoined = (rowA, rowB) =>
      rowA.name.value === rowB.name.value && rowA.device.id === rowB.device.id
    let rowsWithOnlyCounts = rows->Array.reduce([], (acc, current) => {
      let count = (acc->Array.keep(row => isRowsPaymentJoined(row, current))->Array.length + 1, 0)
      let row = {...current, name: {...current.name, count}}
      Array.concat(acc, [row])
    })
    let rowsWithFullCounts = rowsWithOnlyCounts->Array.map(row => {
      let count =
        row.name.count->Option.map(count => (
          count->fst,
          rows->Array.keep(current => isRowsPaymentJoined(row, current))->Array.length,
        ))
      {...row, name: {...row.name, ?count}}
    })
    rowsWithFullCounts->Array.map(row => {
      ...row,
      name: {
        ...row.name,
        count: ?switch (row.name.count, row.flowType) {
        | (Some((_, totalCount)), Receipt | ReceiptTransformed(_)) if totalCount > 1 =>
          row.name.count
        | _ => None
        },
      },
    })
  }

  let sortRowsByTime = (~rows, ~direction) =>
    if direction === #ascending {
      rows->SortArray.stableSortBy((next, current) =>
        Int.fromFloat(Js.Date.getTime(next.createdAt) -. Js.Date.getTime(current.createdAt))
      )
    } else {
      rows->SortArray.stableSortBy((next, current) =>
        Int.fromFloat(Js.Date.getTime(current.createdAt) -. Js.Date.getTime(next.createdAt))
      )
    }

  let sortRowsByAmount = (~rows, ~direction) =>
    if direction === #ascending {
      rows->SortArray.stableSortBy((next, current) =>
        switch true {
        | _ if next.amount < current.amount => -1
        | _ if next.amount > current.amount => 1
        | _ => 0
        }
      )
    } else {
      rows->SortArray.stableSortBy((next, current) =>
        switch true {
        | _ if next.amount > current.amount => -1
        | _ if next.amount < current.amount => 1
        | _ => 0
        }
      )
    }

  let filterPaymentsTransformed = (~initialRows, ~rows) =>
    rows->Array.keep(row =>
      switch row.flowType {
      | InvoiceTransformed({initialSalesReceiptId}) =>
        !(
          initialRows->Array.some(current =>
            switch current.flowType {
            | ReceiptTransformed({paymentId}) => initialSalesReceiptId === paymentId
            | _ => false
            }
          )
        )

      | ReceiptTransformed(_) | _ => true
      }
    )

  let filterRowsByFlowType = (rows, ~flowTypeFilter) =>
    rows->Array.keep(row =>
      switch (flowTypeFilter, row.flowType) {
      | (Some(FlowType.Invoice), Invoice | InvoiceTransformed(_))
      | (Some(Receipt), Receipt | ReceiptTransformed(_))
      | (Some(Cashflow), Cashflow)
      | (None, _) => true
      | (Some(_), _) => false
      }
    )

  let filterRowsByDevice = (rows, ~deviceFilter) =>
    rows->Array.keep(row =>
      switch deviceFilter {
      | Some({id}: device) => row.device.id === id
      | None => true
      }
    )

  let keyExtractor = row => row.id
}

module DeviceSelect = {
  module Query = %graphql(`
    query DevicesQuery {
      devices(first: 50) {
        edges {
          node {
            id
            name
            slug
            shop { id }
          }
        }
      }
    }
  `)

  let sortedCashRegistersSelectItemsFromDevicesEdges = (edges, ~shopId) =>
    edges
    ->Array.keepMap(({Query.node: device}) =>
      switch DeviceName.decode(~slug=device.slug, ~name=device.name) {
      | CashRegister(_) as cashRegister if shopId === device.shop.id =>
        let deviceLabel = DeviceName.format(cashRegister)
        Some({
          Select.key: device.id,
          label: deviceLabel,
          value: Some({
            TableRow.id: device.id,
            label: deviceLabel,
          }),
        })
      | _ => None
      }
    )
    ->SortArray.stableSortBy((next, current) =>
      Js.String.localeCompare(current.label, next.label)->Float.toInt
    )

  let servicesSelectItemsFromDevicesEdges = (edges, ~shopId) =>
    edges->Array.keepMap(({Query.node: device}) =>
      switch DeviceName.decode(~slug=device.slug, ~name=device.name) {
      | (DashboardWinoFr | WinoPay) as service if shopId === device.shop.id =>
        let deviceLabel = DeviceName.format(service)
        Some({
          Select.key: device.id,
          label: deviceLabel,
          value: Some({
            TableRow.id: device.id,
            label: deviceLabel,
          }),
        })
      | _ => None
      }
    )

  @react.component
  let make = (~shopId, ~value, ~devicesAsyncResult, ~onChange) => {
    ReactUpdateEffect.use1(() => {
      onChange(None)
      None
    }, [shopId])

    let defaultItem = {
      Select.key: "default",
      label: t("All"),
      value: None,
      sticky: true,
    }
    let cashRegistersItems = switch devicesAsyncResult {
    | AsyncData.Done(Ok({Query.devices: {edges}})) =>
      edges->sortedCashRegistersSelectItemsFromDevicesEdges(~shopId)
    | _ => []
    }
    let servicesItems = switch devicesAsyncResult {
    | AsyncData.Done(Ok({Query.devices: {edges}})) =>
      // NOTE - temporary: filtered until duplicates are cleared (backend operation)
      let edgesFiltered =
        edges->Array.keep(({node}) =>
          DeviceName.decode(~slug=node.slug, ~name=node.name) !== DashboardWinoFr
        )
      edgesFiltered->servicesSelectItemsFromDevicesEdges(~shopId)
    | _ => []
    }
    let sections = Array.concatMany([
      [{Select.items: [defaultItem]}],
      [{title: t("Cash registers"), items: cashRegistersItems}],
      [{title: t("Services"), items: servicesItems}],
    ])

    <Select
      preset=#filter label={t("Device")} placeholder={t("Select a device")} sections value onChange
    />
  }
}

module Reducer = {
  type filters = {
    flowType?: FlowType.t,
    paymentMethod?: PaymentMethod.t,
    device?: TableRow.device,
  }
  type state = {
    filters: filters,
    searchQuery: string,
    sortDescriptor: ReactStately.Table.sortDescriptor,
    asyncResult: AsyncResult.t<array<TableRow.t>, unit>,
  }

  let initialState = {
    filters: {},
    searchQuery: "",
    sortDescriptor: {column: "time", direction: #descending},
    asyncResult: NotAsked,
  }

  type action =
    | MergeAsyncResultsChanged(
        AsyncResult.t<DailyCashFlowsQuery.DailyCashFlowsQuery_inner.t, unit>,
        AsyncResult.t<DailyPaymentsQuery.DailyPaymentsQuery_inner.t, unit>,
      )
    | FiltersChanged(filters)
    | SearchQueryChanged(string)
    | SortingChanged(ReactStately.Table.sortDescriptor)

  let make = (state, action) =>
    switch action {
    | MergeAsyncResultsChanged(cashFlowsAsyncResult, asyncResult) =>
      let mergedAsyncResults = switch (cashFlowsAsyncResult, asyncResult) {
      | (Done(Ok(cashFlowsData)), Done(Ok(paymentsData))) =>
        let cashFlowsRows = TableRow.cashFlowsRowsFromQueryData(cashFlowsData)
        let paymentsRows = TableRow.paymentRowsFromQueryData(paymentsData)
        let mergedRows = TableRow.mergeRowsByCreatedAt(~cashFlowsRows, ~paymentsRows)
        let rowsWithCounts = TableRow.addCountersToRows(mergedRows)
        AsyncData.Done(Ok(rowsWithCounts))
      | (Done(Error()), _) | (_, Done(Error())) => Done(Error())
      | _ => Loading
      }
      {...state, asyncResult: mergedAsyncResults}
    | FiltersChanged(filters) => {...state, filters}
    | SearchQueryChanged(searchQuery) => {...state, searchQuery}
    | SortingChanged(sortDescriptor) =>
      let sortedAsyncResult = state.asyncResult->AsyncResult.mapOk(rows =>
        switch sortDescriptor {
        | {column: "time", direction} => TableRow.sortRowsByTime(~rows, ~direction)
        | {column: "amount", direction} => TableRow.sortRowsByAmount(~rows, ~direction)
        | _ => rows
        }
      )
      {...state, sortDescriptor, asyncResult: sortedAsyncResult}
    }
}

let formatTime = createdAt =>
  switch (Intl.locale, createdAt->Intl.dateTimeFormat(~timeStyle=#short)) {
  | (#"fr-FR", time) => time->Js.String2.replace(":", "h")
  | (#"en-EN", time) => time
  }

let formatAmount = amount =>
  amount->Intl.currencyFormat(~currency=#EUR, ~minimumFractionDigits=2, ~maximumFractionDigits=2)

let tableColumns = [
  {
    Table.key: "number-and-flow-type",
    name: t("No. and type of flow"),
    layout: {minWidth: 180.->#px, width: 2.->#fr, sticky: true},
    render: ({data: {TableRow.name: name, flowType}}) => {
      let style = ReactDOM.Style.make(
        ~display="flex",
        ~flexDirection="column",
        ~margin="-10px 0",
        (),
      )
      let countBadgeStyle = (~first) =>
        ReactDOM.Style.make(
          ~height="8.5px",
          ~marginLeft="4px",
          ~padding="1.5px 4px 2.5px 4px",
          ~backgroundColor=first ? Colors.neutralColor00 : Colors.neutralColor10,
          ~border="1px solid " ++ (first ? Colors.neutralColor25 : Colors.neutralColor20),
          ~borderRadius="9px",
          ~fontSize="8px",
          ~color=Colors.neutralColor90,
          (),
        )
      let transformed = switch flowType {
      | ReceiptTransformed(_) | InvoiceTransformed(_) => true
      | Receipt | Invoice | Cashflow => false
      }
      <div style>
        <Tooltip
          content={<Tooltip.Span text={t("Transformed receipt")} />}
          disabled={!transformed}
          arrowed=false
          placement=#"top start">
          <Inline space=#xxsmall>
            <TextStyle weight=#semibold> {name.value->React.string} </TextStyle>
            {if transformed {
              <Svg width="14" height="14" viewBox="0 0 20 20">
                <Svg.Path
                  d="M6.77255742,9.78138085 L6.86213558,9.79225636 C7.13219931,9.84987748 7.30441807,10.1155185 7.24679695,10.3855823 C7.01665848,11.4642155 6.41259534,12.3505288 5.55164239,13.0601156 L5.34845403,13.2207364 C5.32077498,13.2417311 5.29297762,13.2624688 5.26506962,13.282951 L17.28125,13.28125 C17.5573924,13.28125 17.78125,13.5051076 17.78125,13.78125 C17.78125,14.0573924 17.5573924,14.28125 17.28125,14.28125 L5.12075,14.2811335 L5.35114555,14.4532045 C6.24809057,15.1581025 6.89687627,15.9800249 7.18156309,16.9292247 L7.24508231,17.1691822 C7.30697562,17.438299 7.13898783,17.7066357 6.86987107,17.768529 C6.60075431,17.8304223 6.33241763,17.6624345 6.27052432,17.3933178 C6.08759208,16.5979147 5.54117274,15.8744012 4.73323976,15.2394567 C4.36585819,14.9507361 3.96980364,14.7005752 3.57379314,14.4911667 L3.31904909,14.3619248 L3.12838928,14.273892 C2.89406794,14.2290563 2.71875,14.025627 2.71875,13.78125 C2.71875,13.5357901 2.89562516,13.3316416 3.12887437,13.2893057 L3.14775,13.2871335 L3.20977248,13.2657597 C3.3076688,13.2300149 3.44113414,13.176162 3.60080708,13.1031188 C3.99124351,12.9245115 4.3818497,12.6987897 4.74412533,12.4240013 C5.53612454,11.8232648 6.07625346,11.0794072 6.26880968,10.1769177 C6.3136261,9.96686817 6.48428175,9.81600877 6.68498257,9.78644413 L6.77255742,9.78138085 Z M14.1816887,2.53518677 C14.5150751,3.1250057 14.9735248,3.7009764 15.5259566,4.25249003 C15.9631917,4.68899842 16.4332909,5.08493741 16.9032253,5.43183551 C17.134673,5.60268652 17.3220738,5.73010042 17.4469386,5.80981523 C17.6416527,5.87772507 17.78125,6.06318519 17.78125,6.28125 C17.78125,6.51209918 17.6248047,6.70640861 17.4121526,6.76393981 L17.2609602,6.84025659 L16.9489502,7.0139921 C16.5493255,7.24636299 16.1497363,7.51474363 15.7789627,7.81391878 C14.9594087,8.47521235 14.4073862,9.18479684 14.2320378,9.90024333 C14.1663037,10.1684478 13.895593,10.3325825 13.6273885,10.2668484 C13.359184,10.2011143 13.1950494,9.93040361 13.2607834,9.66219913 C13.498015,8.69426068 14.176759,7.82178502 15.1510014,7.03567417 C15.2594395,6.94817611 15.3697479,6.86326687 15.4814357,6.78098437 L3.21875,6.78125 C2.94260763,6.78125 2.71875,6.55739237 2.71875,6.28125 C2.71875,6.00510763 2.94260763,5.78125 3.21875,5.78125 L15.7267876,5.78199919 C15.4170871,5.52677652 15.1121319,5.25239191 14.8194382,4.96018468 C14.2070224,4.34878674 13.6932951,3.70336843 13.3111325,3.02725569 C13.1752514,2.78685823 13.2599787,2.48182419 13.5003761,2.34594311 C13.7407736,2.21006203 14.0458077,2.2947893 14.1816887,2.53518677 Z"
                  fill=Colors.neutralColor90
                />
              </Svg>
            } else {
              React.null
            }}
            {switch name.count {
            | Some((count, total)) =>
              let formattedCount = count->Int.toString ++ " / " ++ total->Int.toString
              <Tooltip
                content={<Tooltip.Span text={t("Mixed payment")} />}
                arrowed=false
                placement=#"top start">
                <span style={countBadgeStyle(~first=count === 1)}>
                  {formattedCount->React.string}
                </span>
              </Tooltip>
            | None => React.null
            }}
          </Inline>
          {switch flowType {
          | ReceiptTransformed({transformedInvoiceName: name})
          | InvoiceTransformed({initialSalesReceiptName: name}) =>
            <TextStyle size=#tiny variation=#normal> {name->React.string} </TextStyle>
          | _ => React.null
          }}
        </Tooltip>
      </div>
    },
  },
  {
    key: "time",
    name: t("Time"),
    allowsSorting: true,
    layout: {minWidth: 80.->#px},
    render: ({data: {createdAt, flowType}}) => {
      let formattedSecondaryCreatedAtDate = switch flowType {
      | ReceiptTransformed({transformedInvoiceCreatedAt}) =>
        Some(t("Transformed into invoice at") ++ " " ++ formatTime(transformedInvoiceCreatedAt))
      | _ => None
      }
      <Inline>
        <TextStyle> {formatTime(createdAt)->React.string} </TextStyle>
        {switch formattedSecondaryCreatedAtDate {
        | Some(secondaryCreatedAt) =>
          <TooltipIcon variation=#info offset=0.>
            <Tooltip.Span text=secondaryCreatedAt />
          </TooltipIcon>
        | None => React.null
        }}
      </Inline>
    },
  },
  {
    key: "amount",
    name: t("Amount"),
    allowsSorting: true,
    layout: {minWidth: 125.->#px, alignX: #flexEnd, margin: #xlarge},
    render: ({data: {amount}}) => {
      let variation = amount < 0. ? Some(#negative) : None
      let formattedAmount = formatAmount(amount)
      <TextStyle ?variation weight=#strong> {formattedAmount->React.string} </TextStyle>
    },
  },
  {
    key: "payment-method",
    name: t("Payment method"),
    layout: {minWidth: 132.->#px, width: 1.3->#fr},
    render: ({data: {paymentMethod}}) =>
      <PaymentMethodBadge value=paymentMethod shortened=true bordered=true />,
  },
  {
    key: "customer-or-comment",
    name: t("Customer / Comment"),
    layout: {width: 20.->#pct},
    render: ({data: {customerNameOrReason, flowType}}) => {
      let ellipsisDirection = switch flowType {
      | Receipt | ReceiptTransformed(_) | Cashflow => #rtl
      | Invoice | InvoiceTransformed(_) => #ltr
      }
      <View style={Style.style(~width=100.->Style.pct, ())}>
        <Tooltip
          content={<Tooltip.Span text=customerNameOrReason />}
          arrowed=false
          placement=#"top start"
          disabled={customerNameOrReason === nonApplicableStringLiteral}>
          <TextStyle maxLines=1 direction=ellipsisDirection>
            {customerNameOrReason->React.string}
          </TextStyle>
        </Tooltip>
      </View>
    },
  },
  {
    key: "seller",
    name: t("Seller"),
    layout: {width: 1.5->#fr},
    render: ({data: {staffName}}) =>
      <View style={Style.style(~width=100.->Style.pct, ())}>
        <Tooltip
          content={<Tooltip.Span text=staffName />}
          arrowed=false
          placement=#"top start"
          disabled={staffName === nonApplicableStringLiteral}>
          <TextStyle maxLines=1> {staffName->React.string} </TextStyle>
        </Tooltip>
      </View>,
  },
  {
    key: "source",
    name: t("Source"),
    layout: {width: 15.->#pct},
    render: ({data: {device}}) =>
      <View style={Style.style(~width=100.->Style.pct, ())}>
        <Tooltip content={<Tooltip.Span text=device.label />} arrowed=false placement=#"top start">
          <TextStyle maxLines=1> {device.label->React.string} </TextStyle>
        </Tooltip>
      </View>,
  },
]

let useSearchFiltering = (~asyncResult, ~searchQuery) => {
  let {startsWith, endsWith, contains} = ReactAria.Filter.use({
    sensitivity: #base,
    usage: #search,
    ignorePunctuation: true,
  })
  let normalize = str => str->Js.String2.replaceByRe(%re("/\s/g"), "")
  asyncResult->AsyncResult.mapOk(rows =>
    rows->Array.keep(row =>
      endsWith(row.TableRow.name.value, searchQuery) ||
      startsWith(formatTime(row.createdAt)->normalize, searchQuery) ||
      startsWith(formatAmount(row.amount)->normalize, searchQuery) ||
      contains(PaymentMethod.toShortLabel(row.paymentMethod)->normalize, searchQuery) ||
      contains(PaymentMethod.toLabel(row.paymentMethod), searchQuery) ||
      contains(row.customerNameOrReason, searchQuery) ||
      contains(row.staffName, searchQuery)
    )
  )
}

module OperationMetrics = {
  module OperationsMetricCard = {
    let paddingX = Spaces.large->Float.toString ++ "px"
    let paddingY = Spaces.medium->Float.toString ++ "px"
    let border = "2px"
    let borderRadius = "5px"

    let wrapperStyle = ReactDOM.Style.make(
      ~paddingTop=Spaces.normal->Float.toString ++ "px",
      ~paddingBottom=Spaces.normal->Float.toString ++ "px",
      (),
    )
    let style = (~borderColor, ~active, ~selected, ~pressed, ~dimmed, ~muted) =>
      ReactDOMStyle.make(
        ~position="relative",
        ~zIndex=selected ? "2" : "0",
        ~minWidth="180px",
        ~height="138px",
        ~opacity=dimmed ? "0.3" : "1",
        ~padding=`${paddingY} ${paddingX}`,
        ~paddingTop=(Spaces.normal +. 1.)->Float.toString ++ "px",
        ~boxSizing="border-box",
        ~backgroundColor=Colors.neutralColor00,
        ~outline=`${border} solid ${active ? borderColor : "transparent"}`,
        ~outlineOffset=`-${border}`,
        ~borderTop=`6px solid ${!muted ? borderColor : "transparent"}`,
        ~borderRadius,
        ~boxShadow=!muted
          ? `rgba(37, 36, 58, 0.08) 0 0 10px,
             rgba(37, 36, 58, 0.01) 4px 0 4px,
             rgba(37, 36, 58, 0.01) -4px 0 4px`
          : `rgba(37, 36, 58, 0.01) 0 6px 10px`,
        ~transform=pressed ? "scale3d(0.985, 0.985, 1)" : "initial",
        ~transition=`opacity .1s ease-in, outline-color .075s ease-in,
                     transform 0.2s cubic-bezier(.6, 0.7, .5, 1)`,
        (),
      )
    let badgeIconStyle = (~borderColor) =>
      ReactDOMStyle.make(
        ~position="absolute",
        ~right=paddingX,
        ~bottom=paddingY,
        ~display="flex",
        ~justifyContent="center",
        ~alignItems="center",
        ~width="44px",
        ~height="44px",
        ~border=`1px solid ${borderColor}`,
        ~borderRadius,
        ~boxSizing="content-box",
        (),
      )

    @react.component
    let make = React.memo((
      ~amount,
      ~operationQuantity,
      ~paymentMethod,
      ~selected,
      ~dimmed,
      ~onPress,
    ) => {
      let (ref, hovered) = Hover.use()
      let (pressed, setPressed) = React.useState(() => false)

      let active = selected || hovered
      let dimmed = dimmed && !active
      let muted = operationQuantity === 0 && !selected

      let label = PaymentMethod.toLabel(paymentMethod)
      let colorSet = PaymentMethod.ColorSet.make(paymentMethod)
      let svg = {
        let fill = muted ? Some(Colors.neutralColor25) : None
        <PaymentMethod.Svg value=paymentMethod ?fill />
      }

      React.useLayoutEffect1(() => {
        switch ref.React.current->Js.Nullable.toOption {
        | Some(domElement) if selected =>
          domElement->WebAPI.DomElement.scrollIntoView(
            ~options={inline: #nearest, block: #center, behavior: #smooth},
          )
        | _ => ()
        }
        None
      }, [selected])

      let ariaProps = {
        ReactAria.Button.\"aria-label": PaymentMethod.toLabel(paymentMethod) ++ " MetricCard",
        onPressStart: _ => setPressed(_ => true),
        onPressEnd: _ => setPressed(_ => false),
      }
      let borderColor = !muted ? colorSet.borderCard : Colors.neutralColor20
      let borderIconColor = !muted ? colorSet.borderBadgeAndBorderSvg : Colors.neutralColor20

      <div style=wrapperStyle>
        <Touchable
          ariaProps
          ref={ref->ReactDOM.Ref.domRef}
          style={style(~borderColor, ~active, ~selected, ~pressed, ~dimmed, ~muted)}
          onPress>
          <Stack space=#small>
            <Title level=#4 weight=#medium> {label->React.string} </Title>
            <Title
              level=#1 weight=#strong color={muted ? Colors.neutralColor45 : Colors.neutralColor90}>
              {formatAmount(amount)->React.string}
            </Title>
          </Stack>
          <Box spaceBottom=#normal />
          <Stack space=#xsmall>
            <TextStyle variation=#normal size=#xxsmall> {t("Operations")->React.string} </TextStyle>
            <Title
              level=#4 weight=#strong color={muted ? Colors.neutralColor45 : Colors.neutralColor90}>
              {operationQuantity->Int.toString->React.string}
            </Title>
          </Stack>
          <div style={badgeIconStyle(~borderColor=borderIconColor)}> {svg} </div>
        </Touchable>
      </div>
    })
  }

  let safeAdd = (a, b) => {
    let factor = Js.Math.pow_float(~base=10., ~exp=2.)
    let (a, b) = (a *. factor, b *. factor)
    let added = a +. b
    added /. factor
  }

  @react.component
  let make = React.memo((~asyncResult, ~paymentMethodFiltered=?, ~onPressMetric) => {
    let trackRef = React.useRef(Js.Nullable.null)
    let desktopSize = Media.use(Breakpoints.huge)

    ReactUpdateEffect.use1(() => {
      if paymentMethodFiltered->Option.isNone {
        switch trackRef.React.current->Js.Nullable.toOption {
        | Some(domElement) =>
          domElement->WebAPI.DomElement.scrollTo(~options={left: 0, behavior: #smooth})
        | None => ()
        }
      }
      None
    }, [asyncResult])

    let metricCardsByAmount =
      PaymentMethod.values
      ->Array.map(paymentMethod => {
        let filteredAsyncResult =
          asyncResult->AsyncResult.mapOk(
            rows => rows->Array.keep(row => row.TableRow.paymentMethod === paymentMethod),
          )
        let amount = switch filteredAsyncResult {
        | Done(Ok(rows)) => rows->Array.reduce(0., (acc, row) => safeAdd(acc, row.amount))
        | _ => 0.
        }
        let operationQuantity = switch filteredAsyncResult {
        | Done(Ok(rows)) => rows->Array.length
        | _ => 0
        }
        (paymentMethod, amount, operationQuantity)
      })
      ->SortArray.stableSortBy((next, current) =>
        switch (next, current) {
        | ((_, a, _), (_, b, _)) if a === 0. && b < 0. => 1
        | ((_, a, _), (_, b, _)) if b === 0. && a < 0. => -1
        | ((_, totalAmountA, operationQuantityA), (_, totalAmountB, operationQuantityB)) =>
          let positiveTotalAmountA = totalAmountA > 0. ? -.totalAmountA : totalAmountA
          let positiveTotalAmountB = totalAmountB > 0. ? -.totalAmountB : totalAmountB
          let hasSuperiorTotalAmount = positiveTotalAmountA -. positiveTotalAmountB > 0.
          let hasSuperiorOperationQuantity = operationQuantityB - operationQuantityA > 0
          hasSuperiorTotalAmount ? 1 : hasSuperiorOperationQuantity ? 1 : 0
        }
      )

    <SliderTrack trackRef layout=ItemsInView(desktopSize ? 6 : 4) showIndicator=true>
      {metricCardsByAmount
      ->Array.map(item => {
        let (paymentMethod, amount, operationQuantity) = item
        let selected = Some(paymentMethod) === paymentMethodFiltered
        let dimmed = !selected && paymentMethodFiltered->Option.isSome

        <OperationsMetricCard
          amount
          operationQuantity
          paymentMethod
          selected
          dimmed
          onPress={_ => onPressMetric(paymentMethod)}
          key={PaymentMethod.toLabel(paymentMethod)}
        />
      })
      ->React.array}
    </SliderTrack>
  })
}

module AnalyticsCashFlowLoaded = {
  @react.component
  let make = React.memo((
    ~dailyShopCashFlowsAsyncResult,
    ~dailyShopPaymentsAsyncResult,
    ~shop,
    ~date,
    ~onDateChange,
  ) => {
    let (state, dispatch) = React.useReducer(Reducer.make, Reducer.initialState)
    let scope = Auth.useScope()
    let organisationAccount = switch scope {
    | Organisation(_) => true
    | Single(_) => false
    }

    React.useEffect2(() => {
      dispatch(
        MergeAsyncResultsChanged(dailyShopCashFlowsAsyncResult, dailyShopPaymentsAsyncResult),
      )
      dispatch(SortingChanged(state.sortDescriptor))
      None
    }, (dailyShopCashFlowsAsyncResult, dailyShopPaymentsAsyncResult))

    let onRequestSearch = queryString => dispatch(SearchQueryChanged(queryString))

    let filterPaymentsTransformed = asyncResult =>
      switch (state.asyncResult, asyncResult) {
      | (Done(Ok(initialRows)), AsyncData.Done(Ok(rows))) =>
        AsyncData.Done(Ok(TableRow.filterPaymentsTransformed(~rows, ~initialRows)))
      | _ => asyncResult
      }

    let filteredWithoutPaymentMethodAsyncResult =
      state.asyncResult->AsyncResult.mapOk(rows =>
        TableRow.filterRowsByFlowType(rows, ~flowTypeFilter=state.filters.flowType)
      )
    let filteredWithoutPaymentMethodAndWithDeviceAsyncResult =
      filteredWithoutPaymentMethodAsyncResult->AsyncResult.mapOk(rows =>
        TableRow.filterRowsByDevice(rows, ~deviceFilter=state.filters.device)
      )
    let operationMetricsAsyncResult = filterPaymentsTransformed(
      filteredWithoutPaymentMethodAndWithDeviceAsyncResult,
    )
    let searchedAndFilteredAsyncResult = {
      let filteredAsyncResult =
        filteredWithoutPaymentMethodAndWithDeviceAsyncResult
        ->AsyncResult.mapOk(rows =>
          rows->Array.keep(
            row =>
              switch state.filters {
              | {paymentMethod} => paymentMethod === row.paymentMethod
              | _ => true
              },
          )
        )
        ->filterPaymentsTransformed
      useSearchFiltering(~asyncResult=filteredAsyncResult, ~searchQuery=state.searchQuery)
    }

    let getOperationQuantityFromFlowType = flowType =>
      switch filterPaymentsTransformed(state.asyncResult) {
      | Done(Ok(rows)) if flowType->Option.isSome =>
        let rowsByFlowType = rows->Array.keep(row =>
          switch row.flowType {
          | Invoice | InvoiceTransformed(_) => flowType === Some(FlowType.Invoice)
          | Receipt | ReceiptTransformed(_) => flowType === Some(FlowType.Receipt)
          | current => flowType === Some(current)
          }
        )
        rowsByFlowType->Array.length
      | Done(Ok(rows)) => rows->Array.length
      | _ => 0
      }
    let renderFlowTypeSelectionTriggerView = (
      ~children as _,
      ~item,
      ~hovered,
      ~active,
      ~focused,
    ) => {
      let countBadgeStyle = (~active) =>
        ReactDOM.Style.make(
          ~minWidth="8px",
          ~height="10px",
          ~marginLeft="2px",
          ~padding="2px 5px",
          ~backgroundColor=active ? Colors.neutralColor25 : Colors.neutralColor20,
          ~border="1px solid " ++ (active ? Colors.neutralColor30 : Colors.neutralColor25),
          ~borderRadius="10px",
          ~fontFamily=#Archivo->FontFaces.fontFamilyFromFontName,
          ~fontSize="10px",
          ~textAlign="center",
          ~color=Colors.neutralColor90,
          (),
        )
      <OverlayTriggerView preset=#filter label={t("Flow type")} hovered active focused>
        {switch item {
        | Some({Select.label: label, value}) =>
          let operationQuantity = getOperationQuantityFromFlowType(value)
          <Inline space=#xsmall>
            {label->React.string}
            <span style={countBadgeStyle(~active={active || hovered})}>
              {operationQuantity->Int.toString->React.string}
            </span>
          </Inline>
        | None => React.null
        }}
      </OverlayTriggerView>
    }
    let renderFlowTypeSelectionItemContent = React.useCallback1(item => {
      let countBadgeStyle = ReactDOM.Style.make(
        ~minWidth="8px",
        ~height="10px",
        ~marginLeft="2px",
        ~lineHeight="12px",
        ~padding="1.5px 5px 2.5px",
        ~backgroundColor=Colors.neutralColor20,
        ~border=`1px solid ${Colors.neutralColor25}`,
        ~borderRadius="10px",
        ~fontFamily=#Archivo->FontFaces.fontFamilyFromFontName,
        ~fontSize="11px",
        ~textAlign="center",
        ~color=Colors.neutralColor90,
        (),
      )
      let operationQuantity = getOperationQuantityFromFlowType(item.Select.value)
      <Inline space=#xsmall>
        {item.Select.label->React.string}
        <span style={countBadgeStyle}> {operationQuantity->Int.toString->React.string} </span>
      </Inline>
    }, [state.asyncResult])

    let devicesAsyncResult = DeviceSelect.Query.use()->ApolloHelpers.queryResultToAsyncResult

    let renderHeaderActions = () =>
      switch shop {
      | Some(shop) =>
        <Box spaceBottom=#normal>
          <Inline space=#small>
            {if organisationAccount {
              <Auth.SelectSingleShopFilter value=shop />
            } else {
              React.null
            }}
            {if organisationAccount {
              <Separator />
            } else {
              React.null
            }}
            <SelectDateFilter
              label={t("Date")}
              disabledFutureDays=true
              information={t(
                "A day starts at 5:00am and ends at 4:59am the next day to include all your sales, even if you close late.",
              )}
              value=Some(date)
              onChange=onDateChange
            />
            <Select
              preset=#filter
              label={t("Flow type")}
              sections={
                let defaultItem = {
                  Select.key: "default",
                  label: t("All"),
                  value: None,
                  sticky: true,
                }
                let items = FlowType.values->Array.map(flowType => {
                  Select.key: FlowType.toLabel(flowType),
                  label: FlowType.toLabel(flowType),
                  value: Some(flowType),
                })
                [{items: [defaultItem]}, {title: t("Flow types"), items}]
              }
              renderTriggerView=renderFlowTypeSelectionTriggerView
              renderItemContent=renderFlowTypeSelectionItemContent
              value=state.filters.flowType
              onChange={flowType => dispatch(FiltersChanged({...state.filters, ?flowType}))}
            />
            <Select
              preset=#filter
              label={t("Payment method")}
              sections={
                let defaultItem = {
                  Select.key: "default",
                  label: t("All"),
                  value: None,
                  sticky: true,
                }
                let items = PaymentMethod.values->Array.map(method => {
                  Select.key: PaymentMethod.toLabel(method),
                  label: PaymentMethod.toLabel(method),
                  value: Some(method),
                })
                [{items: [defaultItem]}, {title: t("Payment methods"), items}]
              }
              renderItemContent={item =>
                switch item.Select.value {
                | Some(paymentMethod) => <PaymentMethodBadge value=paymentMethod bordered=true />
                | None => <span> {t("All")->React.string} </span>
                }}
              value=state.filters.paymentMethod
              onChange={paymentMethod =>
                dispatch(FiltersChanged({...state.filters, ?paymentMethod}))}
            />
            <DeviceSelect
              shopId=shop.id
              value=state.filters.device
              devicesAsyncResult
              onChange={device => dispatch(FiltersChanged({...state.filters, ?device}))}
            />
            {if (
              state.filters.flowType->Option.isSome ||
              state.filters.paymentMethod->Option.isSome ||
              state.filters.device->Option.isSome
            ) {
              <Scaffold.ResetFiltersButton onPress={() => dispatch(FiltersChanged({}))} />
            } else {
              React.null
            }}
          </Inline>
        </Box>
      | None => React.null
      }

    let searchBar =
      <Box spaceX=#large spaceBottom=#xmedium>
        <SearchBar
          value=state.searchQuery
          placeholder={t("Search an information, time, amount...")}
          onChange=onRequestSearch
        />
      </Box>

    let placeholderNoRows = switch (state.asyncResult, searchedAndFilteredAsyncResult) {
    | (Done(Ok(data)), Done(Ok([]))) if data->Array.length > 0 =>
      <EmptyState
        illustration=Illustration.notFound
        title={t("No result were found.")}
        text={t("Try again with another keyword/filter or:")}>
        <Button
          variation=#neutral
          onPress={_ => {
            dispatch(SearchQueryChanged(""))
            dispatch(FiltersChanged({}))
          }}>
          {t("Clear search query and filters")->React.string}
        </Button>
      </EmptyState>
    | (Done(Ok([])), Done(Ok([]))) =>
      <EmptyState
        illustration=Illustration.notFound
        title={t("No result were found.")}
        text={t("as of") ++ " " ++ date->Intl.dateTimeFormat(~dateStyle=#medium)}
      />
    | _ => EmptyState.error
    }

    let onPressMetric = React.useCallback1(paymentMethod =>
      if state.filters.paymentMethod !== Some(paymentMethod) {
        dispatch(FiltersChanged({...state.filters, paymentMethod}))
      } else {
        dispatch(FiltersChanged({...state.filters, paymentMethod: ?None}))
      }
    , [state.filters])

    <Page title={t("Cash flow")} variation=#compact renderHeaderActions>
      <OperationMetrics
        asyncResult=operationMetricsAsyncResult
        paymentMethodFiltered=?state.filters.paymentMethod
        onPressMetric
      />
      <Box spaceBottom=#small />
      <TableView
        keyExtractor=TableRow.keyExtractor
        columns=tableColumns
        data=searchedAndFilteredAsyncResult
        placeholderEmptyState=placeholderNoRows
        searchBar
        sortDescriptor=state.sortDescriptor
        onSortChange={sortDescriptor => SortingChanged(sortDescriptor)->dispatch}
      />
    </Page>
  })
}

@react.component
let make = () => {
  let shops = Auth.useShops()
  let shop = Auth.useActiveShop()->Option.orElse(shops[0])
  let (date, setDate) = React.useState(() =>
    Js.Date.make()->Js.Date.setHours(dayHoursShifted)->Js.Date.fromFloat
  )

  let dailyShopCashFlowsQuery = DailyCashFlowsQuery.use(
    DailyCashFlowsQuery.makeVariables(
      ~shopId=shop->Option.mapWithDefault("", shop => shop.id),
      ~date=date->Scalar.Datetime.serialize,
      (),
    ),
    ~fetchPolicy=CacheFirst,
    ~skip=shop->Option.isNone,
  )
  let dailyShopCashFlowsAsyncResult =
    dailyShopCashFlowsQuery->ApolloHelpers.queryResultToAsyncResult->AsyncResult.mapError(_ => ())

  let dailyShopPaymentsQuery = DailyPaymentsQuery.use(
    DailyPaymentsQuery.makeVariables(
      ~shopId=shop->Option.mapWithDefault("", shop => shop.id),
      ~date=date->Scalar.Datetime.serialize,
      (),
    ),
    ~fetchPolicy=CacheFirst,
    ~skip=shop->Option.isNone,
  )
  let dailyShopPaymentsAsyncResult =
    dailyShopPaymentsQuery->ApolloHelpers.queryResultToAsyncResult->AsyncResult.mapError(_ => ())

  let onDateChange = React.useCallback0(date =>
    setDate(_ => date->Js.Date.setHours(dayHoursShifted)->Js.Date.fromFloat)
  )

  <AnalyticsCashFlowLoaded
    dailyShopCashFlowsAsyncResult dailyShopPaymentsAsyncResult shop date onDateChange
  />
}

let make = React.memo(make)

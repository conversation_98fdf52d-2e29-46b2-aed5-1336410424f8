open Intl

module Query = %graphql(`
  query OrderListPageQuery($search: String, $before: String, $after: String, $first: Int, $last: Int, $filterBy: InputOrdersQueryFilter) {
    orders(search: $search, before: $before, after: $after, first: $first, last: $last, filterBy: $filterBy) {
      pageInfo {
        startCursor
          endCursor
      }
      totalCount
      edges {
        node {
          id
          formattedName
          shopName
          supplierCompanyName
          issueDate
          receptionFinishedAt
          estimatedReceptionDate
          formattedStatus @ppxOmitFutureValue
          totalAmountExcludingTaxes
          totalAmountIncludingTaxes
          totalProductsQuantity
          totalProductsExpectedQuantity
          supplier {
            id
          }
        }
      }
    }
  }
`)

module NotificationBanner = {
  @react.component
  let make = React.memo((~notification, ~onRequestClose) =>
    switch notification {
    | Some(result) =>
      <Box spaceTop=#medium>
        {switch result {
        | Ok(message) => <Banner textStatus=Success(message) onRequestClose />
        | Error(message) => <Banner textStatus=Danger(message) onRequestClose />
        }}
      </Box>
    | None => React.null
    }
  )
}

module OrderExportMenuItem = {
  let endpoint = Env.sheetUrl() ++ "/orders-history"

  let makeRequestBodyJson = (
    ~shopIds,
    ~status=?,
    ~orderIssueStartDate=?,
    ~orderIssueEndDate=?,
    ~orderReceptionFinishedStartDate=?,
    ~orderReceptionFinishedEndDate=?,
    (),
  ) => {
    let body = Js.Dict.empty()

    body->Js.Dict.set(
      "shopIds",
      shopIds->Array.map(shopId => shopId->Json.encodeString)->Json.encodeArray,
    )
    switch status {
    | Some(status) => body->Js.Dict.set("status", status->OrderStatus.toString->Json.encodeString)
    | _ => ()
    }
    switch orderIssueStartDate {
    | Some(startDate) =>
      body->Js.Dict.set("orderIssueStartDate", startDate->Js.Date.toISOString->Json.encodeString)
    | _ => ()
    }
    switch orderIssueEndDate {
    | Some(endDate) =>
      body->Js.Dict.set("orderIssueEndDate", endDate->Js.Date.toISOString->Json.encodeString)
    | _ => ()
    }
    switch orderReceptionFinishedStartDate {
    | Some(startDate) =>
      body->Js.Dict.set(
        "orderReceptionFinishedStartDate",
        startDate->Js.Date.toISOString->Json.encodeString,
      )
    | _ => ()
    }
    switch orderReceptionFinishedEndDate {
    | Some(endDate) =>
      body->Js.Dict.set(
        "orderReceptionFinishedEndDate",
        endDate->Js.Date.toISOString->Json.encodeString,
      )
    | _ => ()
    }
    body->Js.Dict.set("timeZone", Intl.timeZone->Json.encodeString)

    body->Json.encodeDict
  }

  @react.component
  let make = (
    ~text,
    ~shopIds,
    ~status=?,
    ~issueDateRange=?,
    ~receptionDateRange=?,
    ~onRequestErrorNotification,
  ) => {
    let {onRequestClose} = Popover.useState()

    let request = () =>
      Request.make(
        endpoint,
        ~method=#POST,
        ~bodyJson=makeRequestBodyJson(
          ~shopIds,
          ~status?,
          ~orderIssueStartDate=?issueDateRange->Option.map(fst),
          ~orderIssueEndDate=?issueDateRange->Option.map(snd),
          ~orderReceptionFinishedStartDate=?receptionDateRange->Option.map(fst),
          ~orderReceptionFinishedEndDate=?receptionDateRange->Option.map(snd),
          (),
        ),
      )

    let onSuccess = _ => onRequestClose()
    let onFailure = error => {
      let errorMessage =
        error->RequestOpenStorageUrlMenuItem.failureErrorToString(~exportName="supplier orders")
      onRequestErrorNotification(errorMessage)
      onRequestClose()
    }

    let operableRequest = Ok(request)

    <RequestOpenStorageUrlMenuItem text operableRequest onSuccess onFailure />
  }
}

module OrderProductExportMenuItem = {
  let endpoint = Env.sheetUrl() ++ "/orders-products-history"

  let makeRequestBodyJson = (
    ~shopIds,
    ~status=?,
    ~orderIssueStartDate=?,
    ~orderIssueEndDate=?,
    ~orderReceptionFinishedStartDate=?,
    ~orderReceptionFinishedEndDate=?,
    (),
  ) => {
    let body = Js.Dict.empty()

    body->Js.Dict.set(
      "shopIds",
      shopIds->Array.map(shopId => shopId->Json.encodeString)->Json.encodeArray,
    )
    switch status {
    | Some(status) => body->Js.Dict.set("status", status->OrderStatus.toString->Json.encodeString)
    | _ => ()
    }
    switch orderIssueStartDate {
    | Some(startDate) =>
      body->Js.Dict.set("orderIssueStartDate", startDate->Js.Date.toISOString->Json.encodeString)
    | _ => ()
    }
    switch orderIssueEndDate {
    | Some(endDate) =>
      body->Js.Dict.set("orderIssueEndDate", endDate->Js.Date.toISOString->Json.encodeString)
    | _ => ()
    }
    switch orderReceptionFinishedStartDate {
    | Some(startDate) =>
      body->Js.Dict.set(
        "orderReceptionFinishedStartDate",
        startDate->Js.Date.toISOString->Json.encodeString,
      )
    | _ => ()
    }
    switch orderReceptionFinishedEndDate {
    | Some(endDate) =>
      body->Js.Dict.set(
        "orderReceptionFinishedEndDate",
        endDate->Js.Date.toISOString->Json.encodeString,
      )
    | _ => ()
    }
    body->Js.Dict.set("timeZone", Intl.timeZone->Json.encodeString)

    body->Json.encodeDict
  }

  @react.component
  let make = (
    ~text,
    ~shopIds,
    ~status=?,
    ~issueDateRange=?,
    ~receptionDateRange=?,
    ~onRequestErrorNotification,
  ) => {
    let {onRequestClose} = Popover.useState()

    let request = () =>
      Request.make(
        endpoint,
        ~method=#POST,
        ~bodyJson=makeRequestBodyJson(
          ~shopIds,
          ~status?,
          ~orderIssueStartDate=?issueDateRange->Option.map(fst),
          ~orderIssueEndDate=?issueDateRange->Option.map(snd),
          ~orderReceptionFinishedStartDate=?receptionDateRange->Option.map(fst),
          ~orderReceptionFinishedEndDate=?receptionDateRange->Option.map(snd),
          (),
        ),
      )

    let onSuccess = _ => onRequestClose()
    let onFailure = error => {
      let errorMessage =
        error->RequestOpenStorageUrlMenuItem.failureErrorToString(~exportName="ordered products")
      onRequestErrorNotification(errorMessage)
      onRequestClose()
    }

    let operableRequest = Ok(request)

    <RequestOpenStorageUrlMenuItem text operableRequest onSuccess onFailure />
  }
}

module Filters = {
  type t = {
    shop: option<Auth.shop>,
    status: option<OrderStatus.t>,
    issueDateRange: option<(Js.Date.t, Js.Date.t)>,
    receptionDateRange: option<(Js.Date.t, Js.Date.t)>,
  }

  let statuses = [#DRAFT, #FINALIZED, #ACCEPTED, #RECEIVING, #RECEIVED, #TO_PAY, #PAID, #ARCHIVED]

  let encoder = ({shop, status, issueDateRange, receptionDateRange}) => (
    shop->Option.map(shop => shop.id),
    status->Option.map(OrderStatus.toString),
    issueDateRange->Option.map(((start, end)) => [start->Js.Date.valueOf, end->Js.Date.valueOf]),
    receptionDateRange->Option.map(((start, end)) => [
      start->Js.Date.valueOf,
      end->Js.Date.valueOf,
    ]),
  )

  let decoder = (~shops, (shopId, status, issueDateRange, receptionDateRange)) => Ok({
    shop: shops->Array.getBy((shop: Auth.shop) => Some(shop.id) === shopId),
    status: status->Option.map(OrderStatus.fromStringExn),
    issueDateRange: issueDateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
    receptionDateRange: receptionDateRange->Option.flatMap(range =>
      switch range {
      | [start, end] => Some((start->Js.Date.fromFloat, end->Js.Date.fromFloat))
      | _ => None
      }
    ),
  })

  let useJsonCodec = () => {
    let shops = Auth.useShops()

    JsonCodec.object4(
      encoder,
      decoder(~shops),
      JsonCodec.field("shopId", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("status", JsonCodec.string)->JsonCodec.optional,
      JsonCodec.field("issueDateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
      JsonCodec.field("receptionDateRange", JsonCodec.array(JsonCodec.float))->JsonCodec.optional,
    )
  }
}

module Row = {
  type t = {
    id: string,
    formattedName: string,
    shopName: string,
    supplierCompanyName: string,
    supplierId: option<string>,
    issueDate: Js.Date.t,
    receptionFinishedAt: option<Js.Date.t>,
    estimatedReceptionDate: Js.Date.t,
    statuses: array<OrderStatus.t>,
    totalAmountExcludingTaxes: float,
    totalAmountIncludingTaxes: float,
    totalProductsQuantity: int,
    totalProductsExpectedQuantity: int,
  }
}

module Scaffolded = Scaffold.Make({
  type filters = Filters.t
  let useFiltersJsonCodec = Filters.useJsonCodec

  module QueryInner = Query.Query_inner
  type queryVariableFilterBy = Query.t_variables_InputOrdersQueryFilter
  let useQuery = Query.use

  let makeQueryVariables = (
    _defaultQueryVariables,
    ~connectionArguments,
    ~search=?,
    ~filterBy=?,
    (),
  ) => {
    QueryInner.first: connectionArguments.first,
    last: connectionArguments.last,
    before: connectionArguments.Scaffold.before,
    after: connectionArguments.after,
    search,
    filterBy,
  }

  let makeQueryVariablesFilterBy = ({
    Filters.shop: shop,
    status,
    issueDateRange,
    receptionDateRange,
  }) => {
    Query.shopIds: switch shop {
    | Some({id: shopId}) => Some(Query.makeInputObjectInFilter(~_in=[shopId], ()))
    | _ => None
    },
    status: switch status->Option.map(OrderStatus.toString) {
    | Some(status) => Some(Query.makeInputObjectInFilter(~_in=[status], ()))
    | _ => None
    },
    createdAt: None,
    issueDate: switch issueDateRange {
    | Some((start, end)) =>
      Some({
        _after: None,
        _before: None,
        _between: Some([start->Scalar.Datetime.serialize, end->Scalar.Datetime.serialize]),
      })
    | _ => None
    },
    receptionFinishedAt: switch receptionDateRange {
    | Some((start, end)) =>
      Some({
        _after: None,
        _before: None,
        _between: Some([start->Scalar.Datetime.serialize, end->Scalar.Datetime.serialize]),
      })
    | _ => None
    },
  }

  let totalCountFromQueryData = ({Query.orders: orders}) => orders.totalCount
  let cursorsFromQueryData = ({Query.orders: orders}) => (
    orders.pageInfo.startCursor,
    orders.pageInfo.endCursor,
  )

  type row = Row.t
  let rowsFromQueryDataAndState = ({Query.orders: orders}, _) =>
    orders.edges->Array.map(edge => {
      Row.id: edge.node.id,
      formattedName: edge.node.formattedName,
      shopName: edge.node.shopName,
      supplierId: edge.node.supplier->Option.map(supplier => supplier.id),
      supplierCompanyName: edge.node.supplierCompanyName,
      issueDate: edge.node.issueDate,
      receptionFinishedAt: edge.node.receptionFinishedAt,
      estimatedReceptionDate: edge.node.estimatedReceptionDate,
      statuses: edge.node.formattedStatus,
      totalAmountExcludingTaxes: edge.node.totalAmountExcludingTaxes,
      totalAmountIncludingTaxes: edge.node.totalAmountIncludingTaxes,
      totalProductsQuantity: edge.node.totalProductsQuantity,
      totalProductsExpectedQuantity: edge.node.totalProductsExpectedQuantity,
    })

  let keyExtractor = ({Row.id: id}) => id
})

module SheetExportInput = {
  type t = {
    shopId: option<string>,
    status: option<OrderStatus.t>,
    issueDateRange: option<(Js.Date.t, Js.Date.t)>,
    receptionDateRange: option<(Js.Date.t, Js.Date.t)>,
  }

  let makeFromState = ({Scaffold.filters: filters}) => {
    let {Filters.shop: shop, status, issueDateRange, receptionDateRange} = filters
    let shopId = shop->Option.map(({id}) => id)

    {
      shopId,
      status,
      issueDateRange,
      receptionDateRange,
    }
  }
}

@react.component
let make = () => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()
  let authScope = Auth.useScope()
  let organisationAccount = switch Auth.useScope() {
  | Organisation(_) => true
  | Single(_) => false
  }

  let initialFilters = {
    Filters.shop: activeShop,
    status: None,
    issueDateRange: None,
    receptionDateRange: None,
  }
  let initialState = Scaffolded.makeInitialState(~filters=initialFilters)
  let (state, dispatch) = Scaffolded.use(() => initialState)
  let defaultQueryVariables = Query.makeVariables()

  let (notification, setNotification) = React.useState(() => None)

  let columns = [
    {
      Scaffold.name: t("Number"),
      layout: {minWidth: 160.->#px, width: 20.->#pct, margin: #normal, sticky: true},
      render: ({Row.formattedName: formattedName, shopName, id}) => {
        let shopName = switch authScope {
        | Organisation({activeShop: None}) => Some(shopName)
        | _ => None
        }
        <OrderNameTableCell value=formattedName ?shopName id />
      },
    },
    {
      name: t("Supplier"),
      layout: {minWidth: 160.->#px, width: 20.->#pct, margin: #small},
      render: ({supplierId, supplierCompanyName}) =>
        switch supplierId {
        | Some(id) => <TextLink text=supplierCompanyName to=Route(SupplierRoutes.showRoute(~id)) />
        | _ => React.null
        },
    },
    {
      name: t("Amount VAT excl."),
      layout: {minWidth: 110.->#px, sticky: true},
      render: ({totalAmountExcludingTaxes, totalAmountIncludingTaxes}) =>
        <AmountTableCell
          value=totalAmountExcludingTaxes
          secondaryValue=totalAmountIncludingTaxes
          decimalPrecision=3
        />,
    },
    {
      name: t("Issue date"),
      layout: {minWidth: 110.->#px},
      render: ({issueDate}) => <TableDateCell value=Some(issueDate) />,
    },
    {
      name: t("Reception date"),
      layout: {minWidth: 110.->#px},
      render: ({receptionFinishedAt, estimatedReceptionDate}) =>
        switch (receptionFinishedAt, estimatedReceptionDate) {
        | (None, estimatedReceptionDate) =>
          <TableDateCell value=Some(estimatedReceptionDate) label={t("estimated")} />
        | (receptionFinishedAt, _) => <TableDateCell value=receptionFinishedAt />
        },
    },
    {
      name: t("Rec. qt"),
      layout: {width: 100.->#px, alignX: #center, margin: #xxlarge},
      render: ({statuses, totalProductsQuantity, totalProductsExpectedQuantity}) =>
        // TODO - should be a /modules/Order component
        <Inline>
          <TextStyle weight=#semibold>
            {switch statuses {
            | statuses
              if statuses->OrderStatus.has(#RECEIVING) ||
                (statuses->OrderStatus.has(#RECEIVED) ||
                statuses->OrderStatus.has(#ARCHIVED)) =>
              totalProductsQuantity->Int.toString->React.string
            | _ => "—"->React.string
            }}
          </TextStyle>
          <TextStyle>
            {(" / " ++ totalProductsExpectedQuantity->Int.toString)->React.string}
          </TextStyle>
        </Inline>,
    },
    {
      name: t("Status"),
      layout: {minWidth: 100.->#px, width: 1.3->#fr},
      render: ({statuses}) => <OrderStatusBadges statuses />,
    },
    {
      layout: {minWidth: 70.->#px, width: 70.->#px},
      render: ({id, statuses}) =>
        <OrderTableActions
          id statuses callbackUrlAfterArchiveAction={Order->LegacyRouter.routeToPathname}
        />,
    },
  ]

  let filters =
    <Inline space=#small>
      {if organisationAccount {
        <Auth.SelectShopFilter
          value=?state.filters.shop
          onChange={shop => FiltersUpdated(prev => {...prev, shop})->dispatch}
        />
      } else {
        React.null
      }}
      {if organisationAccount {
        <Separator />
      } else {
        React.null
      }}
      <Select
        preset=#filter
        label={t("Status")}
        sections={
          let defaultItem = {
            Select.key: "default",
            label: t("Not archived"),
            value: None,
          }
          let items = Filters.statuses->Array.map(status => {
            Select.key: status->OrderStatus.toString,
            label: status->OrderStatus.toLabel,
            value: Some(status),
          })

          [{title: t("Statuses"), items: Array.concat([defaultItem], items)}]
        }
        value=state.filters.status
        onChange={status => FiltersUpdated(prev => {...prev, status})->dispatch}
      />
      <SelectDateRangeFilter
        label={t("Issue")}
        placeholder=""
        value=?state.filters.issueDateRange
        onChange={issueDateRange => FiltersUpdated(prev => {...prev, issueDateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      <SelectDateRangeFilter
        label={t("Reception")}
        placeholder=""
        value=?state.filters.receptionDateRange
        onChange={receptionDateRange =>
          FiltersUpdated(prev => {...prev, receptionDateRange})->dispatch}
        triggerLabelDisplay=#showPreset
      />
      {switch state.filters {
      | {status: Some(_)} | {issueDateRange: Some(_)} | {receptionDateRange: Some(_)} =>
        <Scaffold.ResetFiltersButton
          onPress={() => FiltersUpdated(_ => initialState.filters)->dispatch}
        />
      | _ => React.null
      }}
    </Inline>

  let actions = {
    let {shopId, status, issueDateRange, receptionDateRange} = state->SheetExportInput.makeFromState

    let shopIds = switch shopId {
    | Some(shopId) => [shopId]
    | _ => shops->Array.map(shop => shop.id)
    }

    let onRequestErrorNotification = error => setNotification(_ => Some(Error(error)))

    <Inline space=#small>
      <Menu disabled={shopIds->Array.size === 0} overlayPriority=false>
        <OrderExportMenuItem
          text={t("Orders export")}
          shopIds
          ?status
          ?issueDateRange
          ?receptionDateRange
          onRequestErrorNotification
        />
        <OrderProductExportMenuItem
          text={t("Ordered products export")}
          shopIds
          ?status
          ?issueDateRange
          ?receptionDateRange
          onRequestErrorNotification
        />
      </Menu>
      <ButtonLink variation=#primary to=Route(Order->LegacyRouter.routeToPathname ++ "/create")>
        {t("Create order")->React.string}
      </ButtonLink>
    </Inline>
  }

  let banner = <NotificationBanner notification onRequestClose={() => setNotification(_ => None)} />

  let searchBar =
    <SearchBar
      value=?state.searchQuery
      placeholder={t("Search an order")}
      onChange={searchQuery => Searched(searchQuery)->dispatch}
    />

  let emptyState = switch state {
  | {
      currentPage: 1,
      searchQuery: None,
      filters: {shop, status: None, issueDateRange: None, receptionDateRange: None},
    } if shop === activeShop =>
    <EmptyState
      illustration=Illustration.create title={t("Welcome to the supplier orders space.")}
    />
  | _ =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword/filter or:")}>
      <Button variation=#neutral onPress={_ => Reset(initialState)->dispatch}>
        {t("Clear search query and filters")->React.string}
      </Button>
    </EmptyState>
  }

  <Scaffolded
    title={t("Supplier orders")}
    state
    dispatch
    filters
    columns
    actions
    banner
    searchBar
    emptyState
    defaultQueryVariables
  />
}

let make = React.memo(make)

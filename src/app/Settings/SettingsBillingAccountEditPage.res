open Intl
open Style

module BillingAccountFormLenses = %lenses(
  type state = {
    corporateName: string,
    shopName: string,
    email: string,
    phone: string,
    vatNumber: string,
    address: string,
    city: string,
    postalCode: string,
    country: CountryCode.t,
    shippingAddress: string,
    shippingPostalCode: string,
    shippingCity: string,
    shippingCountry: string,
    sameShippingAddressThanBilling: bool,
  }
)

module BillingAccountForm = Form.Make(BillingAccountFormLenses)

let stripEmptyString = string =>
  switch string {
  | "" => None
  | string => Some(string)
  }

module BillingAccountEditRequest = {
  let encodeBody = (
    ~shopId,
    ~corporateName,
    ~shopName,
    ~email,
    ~phone,
    ~vatNumber,
    ~address,
    ~postalCode,
    ~city,
    ~country,
    ~shippingAddress,
    ~shippingPostalCode,
    ~shippingCity,
    ~shippingCountry,
    ~sameShippingAddressThanBilling,
  ) =>
    Js.Dict.fromArray([
      ("shopId", shopId->Json.encodeString),
      ("corporateName", corporateName->Json.encodeString),
      ("shopName", shopName->Json.encodeString),
      ("email", email->Json.encodeString),
      ("phone", phone->Json.encodeString),
      ("vatNumber", vatNumber->Json.encodeString),
      (
        "billingAddress",
        Js.Dict.fromArray([
          ("address", address->Json.encodeString),
          ("city", city->Json.encodeString),
          ("postalCode", postalCode->Json.encodeString),
          ("country", country->CountryCode.toIsoString->Json.encodeString),
        ])->Json.encodeDict,
      ),
      (
        "shippingAddress",
        if sameShippingAddressThanBilling {
          Js.Dict.fromArray([
            ("address", address->Json.encodeString),
            ("city", city->Json.encodeString),
            ("postalCode", postalCode->Json.encodeString),
            ("country", country->CountryCode.toIsoString->Json.encodeString),
          ])->Json.encodeDict
        } else {
          Js.Dict.fromArray([
            ("address", shippingAddress->Json.encodeString),
            ("city", shippingCity->Json.encodeString),
            ("postalCode", shippingPostalCode->Json.encodeString),
            ("country", shippingCountry->Json.encodeString),
          ])->Json.encodeDict
        },
      ),
    ])

  type failureKind =
    | DuplicateUserUsername
    | InvalidVatNumber
    | NotFoundBillingAccount
    | Unknown

  let decodeInvalidRequestFailure = serverFailure =>
    switch serverFailure {
    | {Request.kind: "DuplicateUserUsername"} => DuplicateUserUsername
    | {kind: "InvalidVatNumber"} => InvalidVatNumber
    | {kind: "NotFoundBillingAccount"} => NotFoundBillingAccount
    | _ => Unknown
    }

  let endpoint = Env.gatewayUrl() ++ "/billing-account"

  type makeT = (
    ~shopId: string,
    ~corporateName: string,
    ~shopName: string,
    ~email: string,
    ~phone: string,
    ~vatNumber: string,
    ~address: string,
    ~postalCode: string,
    ~city: string,
    ~country: CountryCode.t,
    ~shippingAddress: string,
    ~shippingPostalCode: string,
    ~shippingCity: string,
    ~shippingCountry: string,
    ~sameShippingAddressThanBilling: bool,
  ) => Future.t<result<unit, option<failureKind>>>

  let make = (
    ~shopId,
    ~corporateName,
    ~shopName,
    ~email,
    ~phone,
    ~vatNumber,
    ~address,
    ~postalCode,
    ~city,
    ~country,
    ~shippingAddress,
    ~shippingPostalCode,
    ~shippingCity,
    ~shippingCountry,
    ~sameShippingAddressThanBilling,
  ) =>
    Request.make(
      endpoint,
      ~method=#PATCH,
      ~bodyJson=encodeBody(
        ~shopId,
        ~corporateName,
        ~shopName,
        ~email,
        ~phone,
        ~vatNumber,
        ~address,
        ~postalCode,
        ~city,
        ~country,
        ~shippingAddress,
        ~shippingPostalCode,
        ~shippingCity,
        ~shippingCountry,
        ~sameShippingAddressThanBilling,
      )->Json.encodeDict,
      ~authTokenRequired=true,
    )
    ->Future.mapOk(_ => ())
    ->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailure) =>
        invalidRequestFailure->Array.get(0)->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
}

let requestBillingAccountUpdate = BillingAccountEditRequest.make

module NotificationBanner = {
  @react.component
  let make = (~notification) =>
    <Box spaceTop=#medium style={style(~width=100.->pct, ())}>
      <Banner textStatus=notification />
    </Box>

  let make = React.memo(make)
}

let validateAddressValue = (value, values) => {
  if !values.BillingAccountFormLenses.sameShippingAddressThanBilling && value === "" {
    Error(t("Please fulfill this field."))
  } else {
    Ok()
  }
}

let schema = [
  BillingAccountForm.Schema.CustomString(
    CorporateName,
    (value, _) => {
      if value->CorporateEntity.CorporateName.validate {
        Ok()
      } else {
        Error(t("The corporate name is invalid. Please provide the full name of the company."))
      }
    },
  ),
  StringNotEmpty(ShopName),
  Email(Email),
  PhoneNumber(Phone),
  StringNotEmpty(Address),
  StringNotEmpty(PostalCode),
  StringNotEmpty(City),
  CustomString(ShippingAddress, validateAddressValue),
  CustomString(ShippingPostalCode, validateAddressValue),
  CustomString(ShippingCity, validateAddressValue),
  CustomString(ShippingCountry, validateAddressValue),
]

@react.component
let make = (
  ~activeShopId,
  ~corporateName,
  ~shopName,
  ~email,
  ~phone,
  ~vatNumber,
  ~billingAddress,
  ~shippingAddress,
  ~requestBillingAccountUpdate: BillingAccountEditRequest.makeT,
) => {
  let jwt = Auth.getJwt()->Option.getWithDefault("")
  let logUser = Auth.useLogUser()

  let (notification, setNotfication) = React.useState(() => None)

  let onSubmitFailure = error => setNotfication(_ => Some(Error(error)))
  let onSubmitSuccess = _ => {
    logUser(jwt)
    let text = t(
      "You have made changes to your billing information, please now verify your details in Shop settings.",
    )
    setNotfication(_ => Some(Ok(text)))
  }

  let formPropState = BillingAccountForm.useFormPropState({
    let sameShippingAddressThanBilling = switch (billingAddress, shippingAddress) {
    | (Some(billingAddress), Some(shippingAddress)) =>
      CorporateEntity.Address.equal(billingAddress, shippingAddress)
    | (None, None) => true
    | (Some(_), None) | (None, Some(_)) => false
    }

    let billingAddress = billingAddress->Option.getWithDefault(CorporateEntity.Address.initialValue)
    let shippingAddress =
      shippingAddress->Option.getWithDefault(CorporateEntity.Address.initialValue)
    {
      initialValues: {
        corporateName,
        shopName,
        email,
        phone,
        vatNumber: vatNumber->Option.getWithDefault(""),
        address: billingAddress.address,
        postalCode: billingAddress.postalCode,
        city: billingAddress.city,
        country: billingAddress.country->CountryCode.fromString->Result.getWithDefault(FR),
        shippingAddress: shippingAddress.address,
        shippingPostalCode: shippingAddress.postalCode,
        shippingCity: shippingAddress.city,
        shippingCountry: shippingAddress.country
        ->CountryCode.fromString
        ->Result.getWithDefault(FR)
        ->CountryCode.toMediumCountryString,
        sameShippingAddressThanBilling,
      },
      schema,
      onSubmitSuccess,
      onSubmitFailure,
    }
  })

  let (formState, formDispatch) = formPropState
  let {values, submission, validation} = formState

  let submitting = submission->Form.Submission.isRequested

  React.useEffect1(() => {
    if submitting {
      setNotfication(_ => None)
    }
    None
  }, [submitting])

  React.useEffect1(() => {
    if notification->Option.isSome {
      WebAPI.window
      ->WebAPI.Window.scrollToWithOptions({"top": 0., "left": 0., "behavior": "smooth"})
      ->ignore
    }
    None
  }, [notification])

  let onSubmit = (
    _,
    {
      BillingAccountFormLenses.corporateName: corporateName,
      shopName,
      email,
      phone,
      vatNumber,
      address,
      city,
      postalCode,
      country,
      shippingAddress,
      shippingPostalCode,
      shippingCity,
      shippingCountry,
      sameShippingAddressThanBilling,
    },
  ) =>
    requestBillingAccountUpdate(
      ~shopId=activeShopId,
      ~corporateName,
      ~shopName,
      ~email,
      ~phone,
      ~vatNumber,
      ~address,
      ~postalCode,
      ~city,
      ~country,
      ~shippingAddress,
      ~shippingPostalCode,
      ~shippingCity,
      ~shippingCountry,
      ~sameShippingAddressThanBilling,
    )
    ->Future.mapOk(() => Some(t("Account successfully set up.")))
    ->Future.mapError(failure =>
      switch failure {
      | Some(BillingAccountEditRequest.DuplicateUserUsername) =>
        t("This email address is already used.")
      | Some(InvalidVatNumber) => t("The VAT number is not valid.")
      | Some(NotFoundBillingAccount) =>
        let text = "Currently, a maintenance operation is underway on billing accounts. If you have an urgent need regarding these parameters, please contact the support."
        t(text)
      | _ => t("An unexpected error occured. Please try again or contact the support.")
      }
    )

  let errorMessageAddress = switch (validation, submission) {
  | (Error(errors), Failed(_)) =>
    errors
    ->Array.keepMap(((field, error)) =>
      switch (field, error) {
      | (BillingAccountForm.Schema.Field(BillingAccountFormLenses.Address), error) => Some(error)
      | _ => None
      }
    )
    ->Array.get(0)
  | _ => None
  }
  let onChangeAddress = value => formDispatch(FieldValueChanged(Address, _ => value))
  let onRequestAddressAutoComplete = (address: AddressComboBoxField.address) => {
    formDispatch(FieldValueChanged(Address, _ => address.name))
    formDispatch(FieldValueChanged(PostalCode, _ => address.postcode))
    formDispatch(FieldValueChanged(City, _ => address.city))
  }

  let errorMessageShippingAddress = switch (validation, submission) {
  | (Error(errors), Failed(_)) =>
    errors
    ->Array.keepMap(((field, error)) =>
      switch (field, error) {
      | (BillingAccountForm.Schema.Field(BillingAccountFormLenses.ShippingAddress), error) =>
        Some(error)
      | _ => None
      }
    )
    ->Array.get(0)
  | _ => None
  }
  let onChangeShippingAddress = value =>
    formDispatch(FieldValueChanged(ShippingAddress, _ => value))
  let onRequestShippingAddressAutoComplete = (address: AddressComboBoxField.address) => {
    formDispatch(FieldValueChanged(ShippingAddress, _ => address.name))
    formDispatch(FieldValueChanged(ShippingPostalCode, _ => address.postcode))
    formDispatch(FieldValueChanged(ShippingCity, _ => address.city))
  }

  let (canGoBack, onGoBack) = Navigation.useGoBack()

  let cancelButton = if canGoBack {
    <Button variation=#neutral onPress={_ => onGoBack()}> {t("Discard")->React.string} </Button>
  } else {
    <BillingAccountForm.CancelButton size=#medium text={t("Cancel")} />
  }
  let actionsBar =
    <ResourceDetailsPage.ActionsBar
      items=[
        cancelButton,
        <BillingAccountForm.SubmitButton
          size=#medium text={t("Save")} variation=#success onSubmit
        />,
      ]
    />

  let notificationBanner = switch notification {
  | Some(value) =>
    let onRequestClose = () => setNotfication(_ => None)
    <ResourceDetailsPage.NotificationBanner value onRequestClose />
  | None => React.null
  }

  <BillingAccountForm.FormProvider propState=formPropState>
    <BillingAccountForm.ControlEnterKey onSubmit />
    <ResourceDetailsPage title={t("Edit billing information")} actionsBar notificationBanner>
      <Stack space=#xlarge>
        <FieldsetLayoutPanel
          title={t("General information")}
          description={t("Complete the name of your company and associated information.")}>
          <BillingAccountForm.InputText field=CorporateName label={t("Corporate name")} />
          <BillingAccountForm.InputText field=ShopName label={t("Shop name")} />
          <BillingAccountForm.InputText
            field=Email label={t("Billing email")} placeholder={t("<EMAIL>")}
          />
          <BillingAccountForm.InputText
            field=Phone label={t("Shop phone")} placeholder={t("01 23 45 67 89")}
          />
          <BillingAccountForm.InputText
            field=VatNumber label={t("VAT number")} placeholder={t("FR 11 *********")}
          />
        </FieldsetLayoutPanel>
        <FieldsetLayoutPanel
          title={t("Billing address")}
          description={t(
            "Specify the address that will appear on your Wino subscription invoices.",
          )}>
          <AddressComboBoxField
            required=true
            errorMessage=?errorMessageAddress
            addressName=values.address
            onInputChange=onChangeAddress
            onRequestAutoComplete=onRequestAddressAutoComplete
          />
          <Group>
            <BillingAccountForm.InputText field=PostalCode label={t("Postal code")} />
            <BillingAccountForm.InputText field=City label={t("City")} />
          </Group>
          <BillingAccountForm.InputSelect
            field=Country
            label={t("Country")}
            sections={[
              {
                Select.items: BillingAccount.acceptedCountryCodes->Array.map(value => {
                  Select.label: t(value->CountryCode.toMediumCountryString),
                  key: value->CountryCode.toIso2String,
                  value,
                }),
              },
            ]}
          />
        </FieldsetLayoutPanel>
        <FieldsetLayoutPanel
          title={t("Shipping address")}
          description={t(
            "Provide the address where you would like to receive your cash register equipment.",
          )}>
          <BillingAccountForm.InputToggleSwitch
            field={SameShippingAddressThanBilling}
            label={t("Shipping address same as billing address.")}
          />
          {if !values.sameShippingAddressThanBilling {
            <StackFields>
              <AddressComboBoxField
                required=true
                errorMessage=?errorMessageShippingAddress
                addressName=values.shippingAddress
                onInputChange=onChangeShippingAddress
                onRequestAutoComplete=onRequestShippingAddressAutoComplete
              />
              <Group>
                <BillingAccountForm.InputText field=ShippingPostalCode label={t("Postal code")} />
                <BillingAccountForm.InputText field=ShippingCity label={t("City")} />
              </Group>
              <BillingAccountForm.InputText field=ShippingCountry label={t("Country")} />
            </StackFields>
          } else {
            React.null
          }}
        </FieldsetLayoutPanel>
      </Stack>
    </ResourceDetailsPage>
  </BillingAccountForm.FormProvider>
}

let make = React.memo(make)

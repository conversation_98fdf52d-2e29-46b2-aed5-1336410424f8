open Intl

let printersHubName = "StarPrintersHub"

type printer = {
  id: string,
  groupId: string,
  name: string,
  hubName: string,
  macAddress: string,
}
type requestError =
  | InvalidAPIKey
  | NoPrinterFound
  | UnexpectedFailure

type printersResult = AsyncResult.t<option<array<printer>>, requestError>

let errorMessageFromError = error =>
  switch error {
  | InvalidAPIKey =>
    t("The API key is not recognized. You can try to generate a new one or contact our support.")
  | _unexpectedFailure => Request.serverErrorMessage
  }

module SettingsLabelPrinterListRequest = {
  let makeEndpoint = (~shopId) =>
    Env.gatewayUrl() ++ "/printers-hub/" ++ printersHubName ++ "/" ++ shopId ++ "/printers"

  let decodeResultItem = json => {
    let dict = json->Json.decodeDict
    let field = Json.flatDecodeDictFieldString
    let fieldObj = Json.flatDecodeDictFieldDict

    let decodeConfig = dict =>
      switch (
        dict->Json.flatDecodeDictFieldString("id"),
        dict->Json.flatDecodeDictFieldString("groupId"),
      ) {
      | (Some(id), Some(groupId)) => Some((id, groupId))
      | _ => None
      }

    switch (
      dict->field("name"),
      dict->field("hubName"),
      dict->field("macAddress"),
      decodeConfig(dict->fieldObj("config")),
    ) {
    | (Some(name), Some(hubName), Some(macAddress), Some((id, groupId))) =>
      Some({id, groupId, name, hubName, macAddress})
    | _ => None
    }
  }

  let decodeResult = json =>
    switch json->Json.decodeArray {
    | Some(resultItems) => resultItems->Array.keepMap(decodeResultItem)
    | None => []
    }

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "NotFoundShopPrintersHub"} => NoPrinterFound
    | _ => UnexpectedFailure
    }

  let mapFutureResult = futureResult =>
    futureResult
    ->Future.mapOk(decodeResult)
    ->Future.mapError(error =>
      switch error {
      | Request.InvalidRequestFailures(failures) =>
        failures[0]
        ->Option.map(decodeInvalidRequestFailure)
        ->Option.getWithDefault(UnexpectedFailure)
      | _ => UnexpectedFailure
      }
    )

  let make = (~shopId) => Request.make(makeEndpoint(~shopId), ~method=#GET)->mapFutureResult
}

module SettingsLabelDefaultPrinterIdRequest = {
  let makeEndpoint = (~shopId) =>
    Env.gatewayUrl() ++ "/printers-hub/" ++ printersHubName ++ "/" ++ shopId ++ "/default-printer"

  let decodeResult = json => {
    let dict = json->Json.decodeDict
    let fieldObj = Json.flatDecodeDictFieldDict

    let decodeConfig = dict => dict->Json.flatDecodeDictFieldString("id")

    decodeConfig(dict->fieldObj("config"))
  }

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "NotFoundShopDefaultPrinterConfig"} => NoPrinterFound
    | _ => UnexpectedFailure
    }

  let mapFutureResult = futureResult =>
    futureResult
    ->Future.mapOk(decodeResult)
    ->Future.mapError(error =>
      switch error {
      | Request.InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]
        ->Option.map(decodeInvalidRequestFailure)
        ->Option.getWithDefault(UnexpectedFailure)
      | _ => UnexpectedFailure
      }
    )

  let make = (~shopId) => Request.make(makeEndpoint(~shopId), ~method=#GET)->mapFutureResult
}

module SettingsLabelPrinterHubSetupRequest = {
  let makeEndpoint = (~shopId) =>
    Env.gatewayUrl() ++ "/printers-hub/" ++ printersHubName ++ "/" ++ shopId ++ "/setup"

  let encodeBodyJson = (~apiKey) =>
    Js.Dict.fromArray([
      (
        "printersHubConfig",
        Js.Dict.fromArray([("apiKey", apiKey->Json.encodeString)])->Json.encodeDict,
      ),
    ])->Json.encodeDict

  let decodeResultItem = json => {
    let dict = json->Json.decodeDict
    let field = Json.flatDecodeDictFieldString
    let fieldObj = Json.flatDecodeDictFieldDict

    let decodeConfig = dict =>
      switch (
        dict->Json.flatDecodeDictFieldString("id"),
        dict->Json.flatDecodeDictFieldString("groupId"),
      ) {
      | (Some(id), Some(groupId)) => Some((id, groupId))
      | _ => None
      }

    switch (
      dict->field("name"),
      dict->field("hubName"),
      dict->field("macAddress"),
      decodeConfig(dict->fieldObj("config")),
    ) {
    | (Some(name), Some(hubName), Some(macAddress), Some((id, groupId))) =>
      Some({id, groupId, name, hubName, macAddress})
    | _ => None
    }
  }

  let decodeResult = json =>
    switch json->Json.decodeArray {
    | Some(resultItems) => resultItems->Array.keepMap(decodeResultItem)
    | None => []
    }

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "InvalidPrinterHubConfig"}
    | {Request.kind: "PrintersHubPermissionsCheck"} =>
      InvalidAPIKey
    | _ => UnexpectedFailure
    }

  let mapFutureResult = futureResult =>
    futureResult
    ->Future.mapOk(decodeResult)
    ->Future.mapError(error =>
      switch error {
      | Request.InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]
        ->Option.map(decodeInvalidRequestFailure)
        ->Option.getWithDefault(UnexpectedFailure)
      | _ => UnexpectedFailure
      }
    )

  let make = (~shopId, ~apiKey) =>
    Request.make(
      makeEndpoint(~shopId),
      ~bodyJson=encodeBodyJson(~apiKey),
      ~method=#POST,
    )->mapFutureResult
}

module SettingsLabelPrinterDefaultSelectionRequest = {
  let makeEndpoint = (~shopId) =>
    Env.gatewayUrl() ++ "/printers-hub/" ++ printersHubName ++ "/" ++ shopId ++ "/default-printer"

  let encodeBodyJson = (~id, ~groupId) =>
    Js.Dict.fromArray([
      (
        "defaultPrinterDeviceConfig",
        Js.Dict.fromArray([
          ("id", id->Json.encodeString),
          ("groupId", groupId->Json.encodeString),
        ])->Json.encodeDict,
      ),
    ])->Json.encodeDict

  let mapFutureResult = futureResult =>
    futureResult->Future.map(result =>
      switch result {
      | Ok("OK") => Ok()
      | Ok(_) | Error(_) => Error()
      }
    )

  let make = (~shopId, ~id, ~groupId) =>
    switch Auth.getJwt() {
    | Some(jwt) =>
      Fetch.make(
        makeEndpoint(~shopId),
        {
          method: #POST,
          body: encodeBodyJson(~id, ~groupId)->Json.stringify->Fetch.Body.string,
          headers: Fetch.Headers.fromObject({
            "Content-Type": "application/json",
            "Authorization": `Bearer ${jwt}`,
          }),
        },
      )
      ->Promise.then(Fetch.Response.text)
      ->Promise.then(text => Promise.resolve(text))
      ->FuturePromise.fromPromise
      ->Future.mapError(_ => UnexpectedFailure)
    | None => Future.value(Error(UnexpectedFailure))
    }->mapFutureResult
}

module SettingsLabelPrinterApiKeyEdit = {
  let starPrinterUrl = Url.make("https://portal.starprinter.online/Dashboard")
  let secretTextPlaceholder = "• • • • • • • • • • • • • • • • • • • • • • • • • • •"

  @react.component
  let make = (~editionStage=false, ~loading=false, ~onSubmit) => {
    let (apiKey, setApiKey) = React.useState(() => "")
    let (editionModalOpened, setEditionModalOpened) = React.useState(() => false)

    if !editionStage {
      <>
        <InputTextField
          label={t("StarPrinter Online API key")}
          placeholder={t("Enter API key")}
          value=apiKey
          onChange={value => setApiKey(_ => value)}
        />
        <Box spaceTop=#xlarge>
          <Inline align=#spaceBetween alignY=#top>
            <TextLink
              text="StarPrinter Online Dashboard"
              to=Url(starPrinterUrl)
              openNewTab=true
              highlighted=true
              icon=#external_link
            />
            <Button
              variation=#success
              size=#xsmall
              loading
              disabled={apiKey === ""}
              onPress={_ => {
                onSubmit(apiKey)
                setApiKey(_ => "")
              }}>
              {("   " ++ t("Save") ++ "   ")->React.string}
            </Button>
          </Inline>
        </Box>
      </>
    } else {
      <Stack space=#none>
        <Label text={t("StarPrinter Online API key")} />
        <View style={Style.style(~marginTop=-8.->Style.dp, ())}>
          <Inline alignY=#center>
            <TextStyle size=#xlarge weight=#semibold>
              {secretTextPlaceholder->React.string}
            </TextStyle>
            <IconButton
              marginSize=#small
              size=18.
              name=#edit_light
              onPress={_ => setEditionModalOpened(_ => true)}
              color=Colors.neutralColor100
              hoveredColor=Colors.brandColor60
            />
            <Modal
              title={t("Edit StarPrinter Online API key")}
              opened=editionModalOpened
              allowCommit={apiKey !== ""}
              abortButtonText={t("Cancel")}
              commitButtonText={t("Save")}
              commitButtonCallback={_ => onSubmit(apiKey)}
              onRequestClose={_ => {
                setEditionModalOpened(_ => false)
                setApiKey(_ => "")
              }}>
              <Box spaceTop=#xlarge spaceBottom=#xmedium spaceX=#xlarge>
                <InputTextField
                  label={t("StarPrinter Online API key")}
                  placeholder={t("Enter API key")}
                  autoFocus=editionModalOpened
                  value=apiKey
                  onChange={value => setApiKey(_ => value)}
                />
              </Box>
            </Modal>
          </Inline>
        </View>
      </Stack>
    }
  }
}

module SettingsLabelPrinterDefaultSelection = {
  let starPrinterUrl = Url.make("https://portal.starprinter.online/Dashboard")

  let formatPrinterLabel = (~name, ~macAddress) =>
    switch name {
    | "" => `${t("Anonymous")} (${macAddress})`
    | name => `${name} (${macAddress})`
    }

  @react.component
  let make = (~loading=false, ~printers, ~defaultPrinterId, ~onSubmit) => {
    let defaultPrinter = printers->Array.getBy(printer => Some(printer.id) === defaultPrinterId)
    let (pickedPrinter, setPickedPrinter) = React.useState(_ => defaultPrinter)

    switch printers {
    | [printer] =>
      <Inline space=#medium>
        <Title level=#4 weight=#strong>
          {formatPrinterLabel(~name=printer.name, ~macAddress=printer.macAddress)->React.string}
        </Title>
        <TextLink
          text="StarPrinter Online Dashboard"
          to=Url(starPrinterUrl)
          openNewTab=true
          highlighted=true
          icon=#external_link
        />
      </Inline>
    | arrayPrinters =>
      <Stack>
        <InputSelectField
          label={t("Label printer")}
          placeholder={t("Select a printer")}
          sections={
            open Select
            let items = arrayPrinters->Array.map(printer => {
              label: formatPrinterLabel(~name=printer.name, ~macAddress=printer.macAddress),
              key: printer.id,
              value: Some(printer),
            })
            [{title: t("Printers"), items}]
          }
          value=pickedPrinter
          onChange={printer => setPickedPrinter(_ => printer)}
        />
        <Box spaceTop=#xlarge>
          <Inline align=#spaceBetween alignY=#top>
            <TextLink
              text="StarPrinter Online Dashboard"
              to=Url(starPrinterUrl)
              openNewTab=true
              highlighted=true
              icon=#external_link
            />
            <Button
              variation=#success
              size=#xsmall
              loading
              disabled={Option.isNone(pickedPrinter)}
              onPress={_ =>
                switch pickedPrinter {
                | Some(printer) => onSubmit(printer, printers)
                | None => ()
                }}>
              {t("Save")->React.string}
            </Button>
          </Inline>
        </Box>
      </Stack>
    }
  }
}

module SettingsLabelPrinterDocumentationCard = {
  @react.component
  let make = () =>
    <Card title={t("What is an API key?")}>
      <Stack space=#medium>
        <TextStyle>
          {t(
            "This is a unique code that you provide to us so that we can access your Star printer's online functions securely.",
          )->React.string}
        </TextStyle>
        <Stack space=#small>
          <TextAction
            text={t(
              "Consult the tutorial dedicated to Star printer configuration and StarPrinter Online",
            )}
            highlighted=true
            onPress={() => HelpCenter.showArticle(HelpCenter.setupLabelPrinterAndStarPrinterOnline)}
          />
        </Stack>
      </Stack>
    </Card>
}

@react.component
let make = () => {
  let (printersResult, setPrintersResult) = React.useState(_ => AsyncData.NotAsked)
  let (defaultPrinterId, setDefaultPrinterId) = React.useState(_ => None)
  let (notification, setNotification) = React.useState(_ => None)

  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()->Option.getWithDefault(shops->Array.getUnsafe(0))
  let organisationAccount = switch Auth.useScope() {
  | Organisation(_) => true
  | Single(_) => false
  }

  let requestPrintersList = () =>
    SettingsLabelPrinterListRequest.make(~shopId=activeShop.id)->Future.tap(result =>
      switch result {
      | Ok(printers) => setPrintersResult(_ => Done(Ok(Some(printers))))
      | Error(error) =>
        switch error {
        | NoPrinterFound => setPrintersResult(_ => Done(Ok(None)))
        | error => setNotification(_ => Some(Error(errorMessageFromError(error))))
        }
      }
    )

  React.useEffect1(() => {
    switch printersResult {
    | NotAsked =>
      let executedPrintersListFuture = requestPrintersList()
      let defaultPrinterFuture = SettingsLabelDefaultPrinterIdRequest.make(~shopId=activeShop.id)

      defaultPrinterFuture->Future.tapOk(id => setDefaultPrinterId(_ => id))->ignore

      Some(
        () => {
          Future.cancel(executedPrintersListFuture)
          Future.cancel(defaultPrinterFuture)
        },
      )
    | _ => None
    }
  }, [printersResult])

  ReactUpdateEffect.use1(() => {
    setNotification(_ => None)
    setPrintersResult(_ => Loading)

    let executedFuture = requestPrintersList()

    Some(() => Future.cancel(executedFuture))
  }, [activeShop])

  let handleApiKeyChange = apiKey => {
    let printers = printersResult->AsyncData.getWithDefault(Ok(None))

    setPrintersResult(_ => Reloading(printers))
    SettingsLabelPrinterHubSetupRequest.make(~shopId=activeShop.id, ~apiKey)
    ->Future.flatMapOk(printers => {
      switch printers {
      | [printer] =>
        SettingsLabelPrinterDefaultSelectionRequest.make(
          ~shopId=activeShop.id,
          ~id=printer.id,
          ~groupId=printer.groupId,
        )
        ->Future.mapOk(_ => printers)
        ->Future.mapError(_ => UnexpectedFailure)
      | _ => Future.value(Ok(printers))
      }
    })
    ->Future.get(result =>
      switch result {
      | Ok(latestPrinters) =>
        setPrintersResult(_ => Done(Ok(Some(latestPrinters))))
        setNotification(_ => Some(Ok(t("The printer has been successfully registered."))))
      | Error(error) =>
        setPrintersResult(_ => Done(printers))
        setNotification(_ => Some(Error(errorMessageFromError(error))))
      }
    )
  }

  let handleDefaultPrinterSelection = (printer, printers) => {
    setPrintersResult(_ => Reloading(Ok(Some(printers))))

    SettingsLabelPrinterDefaultSelectionRequest.make(
      ~shopId=activeShop.id,
      ~id=printer.id,
      ~groupId=printer.groupId,
    )
    ->Future.flatMap(result =>
      switch result {
      | Ok(_) => SettingsLabelDefaultPrinterIdRequest.make(~shopId=activeShop.id)
      | Error() => Future.value(Error(UnexpectedFailure))
      }
    )
    ->Future.get(result => {
      switch result {
      | Ok(defaultPrinterId) =>
        setDefaultPrinterId(_ => defaultPrinterId)
        setNotification(_ => Some(Ok(t("The default printer has been successfully registered."))))
      | Error(error) => setNotification(_ => Some(Error(errorMessageFromError(error))))
      }
      setPrintersResult(_ => Done(Ok(Some(printers))))
    })
  }

  let renderHeaderActions = () =>
    if organisationAccount {
      <Inline>
        <Auth.SelectSingleShopFilter value=activeShop />
      </Inline>
    } else {
      React.null
    }

  let notificationBanner = switch notification {
  | Some(value) =>
    <Box spaceTop=#normal>
      <Banner
        onRequestClose={_ => setNotification(_ => None)}
        textStatus={switch value {
        | Ok(ok) => Success(ok)
        | Error(error) => Banner.Danger(error)
        }}
      />
    </Box>
  | None => React.null
  }

  <Page title={t("Label printer")} renderHeaderActions variation=#compact>
    {notificationBanner}
    <Box spaceTop=#large>
      <Stack space=#xlarge>
        <FieldsetLayoutPanel
          title={t("Configuration")}
          description={t("To connect your printer, enter the StarPrinter Online API key.")}>
          {switch printersResult {
          | Done(Ok(None)) | Reloading(Ok(None)) =>
            let loading = AsyncData.isBusy(printersResult)

            <SettingsLabelPrinterApiKeyEdit loading onSubmit=handleApiKeyChange />
          | Done(Ok(Some(printers))) | Reloading(Ok(Some(printers))) =>
            let loading = AsyncData.isBusy(printersResult)

            <Stack space=#xnormal>
              <SettingsLabelPrinterApiKeyEdit
                loading editionStage=true onSubmit=handleApiKeyChange
              />
              <SettingsLabelPrinterDefaultSelection
                loading printers defaultPrinterId onSubmit=handleDefaultPrinterSelection
              />
            </Stack>
          | NotAsked | Loading | Reloading(Error(_)) => <Placeholder status=Loading />
          | Done(Error(_)) => <Placeholder status=Error />
          }}
        </FieldsetLayoutPanel>
        <Columns space=#xlarge>
          <Column width=#quarter> React.null </Column>
          <Column width=#three_quarter>
            <SettingsLabelPrinterDocumentationCard />
          </Column>
        </Columns>
      </Stack>
    </Box>
  </Page>
}

let make = React.memo(make)

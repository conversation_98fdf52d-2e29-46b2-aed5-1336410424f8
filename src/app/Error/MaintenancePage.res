open Intl
open Style

@module("./maintenance.png") external imageUri: string = "default"

let styles = StyleSheet.create({
  "container": style(~flex=1., ~justifyContent=#center, ~alignSelf=#center, ~width=450.->dp, ()),
  "image": style(
    ~height=186.->dp,
    ~width=544.->dp,
    ~alignSelf=#center,
    ~marginTop=-150.->dp,
    ~marginBottom=Spaces.xlarge->dp,
    (),
  ),
  "action": style(~alignSelf=#center, ~paddingTop=Spaces.large->dp, ()),
})

@react.component
let make = () =>
  <View style={styles["container"]}>
    <Image style={styles["image"]} source={Image.uriSource(imageUri)->Image.fromUriSource} />
    <Stack space=#normal>
      <Title align=#center>
        {t("The dashboard is currently unavailable due to a maintenance.")->React.string}
      </Title>
      <TextStyle align=#center size=#huge>
        {t("<PERSON><PERSON> will be back soon, thank you for your patience.")->React.string}
      </TextStyle>
      <View style={styles["action"]}>
        <Button onPress={_ => WebAPI.location->WebAPI.Location.reload}>
          {t("Refresh the page")->React.string}
        </Button>
      </View>
    </Stack>
  </View>

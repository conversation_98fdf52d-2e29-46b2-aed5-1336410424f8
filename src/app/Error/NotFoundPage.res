open Intl
open Style

@module("./not_found.png") external imageUri: string = "default"

let styles = StyleSheet.create({
  "container": style(~flex=1., ~justifyContent=#center, ~alignSelf=#center, ~width=450.->dp, ()),
  "image": style(
    ~height=186.->dp,
    ~width=544.->dp,
    ~alignSelf=#center,
    ~marginTop=-150.->dp,
    ~marginBottom=Spaces.xlarge->dp,
    (),
  ),
  "action": style(~alignSelf=#center, ~paddingTop=Spaces.large->dp, ()),
})

@react.component
let make = () => {
  let (canGoBack, onGoBack) = Navigation.useGoBack()

  <View style={styles["container"]}>
    <Image style={styles["image"]} source={Image.uriSource(imageUri)->Image.fromUriSource} />
    <Stack space=#normal>
      <Title align=#center>
        {t("Ooops ! This page doesn't seem available now.")->React.string}
      </Title>
      <TextStyle align=#center size=#huge>
        {t("Please try refreshing the page.")->React.string}
      </TextStyle>
      <View style={styles["action"]}>
        <Button onPress={_ => canGoBack ? onGoBack() : ()}>
          {t("Go back to the previous page")->React.string}
        </Button>
      </View>
    </Stack>
  </View>
}

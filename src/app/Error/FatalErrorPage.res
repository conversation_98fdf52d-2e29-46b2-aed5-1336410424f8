open Intl
open Style

@module("./fatal_error.png") external imageUri: string = "default"

let styles = StyleSheet.create({
  "container": style(~flex=1., ~justifyContent=#center, ~alignSelf=#center, ~width=450.->dp, ()),
  "image": style(
    ~height=186.->dp,
    ~width=544.->dp,
    ~alignSelf=#center,
    ~marginTop=-150.->dp,
    ~marginBottom=Spaces.xlarge->dp,
    (),
  ),
  "action": style(~alignSelf=#center, ~paddingTop=Spaces.large->dp, ()),
})

let onPress = %raw(` () => window.location.replace('/') `)

@react.component
let make = () =>
  <View style={styles["container"]}>
    <Image style={styles["image"]} source={Image.fromUriSource(Image.uriSource(imageUri))} />
    <Stack space=#normal>
      <Title align=#center>
        {t("Ooops ! Looks like something went wrong here.")->React.string}
      </Title>
      <TextStyle align=#center size=#huge>
        {t("Please try refreshing the page.")->React.string}
      </TextStyle>
      <View style={styles["action"]}>
        <Button onPress> {t("Go back to the home page")->React.string} </Button>
      </View>
    </Stack>
  </View>

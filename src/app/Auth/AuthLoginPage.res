open Intl
open Style

module LoginFormLenses = %lenses(
  type state = {
    username: string,
    password: string,
    keepSigned: bool,
  }
)

module LoginForm = Form.Make(LoginFormLenses)

module SignInRequest = {
  let encodeBody = (~username, ~password) =>
    Js.Dict.fromArray([
      ("username", username->Json.encodeString),
      ("password", password->Json.encodeString),
    ])
    ->Json.encodeDict
    ->Json.stringify
    ->Fetch.Body.string

  let decodeResult = json => json->Json.decodeDict->Json.flatDecodeDictFieldString("access_token")

  type makeT = (~username: string, ~password: string) => Future.t<result<string, unit>>

  let make: makeT = (~username, ~password) =>
    Fetch.make(
      Env.gatewayUrl() ++ "/sign-in",
      {
        method: #POST,
        body: encodeBody(~username, ~password),
        headers: Fetch.Headers.fromObject({"Content-Type": "application/json"}),
      },
    )
    ->Promise.then(Fetch.Response.json)
    ->Promise.then(json => Promise.resolve(decodeResult(json)))
    ->FuturePromise.fromPromise
    ->Future.map(result =>
      switch result {
      | Ok(Some(jwt)) => Ok(jwt)
      | Ok(None) | Error(_) => Error()
      }
    )
}

let signInRequest = SignInRequest.make

module RecoveryLinkButton = {
  let styles = StyleSheet.create({
    "root": Style.merge([
      FontFaces.libreFranklinRegularStyle,
      Style.style(~color=Colors.neutralColor70, ~textDecorationLine=#underline, ()),
      FontSizes.styleFromSize(#xsmall),
    ]),
  })

  @react.component
  let make = (~recoveryRoute) =>
    <Navigation.Link to=Route(recoveryRoute)>
      <Text style={styles["root"]}> {t("Forgot password ?")->React.string} </Text>
    </Navigation.Link>
}

module NotificationBanner = {
  @react.component
  let make = (~notification, ~onRequestClose) =>
    <Box spaceTop=#medium style={style(~width=100.->pct, ())}>
      <Banner textStatus=notification onRequestClose />
    </Box>

  let make = React.memo(make)
}

let styles = StyleSheet.create({
  "container": style(
    ~flex=1.,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~width=465.->dp,
    ~alignItems=#stretch,
    ~marginTop=-10.->pct,
    (),
  ),
  "title": style(~width=100.->pct, ~alignItems=#center, ~paddingVertical=Spaces.large->dp, ()),
})

let noop = _ => ()

let schema = [LoginForm.Schema.Email(Username), StringMin(Password, 3)]

@react.component
let make = (
  ~signInRequest: SignInRequest.makeT,
  ~initialNotification=?,
  ~recoveryRoute,
  ~initialUsername="",
) => {
  let authState = Auth.useState()
  let logUser = Auth.useLogUser()

  let (notification, setNotfication) = React.useState(() => initialNotification)

  let onSubmitSuccess = jwt =>
    switch jwt {
    | Some(jwt) => logUser(jwt)
    | _ => setNotfication(_ => Some(Banner.Danger(t("Something went wrong"))))
    }

  let onSubmitFailure = error => setNotfication(_ => Some(Banner.Danger(error)))

  let formPropState = LoginForm.useFormPropState({
    initialValues: {username: initialUsername, password: "", keepSigned: true},
    schema,
    onSubmitSuccess,
    onSubmitFailure,
  })

  let onSubmit = (_, {LoginFormLenses.username: username, password}) =>
    signInRequest(~username, ~password)
    ->Future.mapOk(jwt => Some(jwt))
    ->Future.mapError(_ => t("Incorrect credentials."))

  let onRequestCloseNotificationBanner = () => setNotfication(_ => None)

  <View style={styles["container"]}>
    <View style={styles["title"]}>
      <Title level=#1> {t("Good to see you again !")->React.string} </Title>
      {switch notification {
      | Some(notification) =>
        <NotificationBanner notification onRequestClose=onRequestCloseNotificationBanner />
      | None => React.null
      }}
    </View>
    <LoginForm.FormProvider propState=formPropState>
      <LoginForm.ControlEnterKey onSubmit />
      <Stack space=#large>
        <LoginForm.InputText
          field=Username
          label={t("Email")}
          placeholder={t("<EMAIL>")}
          hideRequired=true
        />
        <LoginForm.InputPassword
          field=Password
          label={t("Password")}
          placeholder={t("Password")}
          hideError=true
          hideRequired=true
        />
        <Box spaceY=#xsmall>
          {switch authState {
          | Unlogged => <LoginForm.SubmitButton text={t("Sign in")} onSubmit />
          | Logging(_) | Logged(_) =>
            <Button variation=#primary size=#large disabled=true onPress={noop}>
              {(t("Sign in") ++ "...")->React.string}
            </Button>
          }}
        </Box>
      </Stack>
      <RecoveryLinkButton recoveryRoute />
    </LoginForm.FormProvider>
  </View>
}

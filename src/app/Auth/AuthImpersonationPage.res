open Style

module UserImpersonationSignInRequest = {
  let encodeBodyJson = (~userId) =>
    Js.Dict.fromArray([("userId", userId->Json.encodeString)])->Json.encodeDict

  let decodeResult = json =>
    switch json->Json.decodeDict->Json.flatDecodeDictFieldString("access_token") {
    | Some(jwt) => Ok(jwt)
    | None => Error()
    }

  let endpoint = Env.gatewayUrl() ++ "/sign-in/impersonate"

  let make = (~jwt, ~userId) => {
    let headers = {"Content-Type": "application/json", "Authorization": `Bearer ${jwt}`}
    Fetch.make(
      endpoint,
      {
        method: #POST,
        body: encodeBodyJson(~userId)->Json.stringify->Fetch.Body.string,
        headers: Fetch.Headers.fromObject(headers),
      },
    )
    ->Promise.then(Fetch.Response.json)
    ->Promise.then(json => Promise.resolve(decodeResult(json)))
    ->FuturePromise.fromPromise
    ->Future.map(result =>
      switch result {
      | Ok(Ok(accessToken)) => Ok(accessToken)
      | _ => Error()
      }
    )
  }
}

let styles = StyleSheet.create({
  "container": style(
    ~flex=1.,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    (),
  ),
})

@react.component
let make = (~userId, ~authLogoutImpersonationFailureRoute, ~appIndexRoute) => {
  let logUser = Auth.useLogUser(~impersonate=true, ())
  let auth = Auth.useState()

  let navigate = Navigation.useNavigate()

  let futureJwt = switch Auth.getJwt() {
  | Some(jwt) => Future.value(Ok(jwt))
  | None => Future.value(Error())
  }

  React.useEffect1(() => {
    switch auth {
    | Logged({user: {id, impersonating: true}}) if id === userId => navigate(appIndexRoute)
    | Logged({user: {id, impersonating: false, canUseImpersonation: true}}) if id !== userId =>
      futureJwt
      ->Future.flatMapOk(jwt => UserImpersonationSignInRequest.make(~userId, ~jwt))
      ->Future.get(result =>
        switch result {
        | Ok(jwt) => logUser(jwt)
        | Error() => navigate(authLogoutImpersonationFailureRoute)
        }
      )
    | Unlogged => navigate(authLogoutImpersonationFailureRoute)
    | _ => ()
    }
    None
  }, [auth])

  <View style={styles["container"]}>
    <Spinner />
  </View>
}

let make = React.memo(make)

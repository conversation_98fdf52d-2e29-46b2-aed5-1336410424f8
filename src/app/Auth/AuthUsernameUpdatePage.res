open Intl
open Style

let styles = StyleSheet.create({
  "container": style(
    ~flex=1.,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~width=465.->dp,
    ~marginTop=-10.->pct,
    (),
  ),
  "title": style(~width=100.->pct, ~alignItems=#center, ~paddingVertical=Spaces.large->dp, ()),
})

module AuthUsernameUpdateRequest = {
  let endpoint = Env.gatewayUrl() ++ "/auth/username"

  let encodeBody = (~tokenId, ~token, ~userId, ~username) =>
    Js.Dict.fromArray([
      ("token", token->Json.encodeString),
      ("tokenId", tokenId->Json.encodeString),
      ("userId", userId->Json.encodeString),
      ("username", username->Json.encodeString),
    ])->Json.encodeDict

  type serverFailure =
    | ExpiredOneTimeToken
    | NotFoundUsernameUpdateOneTimeToken
    | DuplicateUserUsername
    | UnknownServerFailure

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "ExpiredOneTimeToken"} => ExpiredOneTimeToken
    | {kind: "NotFoundUsernameUpdateOneTimeToken"} => NotFoundUsernameUpdateOneTimeToken
    | {kind: "DuplicateUserUsername"} => DuplicateUserUsername
    | _ => UnknownServerFailure
    }

  type makeT = (
    ~token: string,
    ~tokenId: string,
    ~userId: string,
    ~username: string,
  ) => Future.t<result<Json.t, option<serverFailure>>>

  let make = (~token, ~tokenId, ~userId, ~username) =>
    Request.make(
      endpoint,
      ~method=#PATCH,
      ~bodyJson=encodeBody(~token, ~tokenId, ~userId, ~username),
      ~authTokenRequired=false,
    )->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
}

let usernameUpdateRequest = AuthUsernameUpdateRequest.make

module NotificationBanner = {
  @react.component
  let make = (~notification, ~onRequestClose) =>
    <Box spaceTop=#medium style={style(~width=100.->pct, ())}>
      <Banner textStatus=notification onRequestClose />
    </Box>

  let make = React.memo(make)
}

@react.component
let make = (
  ~userId,
  ~tokenId,
  ~token,
  ~username,
  ~usernameUpdateRequest: AuthUsernameUpdateRequest.makeT,
) => {
  let navigate = Navigation.useNavigate()
  let auth = Auth.useState()
  let unlogUser = Auth.useUnlogUser()

  let (notification, setNotfication) = React.useState(() => None)
  let (redirectToLogin, setRedirectToLogin) = React.useState(() => false)

  React.useEffect0(() => {
    usernameUpdateRequest(~tokenId, ~token, ~userId, ~username)->Future.get(result =>
      switch result {
      | Ok(_) => setRedirectToLogin(_ => true)
      | Error(Some(ExpiredOneTimeToken | NotFoundUsernameUpdateOneTimeToken)) =>
        setNotfication(_ => Some(Banner.Danger(t("The link has expired"))))
      | Error(Some(DuplicateUserUsername)) =>
        setNotfication(_ => Some(Banner.Danger(t("This email address is already used."))))
      | Error(None | Some(UnknownServerFailure)) =>
        let danger = t("An unexpected error occured. Please try again or contact the support.")
        setNotfication(_ => Some(Banner.Danger(danger)))
      }
    )

    None
  })

  React.useEffect2(() => {
    if redirectToLogin {
      switch auth {
      | Logged(_) =>
        unlogUser()
        navigate(AuthRoutes.loginUsernameUpdateSuccessRoute ++ "/" ++ username)
      | Unlogged => navigate(AuthRoutes.loginUsernameUpdateSuccessRoute ++ "/" ++ username ++ "/")
      | Logging(_) => ()
      }
    }

    None
  }, (redirectToLogin, auth))

  let onRequestCloseNotificationBanner = () => setNotfication(_ => None)

  let onPressBackButton = _ => navigate(AuthRoutes.baseRoute)

  <View style={styles["container"]}>
    <View style={styles["title"]}>
      <Title level=#1> {t("Email update")->React.string} </Title>
    </View>
    {switch notification {
    | Some(notification) =>
      <NotificationBanner notification onRequestClose=onRequestCloseNotificationBanner />
    | None => <Spinner size=18. />
    }}
    <Box spaceTop=#xxlarge>
      <Button onPress=onPressBackButton> {t("Back")->React.string} </Button>
    </Box>
  </View>
}

open Intl
open Style

module GettingStartedTextIconLink = {
  let styles = StyleSheet.create({
    "base": merge([
      FontFaces.libreFranklinSemiBoldStyle,
      style(~fontSize=FontSizes.normal, ~color=Colors.neutralColor90, ~letterSpacing=0.125, ()),
    ]),
    "hovered": style(~color=Colors.brandColor50, ~textDecorationLine=#underline, ()),
  })

  let styleFromParams = hovered =>
    [styles["base"], hovered ? styles["hovered"] : emptyStyle]->arrayStyle

  let iconColorFromHovered = (~hovered) => hovered ? Colors.brandColor60 : Colors.neutralColor100

  @react.component
  let make = () => {
    let (ref, hovered) = Hover.use()
    <Navigation.Link
      to=Navigation.Url(Url.make(HelpCenter.gettingStartedGuideLink)) openNewTab=true>
      <Box ref>
        <Inline space=#small>
          <Icon name=#book_bold fill={iconColorFromHovered(~hovered)} />
          <Text style={styleFromParams(hovered)}> {t("Getting started guide")->React.string} </Text>
        </Inline>
      </Box>
    </Navigation.Link>
  }
}

module Header = {
  @module("./wino_logo.png") external imageUri: string = "default"

  let styles = StyleSheet.create({
    "image": style(~height=40.->dp, ~width=40.->dp, ~alignSelf=#center, ()),
  })

  @react.component
  let make = () =>
    <Inline align=#spaceBetween>
      <Box>
        <Inline space=#medium>
          <Image style={styles["image"]} source={Image.uriSource(imageUri)->Image.fromUriSource} />
          <TextStyle size=#normal>
            {t("How to contact us? By <NAME_EMAIL>")->React.string}
          </TextStyle>
        </Inline>
      </Box>
      <Box>
        <GettingStartedTextIconLink />
      </Box>
    </Inline>
}

let styles = StyleSheet.create({
  "container": style(
    ~width=100.->pct,
    ~height=100.->pct,
    ~maxWidth=1480.->dp,
    ~paddingTop=Spaces.xlarge->dp,
    ~paddingBottom=Spaces.xxhuge->dp,
    ~paddingHorizontal=Spaces.xlarge->dp,
    (),
  ),
  "content": style(
    ~alignSelf=#center,
    ~justifyContent=#center,
    ~height=75.->pct,
    ~maxWidth=724.->dp,
    (),
  ),
})

let textConfirmation = t(
  "A confirmation email will be sent to you in the next few minutes. Our team will contact you soon to set up the software. You can now close this window.",
)

@react.component
let make = () =>
  <View style={styles["container"]}>
    <Header />
    <Box style={styles["content"]}>
      <Stack align=#center space=#normal>
        <Title level=#1 align=#center>
          {t("🎉 Your account has been created successfully!")->React.string}
        </Title>
        <TextStyle align=#center variation=#normal> {textConfirmation->React.string} </TextStyle>
      </Stack>
    </Box>
  </View>

let make = React.memo(make)

open Intl

module Data = {
  module Queries = PromotionEdit__Queries
  module PromotionByCkuQuery = Queries.PromotionByCkuQuery
  module PromotionDiscountsQuery = Queries.PromotionDiscountsQuery

  exception PromotionEditPage_InitialValuesError

  type queryData = {
    campaigns: array<PromotionByCkuQuery.PromotionByCkuQuery_inner.t_promotionCampaignsByCku_edges>,
    discounts: array<
      PromotionDiscountsQuery.PromotionDiscountsQuery_inner.t_promotionCampaign_discounts_edges,
    >,
  }

  type initialData = {
    form: (
      PromotionEditForm.Core.state,
      ReactUpdateReducer.dispatch<PromotionEditForm.Core.action>,
    ),
    query: option<queryData>,
  }

  type t =
    | Loading
    | Data(initialData)
    | Errored

  let useInitial = (~cku) => {
    let notifier = Notifier.use()
    let navigate = Navigation.useNavigate()
    let apolloClient = ApolloClient.React.useApolloClient()
    let (executeCampaignsQuery, queryCampaignsResults) = PromotionByCkuQuery.useLazy()
    let (discountsStatus, setDiscountsStatus) = React.useState(() => Queries.Loading)
    let (errored, setErrored) = React.useState(() => false)

    // Fetches all campaigns bound to the cku
    React.useEffect1(() => {
      switch cku {
      | Some(cku) => executeCampaignsQuery({cku: cku->Scalar.CKU.serialize})
      | _ => ()
      }
      None
    }, [cku])

    // Fetches all discounts from the first campaign of the set (common to all others)
    React.useEffect1(() => {
      let campaignsData = switch queryCampaignsResults {
      | Executed({data: Some({promotionCampaignsByCku: campaigns})}) => Some(campaigns)
      | Executed({error: Some(_)}) =>
        setErrored(_ => true)
        None
      | _ => None
      }

      switch campaignsData->Option.flatMap(({edges}) => edges[0]) {
      | Some({node: campaign}) =>
        let discountsFetch = (~after) =>
          apolloClient.query(
            ~query=module(PromotionDiscountsQuery),
            ~fetchPolicy=NetworkOnly,
            PromotionDiscountsQuery.makeVariables(
              ~after?,
              ~id=campaign.id,
              ~variantPricesFilterBy=PromotionDiscountsQuery.makeInputObjectInputVariantVariantPricesFilter(
                ~priceId=PromotionDiscountsQuery.makeInputObjectStringEqualsFilter(
                  ~_equals=campaign.price.id,
                  (),
                ),
                (),
              ),
              (),
            ),
          )

        Queries.runScanDiscounts(
          ~discountsFetch,
          ~onDone=result => setDiscountsStatus(_ => result),
          (),
        )
      | _ => ()
      }
      None
    }, [queryCampaignsResults])

    // Extracts data when campaigns and discounts are both entirely fetched
    let data = React.useMemo2(() =>
      switch (queryCampaignsResults, discountsStatus) {
      | (
          Executed({data: Some({promotionCampaignsByCku: {edges: campaigns}})}),
          Success(discounts),
        ) =>
        Some({
          campaigns,
          discounts,
        })
      | (_, Error)
      | (Executed({error: Some(_)}), _) =>
        setErrored(_ => true)
        None
      | _ => None
      }
    , (queryCampaignsResults, discountsStatus))

    // Form initial values based on new or fetched campaigns
    let initialValues = React.useMemo1(() =>
      switch (data, data->Option.flatMap(data => data.campaigns[0])) {
      | (Some(_), None) => raise(PromotionEditPage_InitialValuesError)
      | (Some({campaigns, discounts}), Some({node: campaign})) => {
          PromotionEditForm.Lenses.discountedProducts: discounts->Array.map(({node: discount}) => {
            PromotionEditDiscountedProduct.id: discount.variant.id,
            cku: discount.variant.cku,
            name: discount.variant.formattedName,
            description: discount.variant.formattedDescription,
            stockKeepingUnit: discount.variant.stockKeepingUnit,
            purchasePrice: discount.variant.purchasedPrice->Option.getWithDefault(0.),
            bulkUnit: switch (
              discount.variant.capacityUnit,
              discount.variant.bulk->Option.getWithDefault(false),
            ) {
            | (Some(unit), true) => Some(unit)
            | _ => None
            },
            retailPrices: Some(
              discount.variant.variantPrices.edges->Array.map(
                ({node: variantPrice}) => {
                  PromotionEditDiscountedProduct.ProductPrice.name: campaign.price.name,
                  value: variantPrice.valueIncludingTax,
                },
              ),
            ),
            discount: {
              id: discount.id,
              amount: discount.value,
              kind: discount.kind,
            },
          }),
          name: campaign.name,
          priceName: Some(campaign.price.name),
          period: Some((campaign.startDate, campaign.endDate)),
          rootCampaigns: campaigns->Array.map(({node: campaign}) => {
            PromotionEditForm.RootCampaign.id: campaign.id,
            creatorIdentifier: campaign.creatorIdentifier,
            shopId: campaign.shop.id,
            shopName: campaign.shop.name,
            priceId: campaign.price.id,
            status: Some(campaign.formattedStatus),
            selected: true,
          }),
        }
      | _ => {
          discountedProducts: [],
          name: "",
          priceName: None,
          period: None,
          rootCampaigns: [],
        }
      }
    , [data])

    let onSubmitSuccess = React.useCallback1(submissionResponse =>
      switch (submissionResponse, cku) {
      | (Some(cku), None) =>
        navigate(Promotion->LegacyRouter.routeToPathname ++ ("/" ++ cku), ~replace=true)
      | (Some(cku), Some(_)) => executeCampaignsQuery({cku: cku->Scalar.CKU.serialize})
      | _ => ()
      }
    , [cku])
    let onSubmitFailure = React.useCallback0(message => notifier.reset(Error(message), ()))

    // Form state creation
    let formId = React.useMemo1(() => data->Option.isSome ? cku : None, [data->Option.isSome])
    let (state, dispatch) = PromotionEditForm.useFormPropState({
      initialValues,
      id: ?formId,
      schema: PromotionEditForm.schema,
      onSubmitSuccess,
      onSubmitFailure,
    })

    // Updates form values upon new fetchs
    ReactUpdateEffect.use1(() => {
      if cku->Option.isSome {
        IdAndInitialValuesUpdated(cku, initialValues)->dispatch
      }
      None
    }, [initialValues])

    // Returns the form and the raw data from queries when ready
    switch (cku, data, state.values, errored) {
    | (None, _, _, false)
    | (Some(_), Some(_), _, false) =>
      Data({
        form: (state, dispatch),
        query: data,
      })
    | (_, _, _, true) => Errored
    | _ => Loading
    }
  }
}

@react.component
let make = (~cku=?, ~canShopKindEdit) => {
  let initialData = Data.useInitial(~cku)
  let notifier = Notifier.use()
  let promotionCreated = cku->Option.isSome

  let (editing, setEditing) = React.useState(() => !promotionCreated)

  // Unauthorized types of accounts for mutations
  let readOnly = {
    let adminUser = switch Auth.useScope() {
    | Organisation(_) => true
    | _ => false
    }
    let readOnlyShopType = !canShopKindEdit
    let promotionNotCreatedByUser = switch (Auth.useState(), initialData) {
    | (Logged({user}), Data({form: ({values}, _)})) =>
      values.rootCampaigns->Array.some(campaign =>
        campaign.creatorIdentifier !== user.id && campaign.status !== None
      )
    | _ => promotionCreated
    }

    !adminUser && (readOnlyShopType || promotionNotCreatedByUser)
  }

  // Updates form discounted products upon table addition
  let onRequestProductsUpdate = React.useCallback1(products =>
    switch initialData {
    | Data({form: (state, dispatch)}) =>
      if state.values.discountedProducts != products {
        FieldValueChanged(DiscountedProducts, _ => products)->dispatch
      }
    | _ => ()
    }
  , [initialData])

  // Updates form shops campaigns data upon table selection
  let onRequestShopsUpdate = React.useCallback2(shops =>
    switch (initialData, editing) {
    | (Data({form: (state, dispatch)}), true) =>
      if state.values.rootCampaigns != shops {
        FieldValueChanged(RootCampaigns, _ => shops)->dispatch
      }
    | _ => ()
    }
  , (initialData, editing))

  // Gets the current campaign status
  // NOTE - use only for common statuses between all shops
  let formattedStatus = switch (promotionCreated, initialData) {
  | (true, Data({query: Some({campaigns})})) =>
    campaigns[0]->Option.mapWithDefault(#DRAFT, ({node: campaign}) => campaign.formattedStatus)
  | _ => #DRAFT
  }

  let onRequestCsvDownload = promotionCampaign => {
    let discounts =
      promotionCampaign.PromotionEditForm.Lenses.discountedProducts->Array.map(product => {
        {
          PromotionSheet.value: product.discount.amount,
          kind: product.discount.kind,
          variantId: product.id,
          variantStockKeepingUnit: product.stockKeepingUnit,
        }
      })

    switch discounts->PromotionSheet.makeCsvBlob {
    | Ok(csvBlob) =>
      csvBlob
      ->TriggerDownload.fromBlob(
        ~filename=promotionCampaign.name->PromotionSheet.makeCsvFilename(
          ~date=switch promotionCampaign.period {
          | Some((date, _)) => date
          | _ => Js.Date.make()
          },
        ),
      )
      ->Future.tapError(() => notifier.add(Error("Something went wrong"), ()))
      ->ignore
    | _ => notifier.add(Error("Something went wrong"), ())
    }
  }

  let onRequestExcelDownload = promotionCampaign => {
    let discounts =
      promotionCampaign.PromotionEditForm.Lenses.discountedProducts->Array.map(product => {
        {
          PromotionSheet.value: product.discount.amount,
          kind: product.discount.kind,
          variantId: product.id,
          variantStockKeepingUnit: product.stockKeepingUnit,
        }
      })

    discounts
    ->PromotionSheet.makeExcelBlob
    ->Future.get(result =>
      switch result {
      | Ok(excelBlob) =>
        excelBlob
        ->TriggerDownload.fromBlob(
          ~filename=promotionCampaign.name->PromotionSheet.makeExcelFilename(
            ~date=switch promotionCampaign.period {
            | Some((date, _)) => date
            | _ => Js.Date.make()
            },
          ),
        )
        ->Future.tapError(() => notifier.add(Error("Something went wrong"), ()))
        ->ignore
      | _ => notifier.add(Error("Something went wrong"), ())
      }
    )
  }

  // Renders the top rights buttons for edition mode and campaign mutations
  let renderPageActions = React.useCallback4(() =>
    switch readOnly {
    | false =>
      <PromotionEditFormPageActions
        status=formattedStatus
        editing
        promotionCreated
        onRequestEditing={value => setEditing(_ => value)}
        onRequestCsvDownload
        onRequestExcelDownload
      />
    | _ => React.null
    }
  , (readOnly, formattedStatus, promotionCreated, editing))

  // Renders global campaign status at the end of the page title
  let renderPageTitleEnd = React.useCallback3(() =>
    switch (initialData, promotionCreated, editing) {
    | (Data({query: Some({campaigns})}), true, false) =>
      campaigns
      ->Array.map(({node: campaign}) => campaign.formattedStatus)
      ->PromotionStatus.globalFromStatuses
      ->Option.mapWithDefault(React.null, globalStatus =>
        <PromotionStatusBadge status=globalStatus />
      )
    | _ => React.null
    }
  , (initialData, promotionCreated, editing))

  // Formates the page title from the current campaign name and step
  let formattedTitle = switch (promotionCreated, editing, initialData) {
  | (false, _, _) => t("New promotional campaign")
  | (true, true, Data({form: (state, _)})) =>
    t("Edition") ++ " " ++ t("Campaign") ++ " " ++ state.initialValues.name
  | (true, false, Data({form: (state, _)})) => state.initialValues.name
  | _ => ""
  }

  // Sets edition mode for interfaces
  let editable = switch (editing, formattedStatus) {
  | (_, #DRAFT) | (true, #PROGRAMMED | #NOT_PROGRAMMED) => !readOnly
  | _ => false
  }

  switch initialData {
  | Data({form: (state, dispatch)}) =>
    <PromotionEditForm.FormProvider propState=(state, dispatch)>
      <Page title=formattedTitle renderTitleEnd=renderPageTitleEnd renderActions=renderPageActions>
        <Stack space=#medium>
          <Notifier.Banner notifier />
          <PromotionEditDiscountedProductTableCard
            editable
            products=state.values.discountedProducts
            selectedPriceName=state.values.priceName
            renderPageActions
            onRequestProductsUpdate
          />
          <Columns space=#medium>
            <Column width=#one_third>
              <PromotionEditFormInformationCard editable promotionCreated />
            </Column>
            <Column width=#three_quarter>
              // NOTE - key ensures the component remounts on priceName change due to the lack of a controlled selection in Table.
              <PromotionEditFormManagementTableCard
                key=?{state.values.priceName}
                editing
                promotionCreated
                campaigns=state.values.rootCampaigns
                selectedPriceName={state.values.priceName->Option.getWithDefault("")}
                onRequestShopsUpdate
              />
            </Column>
          </Columns>
        </Stack>
        React.null
      </Page>
    </PromotionEditForm.FormProvider>
  | Loading => <Placeholder status=Loading />
  | Errored => <Placeholder status=Error />
  }
}

let make = React.memo(make)

open Intl

@react.component
let make = () => {
  let notifier = Notifier.use()
  let shops = Auth.useShops()

  let onSubmitFailure = React.useCallback(message => notifier.reset(Error(message), ()))
  let onSubmitSuccess = React.useCallback(submissionResponse =>
    switch submissionResponse {
    | Some(_) => notifier.clear()
    | _ => notifier.add(Error(Request.serverErrorMessage), ())
    }
  )

  let (formState, formDispatch) = StockTransferForm.useFormPropState({
    initialValues: StockTransferForm.initialValues,
    resetValuesAfterSubmission: true,
    schema: StockTransferForm.schema,
    onSubmitFailure,
    onSubmitSuccess,
  })

  let productsRowErrors = formState.values.products->StockTransferForm.productsError
  let oldFormStatus = React.useRef(formState.status)

  React.useEffect1(() => {
    switch formState.submission {
    | Succeeded(_) =>
      let {senderShopId, recipientShopId} = formState.values
      let senderShop = shops->Array.getBy(shop => shop.id === senderShopId)->Option.getUnsafe
      let recipientShop = shops->Array.getBy(shop => shop.id === recipientShopId)->Option.getUnsafe

      let strLiteral =
        formState.values.products->Array.length->isPlural
          ? "All seized stocks were successfully transferred from {{sender}} to {{recipient}}!"
          : "The seized stock was successfully transferred from {{sender}} to {{recipient}}!"

      let notification = template(
        t(strLiteral),
        ~values={"sender": senderShop.name, "recipient": recipientShop.name},
        (),
      )

      notifier.reset(Success(notification), ())
    | _ => ()
    }

    None
  }, [formState.submission])

  React.useEffect1(() => {
    if oldFormStatus.current === Pristine && formState.status !== Pristine {
      notifier.clear()
    }
    oldFormStatus.current = formState.status
    None
  }, [formState.status])

  <StockTransferForm.FormProvider propState=(formState, formDispatch)>
    <Page title={t("Stock transfer")} renderActions={() => <StockTransferFormActions />}>
      <Notifier.Banner notifier />
      <Box spaceTop=#medium>
        <Stack space=#medium>
          <Columns space=#large>
            <Column width=#half>
              <StockTransferFormSenderInformationCard />
            </Column>
            <Column width=#half>
              <StockTransferFormRecipientInformationCard />
            </Column>
          </Columns>
          <StockTransferFormProductsCard productsRowErrors />
        </Stack>
      </Box>
    </Page>
  </StockTransferForm.FormProvider>
}

let make = React.memo(make)

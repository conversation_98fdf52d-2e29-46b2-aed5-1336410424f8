open Intl
open StyleX

type action =
  | First
  | PrevPrev
  | Prev
  | Next
  | NextNext
  | Last

type direction = Forward | Backward

let isForwardDirection = direction =>
  switch direction {
  | Forward => true
  | Backward => false
  }

let isButtonDisabled = (~action, ~currentPage, ~totalPages) =>
  switch action {
  | PrevPrev => currentPage - 1 <= 1
  | First | Prev => currentPage <= 1
  | Next | Last => currentPage >= totalPages
  | NextNext => currentPage - 1 >= totalPages
  }

module ResourceListPaginationSummary = {
  @inline let font = `normal 700 12px "Libre Franklin"`

  let styles = StyleX.create({
    "PaginationSummary_root": style(
      ~display=#flex,
      ~gap=Spaces.smallPx,
      ~lineHeight="17px",
      ~minWidth="160px",
      ~font,
      ~color=Colors.neutralColor30,
      (),
    ),
  })

  let styleProps = () => StyleX.props([styles["PaginationSummary_root"]])

  @react.component
  let make = (~currentPage, ~totalCount, ~loading, ~itemsPerPage) => {
    let {?style, ?className} = styleProps()

    let start = ((currentPage - 1) * itemsPerPage + 1)->Int.toString
    let end = Js.Math.min_int(currentPage * itemsPerPage, totalCount)->Int.toString

    <span ?style ?className>
      {(start ++ " — " ++ end ++ t(" of ") ++ totalCount->Int.toString)->React.string}
      {if loading {
        <Spinner size=17. />
      } else {
        React.null
      }}
    </span>
  }
}

module ResourceListPaginationPageButton = {
  @inline let font = `normal 400 13px "Libre Franklin"`
  @inline let fontDisabled = `normal 600 13px "Libre Franklin"`

  let styles = StyleX.create({
    "PaginationPageButton_root": style(
      ~display=#flex,
      ~justifyContent=#center,
      ~width="32px",
      ~height="32px",
      ~border="1px solid " ++ Colors.neutralColor20,
      ~borderRadius="5px",
      ~boxSizing=#"border-box",
      ~transition="all .1s ease-out",
      ~\":hover"=style(
        ~borderColor=Colors.neutralColor30,
        ~backgroundColor=Colors.neutralColor05,
        (),
      ),
      (),
    ),
    "PaginationPageButton_disabled": style(
      ~backgroundColor="#e9e9ee",
      ~border="1px solid " ++ Colors.neutralColor25,
      ~\":hover"=style(~borderColor=Colors.neutralColor25, ~backgroundColor="#e9e9ee", ()),
      (),
    ),
    "PaginationPageButton_text": style(
      ~display=#flex,
      ~alignItems=#center,
      ~justifyContent=#center,
      ~width="32px",
      ~height="100%",
      ~font,
      ~textAlign=#center,
      ~color=Colors.neutralColor50,
      (),
    ),
    "PaginationPageButton_textDisabled": style(
      ~font=fontDisabled,
      ~color=Colors.neutralColor90,
      (),
    ),
  })

  let styleProps = (~disabled, ~pressed) =>
    StyleX.props([
      styles["PaginationPageButton_root"],
      disabled ? styles["PaginationPageButton_disabled"] : style(),
      style(~transform=pressed ? "scale3d(0.94, 0.94, 1)" : "initial", ()),
    ])
  let textStyleProps = (~disabled) =>
    StyleX.props([
      styles["PaginationPageButton_text"],
      disabled ? styles["PaginationPageButton_textDisabled"] : style(),
    ])

  @react.component
  let make = (~loading, ~currentPage, ~totalPages, ~action=?, ~children=?, ~onPress) => {
    let (pressed, setPressed) = React.useState(() => false)

    let buttonProps = {
      ReactAria.Button.elementType: #div,
      onPressStart: _ => setPressed(_ => true),
      onPressEnd: _ => setPressed(_ => false),
    }
    let disabled =
      action->Option.mapWithDefault(true, action =>
        isButtonDisabled(~action, ~currentPage, ~totalPages)
      )

    let onPress = _ =>
      switch action {
      | Some(action) => onPress(action)
      | None => ()
      }

    let {?style, ?className} = styleProps(~disabled, ~pressed)
    let {style: ?textStyle, className: ?textClassName} = textStyleProps(~disabled)

    let disabled = loading || disabled

    <div ?style ?className>
      <Touchable disabled ariaProps=buttonProps onPress={_ => onPress(action)}>
        <span style=?textStyle className=?textClassName>
          {switch (children, action) {
          | (Some(pageNumberElement), _) => pageNumberElement
          | (_, Some(First)) => 1->React.int
          | (_, Some(Prev)) =>
            <Svg width="20" height="20" viewBox="0 0 20 20">
              <Svg.Path
                d="M13.328 15.698a.5.5 0 1 1-.712.702l-5.972-6.049a.5.5 0 0 1 .002-.704l5.972-6a.5.5 0 1 1 .708.706L7.704 10l5.624 5.697z"
                fill=Colors.neutralColor50
              />
            </Svg>
          | (_, Some(Next)) =>
            <Svg width="20" height="20" viewBox="0 0 20 20">
              <Svg.Path
                d="M12.268 10.048 6.644 4.35a.5.5 0 1 1 .712-.702l5.972 6.049a.5.5 0 0 1-.002.704l-5.972 6a.5.5 0 0 1-.708-.706l5.622-5.648z"
                fill=Colors.neutralColor50
              />
            </Svg>
          | (_, Some(Last)) => totalPages->React.int
          | _ => React.null
          }}
        </span>
      </Touchable>
    </div>
  }

  let make = React.memo(make)
}

module ResourceListPaginationScrollToTopButton = {
  let styles = StyleX.create({
    "PaginationScrollToTopButton_root": style(
      ~display=#flex,
      ~justifyContent=#center,
      ~width="32px",
      ~height="32px",
      ~border="1px solid " ++ Colors.neutralColor20,
      ~borderRadius="5px",
      ~boxSizing=#"border-box",
      ~transition="all .1s ease-out",
      ~boxShadow=`0px 0px 5px 1px ${Colors.neutralColor15}`,
      ~cursor=#pointer,
      ~\":hover"=style(
        ~borderColor=Colors.neutralColor25,
        ~backgroundColor=Colors.neutralColor05,
        (),
      ),
      (),
    ),
  })

  let styleProps = (~pressed, ~show) =>
    StyleX.props([
      styles["PaginationScrollToTopButton_root"],
      style(~transform=pressed ? "scale3d(0.94, 0.94, 1)" : "initial", ()),
      style(~opacity=show ? 1. : 0., ~transition="opacity .15s ease-out", ()),
    ])

  @react.component
  let make = () => {
    let (pressed, setPressed) = React.useState(() => false)
    let (show, setShow) = React.useState(() => false)

    React.useLayoutEffect0(() => {
      let handler = _ => setShow(_ => WebAPI.window->WebAPI.Window.scrollY > 15.)
      handler()
      WebAPI.window->WebAPI.Window.addEventListener("scroll", handler)
      Some(() => WebAPI.window->WebAPI.Window.removeEventListener("scroll", handler))
    })

    let buttonProps = {
      ReactAria.Button.elementType: #div,
      onPressStart: _ => setPressed(_ => true),
      onPressEnd: _ => setPressed(_ => false),
    }

    let handleButtonPress = _ =>
      WebAPI.window
      ->WebAPI.Window.scrollToWithOptions({"top": 0., "left": 0., "behavior": "smooth"})
      ->ignore

    let {?style, ?className} = styleProps(~pressed, ~show)

    <Touchable ariaProps=buttonProps onPress=handleButtonPress>
      <div ?style ?className>
        <Svg
          width="18"
          height="18"
          viewBox="0 0 18 16"
          style={ReactDOMStyle.make(~paddingTop="4px", ())}>
          <Svg.Path
            transform="rotate(270 10.5 11.5)"
            d="M 11.236 4.018 L 16.683 9.464 L 1.97 9.464 C 1.559 9.464 1.301 9.91 1.508 10.267 C 1.603 10.433 1.779 10.535 1.97 10.535 L 16.799 10.535 L 11.352 15.981 C 11.06 16.272 11.194 16.768 11.592 16.876 C 11.775 16.926 11.973 16.873 12.108 16.738 L 18.408 10.436 C 18.618 10.227 18.618 9.887 18.408 9.679 L 11.991 3.263 C 11.784 3.053 11.444 3.053 11.236 3.263 C 11.027 3.47 11.027 3.81 11.236 4.018 Z"
            fill=Colors.neutralColor90
          />
        </Svg>
      </div>
    </Touchable>
  }

  let make = React.memo(make)
}

let styles = StyleX.create({
  "Pagination_root": style(
    ~position=#sticky,
    ~bottom="0px",
    ~zIndex=2,
    ~display=#flex,
    ~height="60px",
    ~flexDirection=#row,
    ~alignItems=#center,
    ~justifyContent=#center,
    ~backgroundColor=Colors.neutralColor00,
    ~paddingHorizontal=Spaces.largePx,
    ~borderTop="1px solid " ++ Colors.neutralColor15,
    (),
  ),
  "Pagination_controls": style(
    ~display=#flex,
    ~alignItems=#center,
    ~gap=Spaces.xsmallPx,
    ~margin="auto",
    ~transform="translateX(-80px)",
    (),
  ),
})

let styleProps = () => StyleX.props([styles["Pagination_root"]])
let controlsStyleProps = () => StyleX.props([styles["Pagination_controls"]])

let separatorIconElement =
  <Svg width="20" height="20" viewBox="0 0 20 20">
    <Svg.Path
      d="M4,11 C4.55228475,11 5,10.5522847 5,10 C5,9.44771525 4.55228475,9 4,9 C3.44771525,9 3,9.44771525 3,10 C3,10.5522847 3.44771525,11 4,11 Z M10,11 C10.5522847,11 11,10.5522847 11,10 C11,9.44771525 10.5522847,9 10,9 C9.44771525,9 9,9.44771525 9,10 C9,10.5522847 9.44771525,11 10,11 Z M16,11 C16.5522847,11 17,10.5522847 17,10 C17,9.44771525 16.5522847,9 16,9 C15.4477153,9 15,9.44771525 15,10 C15,10.5522847 15.4477153,11 16,11 Z"
      fill=Colors.neutralColor50
    />
  </Svg>

@react.component
let make = (
  ~currentPage,
  ~totalPages,
  ~totalCount,
  ~loading,
  ~lastDirection,
  ~itemsPerPage,
  ~onRequestPaginate as onPress,
) => {
  let {?style, ?className} = styleProps()
  let {style: ?controlsStyle, className: ?controlsClassName} = controlsStyleProps()

  React.useEffect2(() => {
    if !loading {
      WebAPI.window
      ->WebAPI.Window.scrollToWithOptions({"top": 0., "left": 0., "behavior": "smooth"})
      ->ignore
    }
    None
  }, (loading, currentPage))

  let notFirstPages = currentPage > 1 && totalPages !== 2
  let notLastPages = currentPage < totalPages - 1

  <div ?style ?className>
    <ResourceListPaginationSummary currentPage totalCount loading itemsPerPage />
    <div style=?controlsStyle className=?controlsClassName>
      <ResourceListPaginationPageButton action=Prev loading currentPage totalPages onPress />
      <ResourceListPaginationPageButton action=First loading currentPage totalPages onPress />
      {if (
        (isForwardDirection(lastDirection) && (notLastPages || totalPages === 1)) ||
          (!isForwardDirection(lastDirection) && currentPage < 3)
      ) {
        <>
          {if currentPage > 2 {
            separatorIconElement
          } else {
            React.null
          }}
          {if notFirstPages {
            <ResourceListPaginationPageButton loading currentPage totalPages onPress>
              {currentPage->React.int}
            </ResourceListPaginationPageButton>
          } else {
            React.null
          }}
          {if totalPages !== 2 && notLastPages {
            <ResourceListPaginationPageButton action=Next loading currentPage totalPages onPress>
              {(currentPage + 1)->React.int}
            </ResourceListPaginationPageButton>
          } else {
            React.null
          }}
          {if currentPage !== 2 && currentPage < totalPages - 2 {
            <ResourceListPaginationPageButton
              action=NextNext loading currentPage totalPages onPress>
              {(currentPage + 2)->React.int}
            </ResourceListPaginationPageButton>
          } else {
            React.null
          }}
          {if currentPage !== totalPages && currentPage < totalPages - 3 {
            separatorIconElement
          } else {
            React.null
          }}
        </>
      } else {
        <>
          {if currentPage > 4 && totalPages >= 4 {
            separatorIconElement
          } else {
            React.null
          }}
          {if (
            currentPage !== 2 &&
            currentPage > 3 &&
            totalPages > 3 &&
            (currentPage < totalPages - 1 || currentPage === totalPages)
          ) {
            <ResourceListPaginationPageButton
              action=PrevPrev loading currentPage totalPages onPress>
              {(currentPage - 2)->React.int}
            </ResourceListPaginationPageButton>
          } else {
            React.null
          }}
          {if notFirstPages && (totalPages > 3 || currentPage === totalPages) {
            <ResourceListPaginationPageButton action=Prev loading currentPage totalPages onPress>
              {(currentPage - 1)->React.int}
            </ResourceListPaginationPageButton>
          } else {
            React.null
          }}
          {if notFirstPages && currentPage !== totalPages {
            <ResourceListPaginationPageButton loading currentPage totalPages onPress>
              {currentPage->React.int}
            </ResourceListPaginationPageButton>
          } else {
            React.null
          }}
          {if currentPage !== totalPages && currentPage < totalPages - 1 {
            separatorIconElement
          } else {
            React.null
          }}
        </>
      }}
      {if totalPages !== 1 {
        <ResourceListPaginationPageButton action=Last loading currentPage totalPages onPress />
      } else {
        React.null
      }}
      <ResourceListPaginationPageButton action=Next loading currentPage totalPages onPress />
    </div>
    <ResourceListPaginationScrollToTopButton />
  </div>
}

let make = React.memo(make)

let updateUserFields = (~userId, ~name, ~email) => {
  if Env.context() === #production {
    LogRocket.identify(userId, ~metadata={name, email})
    PostHog.identify(userId, ~metadata={name, email})
  }
}

let onSession = handler =>
  if Env.context() === #production {
    LogRocket.getSessionURL(session => handler(session))
  }

module Event = {
  type t = [
    | #download_customer_export_file
    | #download_sales_export_file
    | #alert_bar_click_add_payment_method
    | #alert_bar_click_validate_mandate
    | #alert_bar_click_payment_overdue
    | #validate_sepa_mandate
    | #download_accounting_excel_export_file
    | #download_accounting_isacompta_export_file
    | #create_automatic_order
    | #cart_global_shipping_costs_click_apply
    | #catalog_settings_click_save
    | #catalog_pricelist_select
    | #catalog_variant_page_retailprice_click_save
    | #catalog_retailprice_click_save
    | #catalog_retailprice_with_charges_click_save
  ]
}

let captureEvent = (event: Event.t) =>
  if Env.context() === #production {
    PostHog.capture((event :> string))
  }

let useCaptureEvent = () => {
  let auth = Auth.useState()

  event => {
    switch auth {
    | Logged({user: {impersonating: false}}) => captureEvent(event)
    | _ => ()
    }
  }
}

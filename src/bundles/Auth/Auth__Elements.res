open Intl
open Auth__Types

let {useDispatch, useScope, useShops, useActiveShop} = module(Auth__Hooks)

let truncateWithMiddleEllipsis = (str, ~maxLen) =>
  if str->String.length > maxLen {
    let len = str->String.length
    let halfLen = (maxLen - 3) / 2
    let startStr = str->Js.String2.slice(~from=0, ~to_=halfLen)->Js.String2.trim
    let endStr = str->Js.String2.slice(~from=len - halfLen, ~to_=len)->Js.String2.trim
    startStr ++ "..." ++ endStr
  } else {
    str
  }

module SelectShopFilter = {
  type variation = [#primary | #secondary]

  @react.component
  let make = (
    ~variation=#primary, // NOTE - temporary until the new design system is applied globally
    ~value=?,
    ~disabledIds=[],
    ~hideLabel=false,
    ~truncateItemLabel=false,
    ~onChange=?,
  ) => {
    let dispatch = useDispatch()
    let shops = useShops()
    let activeShop = useActiveShop()

    let onChange = nextShopId => {
      let nextShop =
        nextShopId->Option.flatMap(shopId => shops->Array.getBy(shop => shop.id === shopId))
      onChange->Option.forEach(fn => fn(nextShop))
      ActiveShopSet(nextShop)->dispatch
    }

    let shopItems = shops->Array.map(shop => {
      Select.key: shop.id,
      label: shop.name,
      value: Some(shop.id),
      disabled: disabledIds->Array.some(id => id === shop.id),
    })
    let sections = [
      {
        Select.items: [
          {
            key: "default",
            label: template(t("All{{feminine}}"), ()),
            value: None,
            sticky: true,
          },
        ],
      },
      {title: t("Shops"), items: shopItems},
    ]
    let value = value->Option.orElse(activeShop)->Option.map(shop => shop.id)
    let label = !hideLabel ? Some(t("Shop")) : None
    let preset = switch variation {
    | #primary => #filter
    | #secondary => #inputField({OverlayTriggerView.required: false})
    }

    let renderTriggerView = truncateItemLabel
      ? Some(
          (~children as _, ~item, ~hovered, ~active, ~focused) => {
            let itemLabel =
              item->Option.mapWithDefault(t("Select a shop"), item => item.Select.label)
            let formattedLabel = truncateWithMiddleEllipsis(itemLabel, ~maxLen=17)
            <OverlayTriggerView preset ?label hovered active focused>
              {switch item {
              | Some({value: None}) =>
                <InlineText>
                  <TextStyle variation=#normal>
                    {(t("Shop") ++ t(":") ++ " ")->React.string}
                  </TextStyle>
                  <TextStyle> {itemLabel->React.string} </TextStyle>
                </InlineText>
              | _ => <TextStyle> {formattedLabel->React.string} </TextStyle>
              }}
            </OverlayTriggerView>
          },
        )
      : None

    <Select ?label preset sections value ?renderTriggerView onChange />
  }
}

module SelectSingleShopFilter = {
  @react.component
  let make = (~value, ~disabledIds=[], ~onChange=?) => {
    let dispatch = useDispatch()
    let shops = useShops()

    let onChange = nextActiveShopId => {
      let nextActiveShop = shops->Array.getBy(shop => shop.id === nextActiveShopId)
      switch nextActiveShop {
      | Some(nextActiveShop) => onChange->Option.forEach(fn => fn(nextActiveShop))
      | None => ()
      }
      ActiveShopSet(nextActiveShop)->dispatch
    }

    let value = value.id
    let shopItems = shops->Array.map(shop => {
      Select.key: shop.id,
      label: shop.name,
      value: shop.id,
      disabled: disabledIds->Array.some(id => id === shop.id),
    })
    let sections = [{Select.title: t("Shops"), items: shopItems}]

    <Select label={t("Shop")} sections value preset=#filter onChange />
  }
}

module InputSelectSingleShopField = {
  @react.component
  let make = (~value, ~required, ~disabledIds=[], ~onChange=?) => {
    let dispatch = useDispatch()
    let shops = useShops()

    let onChange = nextActiveShopId => {
      let nextActiveShop = shops->Array.getBy(shop => shop.id === nextActiveShopId)
      switch nextActiveShop {
      | Some(nextActiveShop) => onChange->Option.forEach(fn => fn(nextActiveShop))
      | None => ()
      }
      ActiveShopSet(nextActiveShop)->dispatch
    }

    let value = value.id
    let shopItems = shops->Array.map(shop => {
      Select.key: shop.id,
      label: shop.name,
      value: shop.id,
      disabled: disabledIds->Array.some(id => id === shop.id),
    })
    let sections = [{Select.title: t("Shops"), items: shopItems}]

    <Select label={t("Shop")} sections value preset=#inputField({required: required}) onChange />
  }
}

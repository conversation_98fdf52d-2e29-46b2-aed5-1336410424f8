// Bindings with JavaScript/Typescript is the most dangerous part of
// these application. And it’s definitely a bad idea to trust yourself
// that you’ve done it correctly. Especially considering the fact that
// it might become outdated during the next release.

// TODO - examples that will eventually be specialized with:
// NOTE - [...] https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement
// NOTE - [...] https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes
// @genType type inputProps = JsxDOM.domProps
// NOTE - [...] https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement
// NOTE - [...] https://developer.mozilla.org/en-US/docs/Web/API/HTMLTextAreaElement
// @genType type textAreaProps = JsxDOM.domProps

module Button = {
  @genType.import(("@react-types/shared", "PressEvent"))
  type pressEvent = {
    \"type": [#pressstart | #pressend | #pressup | #press],
    pointerType: [#mouse | #pen | #touch | #keyboard | #virtual],
    target: Dom.element,
    shiftKey: bool,
    ctrlKey: bool,
    metaKey: bool,
    altKey: bool,
  }

  @genType.import(("@react-types/button", "AriaButtonProps"))
  type props = {
    id?: string,
    \"aria-labelledby"?: string,
    \"aria-label"?: string,
    elementType?: [#button | #div | #a],
    @as("isDisabled") disabled?: bool,
    excludeFromTabOrder?: bool,
    onPress?: pressEvent => unit,
    onPressStart?: pressEvent => unit,
    onPressEnd?: pressEvent => unit,
    onFocus?: pressEvent => unit,
    onBlur?: pressEvent => unit,
  }

  type buttonProps
  type buttonAria = {
    @as("isPressed") pressed: bool,
    buttonProps: buttonProps,
  }

  @module("react-aria")
  external use: (~props: props, ~ref: ReactDOM.domRef=?, unit) => buttonAria = "useButton"
}

module Label = {
  type props = {
    id?: string,
    label?: string,
    \"aria-label"?: string,
    labelElementType?: [#label | #div | #a],
  }

  type labelAria = {
    labelProps: JsxDOM.domProps,
    fieldProps: JsxDOM.domProps,
  }

  @module("react-aria")
  external use: (~props: props) => labelAria = "useLabel"
}

module TextField = {
  type props = {
    id?: string,
    label?: string,
    \"aria-label"?: string,
    placeholder?: string,
    inputElementType?: [#input | #textarea],
    \"type"?: [#text | #password],
    @as("isReadOnly") readOnly: bool,
    autoFocus?: bool,
    value?: string,
    defaultValue?: string,
    onChange?: string => unit,
    onFocus?: unit => unit,
    onBlur?: unit => unit,
    onFocusChange?: bool => unit,
  }

  type textAria = {inputProps: JsxDOM.domProps}

  @module("react-aria")
  external use: (~props: props, ~ref: ReactDOM.domRef) => textAria = "useTextField"
}

module Checkbox = {
  // TODO - in Rescript v11 it should inherit from ReactStately.Toggle
  @genType.import(("@react-types/checkbox", "AriaCheckboxProps"))
  type props = {
    id?: string,
    \"aria-label": string,
    @as("isIndeterminate") indeterminate: bool,
    @as("isSelected") selected: bool,
    @as("isDisabled") disabled: bool,
    onChange?: bool => unit,
  }

  type t = {
    inputProps: JsxDOM.domProps,
    @as("isSelected") selected: bool,
    @as("isPressed") pressed: bool,
    @as("isReadOnly") readOnly: bool,
    @as("isDisabled") disabled: bool,
  }

  @module("react-aria")
  external use: (~props: props, ~state: ReactStately.Toggle.state, ~ref: ReactDOM.domRef) => t =
    "useCheckbox"
}

module Select = {
  type t = {
    labelProps: JsxDOM.domProps,
    valueProps: JsxDOM.domProps,
    menuProps: JsxDOM.domProps,
    triggerProps: Button.props,
  }

  @module("react-aria")
  external use: (
    ~props: ReactStately.Select.props,
    ~state: ReactStately.Select.state,
    ~ref: ReactDOM.domRef,
  ) => t = "useSelect"
}

module Radio = {
  module Group = {
    type t = {
      labelProps: JsxDOM.domProps,
      descriptionProps: JsxDOM.domProps,
      radioGroupProps: JsxDOM.domProps,
    }

    @module("react-aria")
    external use: (
      ~props: ReactStately.RadioGroup.props,
      ~state: ReactStately.RadioGroup.state,
    ) => t = "useRadioGroup"
  }

  type props = {
    value: string,
    children: React.element,
    @as("isDisabled") disabled?: bool,
  }

  type t = {
    inputProps: JsxDOM.domProps,
    @as("isSelected") selected: bool,
    @as("isPressed") pressed: bool,
    @as("isDisabled") disabled: bool,
  }

  @module("react-aria")
  external use: (~props: props, ~state: ReactStately.RadioGroup.state, ~ref: ReactDOM.domRef) => t =
    "useRadio"
}

module Table = {
  module Selection = {
    type props = {key: ReactStately.key}

    type t = {checkboxProps: Checkbox.props}

    @module("react-aria")
    external useCheck: (~props: props, ~state: ReactStately.Table.state<'columnProps>) => t =
      "useTableSelectionCheckbox"
    @module("react-aria")
    external useCheckAll: (~state: ReactStately.Table.state<'columnProps>) => t =
      "useTableSelectAllCheckbox"
  }

  module HeaderRow = {
    type props<'columnProps> = {node: ReactStately.Table.collectionNode<'columnProps>}

    type t = {rowProps: JsxDOM.domProps}

    @module("react-aria")
    external use: (
      ~props: props<'columnProps>,
      ~state: ReactStately.Table.state<'columnProps>,
      ~ref: ReactDOM.domRef,
    ) => t = "useTableHeaderRow"
  }

  module ColumnHeader = {
    type props<'columnProps> = {node: ReactStately.Table.collectionNode<'columnProps>}

    type t = {columnHeaderProps: JsxDOM.domProps}

    @module("react-aria")
    external use: (
      ~props: props<'columnProps>,
      ~state: ReactStately.Table.state<'columnProps>,
      ~ref: ReactDOM.domRef,
    ) => t = "useTableColumnHeader"
  }

  module RowGroup = {
    type t = {rowGroupProps: JsxDOM.domProps}

    @module("react-aria")
    external use: unit => t = "useTableRowGroup"
  }

  module Row = {
    type props<'columnProps> = {node: ReactStately.Table.collectionNode<'columnProps>}

    type t = {
      rowProps: JsxDOM.domProps,
      @as("isPressed") pressed: bool,
      @as("isSelected") selected: bool,
      @as("isFocused") focused: bool,
      @as("isDisabled") disabled: bool,
      allowsSelection: bool,
      hasAction: bool,
    }

    @module("react-aria")
    external use: (
      ~props: props<'columnProps>,
      ~state: ReactStately.Table.state<'columnProps>,
      ~ref: ReactDOM.domRef,
    ) => t = "useTableRow"
  }

  module Cell = {
    type props<'columnProps> = {node: ReactStately.Table.collectionNode<'columnProps>}

    type t = {
      gridCellProps: JsxDOM.domProps,
      @as("isPressed") pressed: bool,
    }

    @module("react-aria")
    external use: (
      ~props: props<'columnProps>,
      ~state: ReactStately.Table.state<'columnProps>,
      ~ref: ReactDOM.domRef,
    ) => t = "useTableCell"
  }

  // TODO - to explore (there is no documentation about it yet):
  // the concept is to override some wanted @react-stately/layout class to control
  // and customize all the layout behaviors and get the final desired layout object.
  // Also required for handling a virtualized view (example in react-spectrum BaseLayout.tsx).
  module Layout = {
    type t
  }

  type props = {
    layout?: Layout.t,
    \"aria-label": string,
    scrollRef: ReactDOM.domRef,
    onRowAction?: ReactStately.key => unit,
    onCellAction?: ReactStately.key => unit,
  }

  type t = {gridProps: JsxDOM.domProps}

  @module("react-aria")
  external use: (
    ~props: props,
    ~state: ReactStately.Table.state<'columnProps>,
    ~ref: ReactDOM.domRef,
  ) => t = "useTable"
}

module Overlay = {
  type overlayProps = JsxDOM.domProps
  type underlayProps = JsxDOM.domProps

  module Position = {
    type placementAxis = [#top | #right | #bottom | #left]
    @genType.import(("@react-types/overlays", "Placement"))
    type placement = [
      | #top
      | #"top start"
      | #"top end"
      | #end
      | #"end top"
      | #"end bottom"
      | #bottom
      | #"bottom start"
      | #"bottom end"
      | #start
      | #"start top"
      | #"start bottom"
    ]

    type style<'styleProps> = {style: 'styleProps}
    type arrowStyle = {
      top: option<float>,
      left: option<float>,
    }

    type ariaProps = {
      targetRef: ReactDOM.domRef,
      overlayRef: ReactDOM.domRef,
      @as("isOpen") opened: bool,
      placement?: placement,
      offset?: float,
      crossOffset?: float,
    }

    type t = {
      overlayProps: JsxDOM.domProps,
      arrowProps: style<arrowStyle>,
      placement: placementAxis,
      updatePosition: unit => unit,
    }

    @module("react-aria")
    external use: (~props: ariaProps) => t = "useOverlayPosition"
  }

  module Trigger = {
    // NOTE - this type is different from ReactAria.Overlay.Trigger.props
    type ariaProps = {\"type": [#dialog | #menu | #listbox | #tree | #grid]}

    type t = {
      triggerProps: Button.props,
      overlayProps: overlayProps,
    }

    @module("react-aria")
    external use: (
      ~props: ariaProps,
      ~state: ReactStately.Overlay.Trigger.state,
      ~ref: ReactDOM.domRef=?,
      unit,
    ) => t = "useOverlayTrigger"
  }

  @react.component @module("react-aria")
  external make: (~children: React.element) => React.element = "Overlay"
}

module Popover = {
  type ariaProps = {
    triggerRef: ReactDOM.domRef,
    popoverRef: ReactDOM.domRef,
    shouldUpdatePosition: bool,
    placement?: Overlay.Position.placement,
    offset?: float,
    crossOffset?: float,
    isNonModal?: bool,
  }

  type t = {
    popoverProps: JsxDOM.domProps,
    underlayProps: Overlay.underlayProps,
    arrowProps: Overlay.Position.style<Overlay.Position.arrowStyle>,
    placement: Overlay.Position.placementAxis,
  }

  @module("react-aria")
  external use: (~props: ariaProps, ~state: ReactStately.Overlay.Trigger.state) => t = "usePopover"
}

module Dialog = {
  type ariaProps = {\"aria-label": string}

  type t = {
    dialogProps: JsxDOM.domProps,
    titleProps: JsxDOM.domProps,
  }

  @module("react-aria")
  external use: (~props: ariaProps, ~ref: ReactDOM.domRef) => t = "useDialog"
}

module Tooltip = {
  type tooltipProps = JsxDOM.domProps

  type ariaProps = {
    id: string,
    @as("isOpen") opened: bool,
  }

  type t = {tooltipProps: tooltipProps}

  @module("react-aria")
  external use: (~props: ariaProps, ~state: ReactStately.Tooltip.Trigger.state=?, unit) => t =
    "useTooltip"

  module Trigger = {
    type t = {
      triggerProps: JsxDOM.domProps,
      tooltipProps: tooltipProps,
    }

    @module("react-aria")
    external use: (
      ~props: ReactStately.Tooltip.Trigger.props,
      ~state: ReactStately.Tooltip.Trigger.state,
      ~ref: ReactDOM.domRef,
    ) => t = "useTooltipTrigger"
  }
}

module DateSegment = {
  type t = {segmentProps: JsxDOM.domProps}

  @module("react-aria")
  external use: (
    ~segment: ReactStately.DateSegment.t,
    ~state: ReactStately.TimeField.state,
    ~ref: ReactDOM.domRef,
  ) => t = "useDateSegment"
}

module TimeField = {
  type t = {fieldProps: JsxDOM.domProps}

  @module("react-aria")
  external use: (
    ~props: ReactStately.TimeField.props,
    ~state: ReactStately.TimeField.state,
    ~ref: ReactDOM.domRef,
  ) => t = "useTimeField"
}

module NumberField = {
  type groupProps
  type t = {
    inputProps: JsxDOM.domProps,
    groupProps: groupProps,
    incrementButtonProps: Button.props,
    decrementButtonProps: Button.props,
  }

  @module("react-aria")
  external use: (
    ~props: ReactStately.NumberField.props,
    ~state: ReactStately.NumberField.state,
    ~ref: ReactDOM.domRef=?,
    unit,
  ) => t = "useNumberField"
}

module ListBox = {
  type props = JsxDOM.domProps
  type state = ReactStately.ListBox.state

  type t = {
    listBoxProps: JsxDOM.domProps,
    labelProps: JsxDOM.domProps,
  }

  module Section = {
    type props = {heading?: React.element, \"aria-label"?: string}

    type t = {
      itemProps: JsxDOM.domProps,
      headingProps: JsxDOM.domProps,
      groupProps: JsxDOM.domProps,
    }

    @module("react-aria")
    external use: (~props: props) => t = "useListBoxSection"
  }

  @module("react-aria")
  external use: (~props: props, ~state: state, ~ref: ReactDOM.domRef=?, unit) => t = "useListBox"
}

module Option = {
  type props = {key: string}

  type t = {
    optionProps: JsxDOM.domProps,
    labelProps: JsxDOM.domProps,
    \"aria-label": string,
    @as("isFocused") focused: bool,
    @as("isSelected") selected: bool,
    @as("isPressed") pressed: bool,
    @as("isDisabled") disabled: bool,
  }

  @module("react-aria")
  external use: (
    ~props: props,
    ~state: ReactStately.ListBox.state,
    ~ref: ReactDOM.domRef=?,
    unit,
  ) => t = "useOption"
}

module ComboBox = {
  type t = {
    labelProps: JsxDOM.domProps,
    inputProps: JsxDOM.domProps,
    listBoxProps: ListBox.props,
    buttonProps: Button.props,
  }

  @module("react-aria")
  external use: (~props: ReactStately.ComboBox.props, ~state: ReactStately.ComboBox.state) => t =
    "useComboBox"
}

module Filter = {
  type props = Intl.collatorOptions
  type t = {
    startsWith: ReactStately.filterFn,
    endsWith: ReactStately.filterFn,
    contains: ReactStately.filterFn,
  }

  @module("react-aria")
  external use: props => t = "useFilter"
}

module Resize = {
  type props = {
    ref: ReactDOM.domRef,
    onResize: unit => unit,
  }

  @module("@react-aria/utils")
  external useObserver: props => unit = "useResizeObserver"
}

module DismissButton = {
  @react.component @module("react-aria")
  external make: (~onDismiss: unit => unit) => React.element = "DismissButton"
}

module VisuallyHidden = {
  @react.component @module("react-aria")
  external make: (~children: React.element) => React.element = "VisuallyHidden"
}

module Focus = {
  type props = {
    onFocus: Dom.focusEvent => unit,
    onBlur: Dom.focusEvent => unit,
  }
  type t = {focusProps: JsxDOM.domProps}

  module Within = {
    type props = {onFocusWithinChange: bool => unit}
    type t = {focusWithinProps: JsxDOM.domProps}

    @module("react-aria")
    external use: (~props: props) => t = "useFocusWithin"
  }

  module Scope = {
    @react.component @module("react-aria")
    external make: (
      ~children: React.element,
      ~contain: bool=?,
      ~restoreFocus: bool=?,
      ~autoFocus: bool=?,
    ) => React.element = "FocusScope"
  }

  module Ring = {
    type props = {
      within?: bool,
      autoFocus?: bool,
      @as("isTextInput") textInput?: bool,
    }

    type t = {
      focusProps: JsxDOM.domProps,
      @as("isFocused") focused: bool,
      @as("isFocusVisible") focusVisible: bool,
    }

    @module("react-aria")
    external use: (~props: props=?, unit) => t = "useFocusRing"
  }

  let useRing = Ring.use
  let useWithin = Within.use

  @module("react-aria")
  external use: (~props: props) => t = "useFocus"
}

module Press = {
  type props = {allowTextSelectionOnPress?: bool}

  type t = {
    pressProps: JsxDOM.domProps,
    @as("isPressed") pressed: bool,
  }

  @module("react-aria")
  external use: (~props: props) => t = "usePress"
}

// NOTE - ReactAria makes a very important use of spreads
// which is sometimes difficult to reproduce with rescript
// With ReScript@10.1+, some new features will help us.
// see: to remove with https://forum.rescript-lang.org/t/rfc-new-react-ppx/3422
module Spread = {
  @react.component
  let make = (~props, ~children) => React.cloneElement(children, props)
}

@module("react-aria") external mergeProps2: ('propsA, 'propsB) => 'props = "mergeProps"
@module("react-aria")
external mergeProps3: ('propsA, 'propsB, 'propsC) => 'props = "mergeProps"
@module("react-aria")
external mergeProps4: ('propsA, 'propsB, 'propsC, 'propsD) => 'props = "mergeProps"

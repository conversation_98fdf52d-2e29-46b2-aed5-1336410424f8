type screen
@module("@testing-library/react") @val external screen: screen = "screen"

type rec render = {
  // NOTE - https://forum.rescript-lang.org/t/uncurried-by-default/274/55
  rerender: @uncurry (React.element => render),
  baseElement: Dom.element,
  container: Dom.element,
  unmount: unit => unit,
}
@module("@testing-library/react")
external render: React.element => render = "render"

// NOTE - useful to pass context providers as wrapper. Incomplete options, see:
// https://testing-library.com/docs/react-testing-library/api/#render-options
type renderOption = {wrapper: {"children": React.element} => React.element}
@module("@testing-library/react")
external renderWithOptions: (React.element, ~options: renderOption) => render = "render"

@module("@testing-library/react")
external within: Dom.element => screen = "within"

@module("@testing-library/react")
external act: (unit => unit) => unit = "act"

@module("@testing-library/react")
external actPromise: (unit => Promise.t<unit>) => Promise.t<unit> = "act"

@send external debug: screen => unit = "debug"
@send external debugElement: (screen, Dom.element) => unit = "debug"
@send external debugElementWithPrintLimit: (screen, Dom.element, int) => unit = "debug"

// NOTE - to fetch specific elements that don't have an accessibility role such as <svg>.
// used in pair with { baseElement } | { container } = render
// NOTE - Unsafe suffix: actually returns a nullable value
@send external querySelectorUnsafe: (Dom.element, string) => Dom.element = "querySelector"
@return(nullable) @send
external querySelector: (Dom.element, string) => option<Dom.element> = "querySelector"

type role = [
  | #application
  | #article
  | #banner
  | #blockquote
  | #button
  | #caption
  | #cell
  | #checkbox
  | #columnheader
  | #combobox
  | #command
  | #complementary
  | #composite
  | #contentinfo
  | #definition
  | #deletion
  | #dialog
  | #directory
  | #document
  | #emphasis
  | #feed
  | #figure
  | #form
  | #generic
  | #grid
  | #gridcell
  | #group
  | #heading
  | #img
  | #input
  | #insertion
  | #landmark
  | #link
  | #list
  | #listbox
  | #listitem
  | #main
  | #math
  | #menu
  | #menubar
  | #menuitem
  | #menuitemcheckbox
  | #menuitemradio
  | #meter
  | #navigation
  | #none
  | #note
  | #paragraph
  | #presentation
  | #progressbar
  | #option
  | #radio
  | #radiogroup
  | #range
  | #region
  | #roletype
  | #row
  | #rowgroup
  | #rowheader
  | #scrollbar
  | #search
  | #searchbox
  | #section
  | #sectionhead
  | #select
  | #separator
  | #slider
  | #spinbutton
  | #strong
  | #structure
  | #subscript
  | #superscript
  | #tab
  | #table
  | #tablist
  | #tabpanel
  | #term
  | #textbox
  | #time
  | #tion
  | #toolbar
  | #tooltip
  | #tree
  | #treegrid
  | #treeitem
  | #widget
  | #window
]

// see all options: https://testing-library.com/docs/queries/byrole/
type roleValueOption = {text?: string}
type withRoleOptions = {
  name?: string,
  description?: string,
  hidden?: bool,
  expanded?: bool,
  value?: roleValueOption,
}

type withTextOptions = {selector?: string}

@return(nullable) @send
external queryByText: (screen, string) => option<Dom.element> = "queryByText"
@return(nullable) @send
external queryByLabelText: (screen, string) => option<Dom.element> = "queryByLabelText"
@return(nullable) @send
external queryByPlaceholderText: (screen, string) => option<Dom.element> = "queryByPlaceholderText"
@return(nullable) @send
external queryByRole: (screen, role) => option<Dom.element> = "queryByRole"
@return(nullable) @send
external queryByRoleWithOptions: (screen, role, withRoleOptions) => option<Dom.element> =
  "queryByRole"
@return(nullable) @send
external queryAllByRoleWithOptions: (screen, role, withRoleOptions) => option<Dom.element> =
  "queryAllByRole"

@send external getByTextExn: (screen, string) => Dom.element = "getByText"
@send external getAllByTextExn2: (screen, string) => (Dom.element, Dom.element) = "getAllByText"
@module("@testing-library/react")
external getByTextWithContainerExn: (Dom.element, string) => Dom.element = "getByText"
@send
external getByTextWithOptionsExn: (screen, string, withTextOptions) => Dom.element = "getByText"
@send
external getAllByTextWithOptionsExn: (screen, string, withTextOptions) => array<Dom.element> =
  "getAllByText"
@send external getByLabelTextExn: (screen, 'a) => Dom.element = "getByLabelText"
@send external getAllByLabelTextExn: (screen, 'a) => array<Dom.element> = "getAllByLabelText"

@send external getByRoleExn: (screen, role) => Dom.element = "getByRole"
@send external getByRoleWithOptionsExn: (screen, role, withRoleOptions) => Dom.element = "getByRole"

@send external getAllByRoleExn: (screen, role) => array<Dom.element> = "getAllByRole"
@send external getAllByRoleExn2: (screen, role) => (Dom.element, Dom.element) = "getAllByRole"
@send
external getAllByRoleExn3: (screen, role) => (Dom.element, Dom.element, Dom.element) =
  "getAllByRole"
@send
external getAllByRoleExn4: (screen, role) => (Dom.element, Dom.element, Dom.element, Dom.element) =
  "getAllByRole"
@send
external getAllByRoleExn5: (
  screen,
  role,
) => (Dom.element, Dom.element, Dom.element, Dom.element, Dom.element) = "getAllByRole"
@send
external getAllByRoleExn6: (
  screen,
  role,
) => (Dom.element, Dom.element, Dom.element, Dom.element, Dom.element, Dom.element) = "getAllByRole"
@send
external getAllByRoleExn7: (
  screen,
  role,
) => (Dom.element, Dom.element, Dom.element, Dom.element, Dom.element, Dom.element, Dom.element) =
  "getAllByRole"

@send
external getAllByRoleWithOptionsExn: (screen, role, withRoleOptions) => array<Dom.element> =
  "getAllByRole"
@send
external getAllByRoleWithOptionsExn2: (
  screen,
  role,
  withRoleOptions,
) => (Dom.element, Dom.element) = "getAllByRole"

// @module('@testing-library/jest-dom)
// opened globally with /tests/extends.ts
@send external toBeVisible: Vitest.expect<Dom.element> => unit = "toBeVisible"
@send external toBeDisabled: Vitest.expect<Dom.element> => unit = "toBeDisabled"
@send external toHaveFocus: Vitest.expect<Dom.element> => unit = "toHaveFocus"
@send external toHaveValue: (Vitest.expect<Dom.element>, string) => unit = "toHaveValue"
@send external toHaveTextContent: (Vitest.expect<Dom.element>, string) => unit = "toHaveTextContent"
@send external toBeInTheDocument: Vitest.expect<Dom.element> => unit = "toBeInTheDocument"
@send external toBeEmptyDOMElement: Vitest.expect<Dom.element> => unit = "toBeEmptyDOMElement"
@send external toHaveStyle: (Vitest.expect<Dom.element>, {..}) => unit = "toHaveStyle"

@module("@testing-library/react")
external waitFor: (unit => unit) => Promise.t<unit> = "waitFor"
@module("@testing-library/react")
external waitForReturn: (unit => 'a) => Promise.t<'a> = "waitFor"

// NOTE - in next versions will be included in @testing-library/react package
type renderHookResult<'output> = {
  all: array<'output>,
  current: 'output,
  error: Js.Exn.t,
}
type renderHookOutput<'props, 'output> = {
  result: renderHookResult<'output>,
  rerender: 'props => unit,
  unmount: unit => unit,
}

type renderHookCallback<'props, 'output> = 'props => 'output

@module("@testing-library/react-hooks")
external renderHook: renderHookCallback<'props, 'output> => renderHookOutput<'props, 'output> =
  "renderHook"

type renderHookOptions<'props> = {
  initialProps?: 'props,
  wrapper?: {"children": React.element} => React.element,
}
@module("@testing-library/react-hooks")
external renderHookWithOptions: (
  renderHookCallback<'props, 'output>,
  ~options: renderHookOptions<'props>,
) => renderHookOutput<'props, 'output> = "renderHook"

// NOTE - These interfaces obey to the ReactStately schema:
// submodules represents a subpackage and nested submodules an interface
// example: https://github.com/adobe/react-spectrum/blob/main/packages/%40react-stately/selection/src/index.ts
// NOTE - Bindings with JavaScript/Typescript is the most dangerous part of
// these applications. And it’s definitely a bad idea to trust yourself
// that you’ve done it correctly. Especially considering the fact that
// it might become outdated during the next release.

// TODO - consider using https://github.com/rescript-association/rescript-core
type jsMap<'value> = Js.Array.array_like<'value>
type jsSet<'value> = Js.Array.array_like<'value>

@genType.as("Key")
type key = string // TODO - React.key

type filterFn = (string, string) => bool

module Selection = {
  // TODO - use `Untagged variants` ReScript v11 feature
  type t // NOTE - polymorphic: "all" | jsSet<key>
  type mode = [#none | #single | #multiple]

  module Manager = {
    type t = {
      selectionMode: mode,
      focusedKey: key,
      selectedKeys: jsSet<key>,
      disabledKeys: jsSet<key>,
      @as("isFocused") focused: bool,
      @as("setFocusedKey") onRequestFocusedKey: (. ~key: option<key>) => unit,
      @as("setSelectedKeys") onRequestSelectedKeys: (. ~keys: array<key>) => unit,
      toggleSelectAll: (. unit) => unit,
      extendSelection: (. ~toKey: key) => unit,
      clearSelection: (. unit) => unit,
      selectAll: (. unit) => unit,
      isSelected: (. ~key: key) => bool,
      isDisabled: (. ~key: key) => bool,
    }
  }
}

module Collection = {
  type rec t = {
    size: int,
    getKeys: (. unit) => jsMap<key>,
    getItem: (. ~key: key) => Js.Nullable.t<node>,
    getKeyBefore: (. ~key: key) => Js.Nullable.t<key>,
    getKeyAfter: (. ~key: key) => Js.Nullable.t<key>,
    getFirstKey: (. unit) => Js.Nullable.t<key>,
    getLastKey: (. unit) => Js.Nullable.t<key>,
  }
  // TODO - ReScript v11 type inheritance: prop `type` should be templated
  and node = {
    @as("type") type_: [#section | #item],
    key: key,
    rendered: React.element,
    childNodes: t,
    hasChildNodes: bool,
  }

  let toArray: t => array<node> = %raw(` values => [...values] `)
}

module Select = {
  type props = {
    children: array<React.element>,
    disabledKeys: array<key>,
    \"aria-label": string,
    placeholder?: string,
    defaultOpen?: bool,
    @as("isRequired") required?: bool,
    @as("isDisabled") disabled?: bool,
    selectedKey: Js.Nullable.t<key>,
    onSelectionChange?: Js.Nullable.t<key> => unit,
  }

  type state = {
    @as("isOpen") opened: bool,
    @as("isFocused") focused: bool,
    collection: Collection.t,
    selectionManager: Selection.Manager.t,
    selectedItem: Js.Nullable.t<Collection.node>,
    selectedKey: Js.Nullable.t<key>,
    disabledKeys: jsSet<key>,
    @as("setFocused") onRequestFocused: bool => unit,
    @as("setSelectedKey") onRequestSelectKey: key => unit,
    @as("toggle") onRequestToggle: unit => unit,
    @as("close") onRequestClose: unit => unit,
  }

  @module("react-stately")
  external useState: (~props: props) => state = "useSelectState"
}

module RadioGroup = {
  type orientation = [#vertical | #horizontal]
  type props = {
    value: Js.Nullable.t<string>,
    orientation?: orientation,
    @as("isDisabled") disabled?: bool,
    @as("label") labelElement?: React.element,
    @as("description") descriptionElement?: React.element,
    onChange: string => unit,
  }

  type state = {
    @as("isFocused") focused: bool,
    selectedValue: Js.Nullable.t<string>,
    @as("setSelectedValue") onRequestSelectedValue: Js.Nullable.t<string> => unit,
  }

  @module("react-stately")
  external useState: (~props: props) => state = "useRadioGroupState"
}

module Table = {
  type elementType = [#thead | #tbody]

  type collectionNodeProps<'columnProps> = {
    @as("isSelectionCell") selectionCell: bool,
    allowsSorting: bool,
    defaultWidth?: float,
    minWidth: float,
    extraColumnProps: 'columnProps,
    // ...
  }

  type sortDirection = [#ascending | #descending]
  @genType type sortDescriptor = {column: key, direction: sortDirection}

  // TODO - in Rescript v11 it should inherit from the base collection type
  type rec collection<'columnProps> = {
    size: int,
    headerRows: array<collectionNode<'columnProps>>,
    rows: array<collectionNode<'columnProps>>,
    columns: array<collectionNode<'columnProps>>,
    columnCount: int,
    body: collectionNode<'columnProps>,
    rowHeaderColumnKeys: jsSet<key>,
    getKeys: (. unit) => jsMap<key>,
    getItem: (. ~key: key) => Js.Nullable.t<collectionNode<'columnProps>>,
    getFirstKey: (. unit) => Js.Nullable.t<key>,
    getLastKey: (. unit) => Js.Nullable.t<key>,
  }
  and collectionNode<'columnProps> = {
    @as("type") type_: elementType,
    key: key,
    parentKey: key,
    index: int,
    rendered: React.element,
    textValue: option<string>,
    column: collectionNode<'columnProps>,
    colspan: int,
    childNodes: collection<'columnProps>,
    props: collectionNodeProps<'columnProps>,
  }

  type props = {
    children: (React.element, React.element),
    selectionMode: Selection.mode,
    showSelectionCheckboxes: bool,
    allowDuplicateSelectionEvents?: bool,
    disabledBehavior: [#selection | #all],
    disabledKeys: array<key>,
    selectedKeys: jsSet<key>,
    sortDescriptor?: sortDescriptor,
    onSortChange?: sortDescriptor => unit,
    onSelectionChange: Selection.t => unit,
  }

  type state<'columnProps> = {
    selectionManager: Selection.Manager.t,
    collection: collection<'columnProps>,
    sortDescriptor?: sortDescriptor,
    sort: (~columnKey: key, ~direction: sortDirection) => unit,
  }

  @module("react-stately")
  external useState: (~props: props) => state<'columnProps> = "useTableState"

  module Column = {
    module Size: {
      type t
      type kind =
        | Static([#px(float) | #pct(float)])
        | Dynamic([#fr(float)])

      let make: kind => t
    } = {
      type t = string
      type kind =
        | Static([#px(float) | #pct(float)])
        | Dynamic([#fr(float)])

      let make = value =>
        switch value {
        | Static(#px(rawValue)) => Obj.magic(rawValue)
        | Static(#pct(rawValue)) => `${rawValue->Float.toString}%`
        | Dynamic(#fr(rawValue)) => `${rawValue->Float.toString}fr`
        }
    }

    module Resize = {
      type props<'columnProps> = {
        tableWidth: option<float>,
        getDefaultWidth: collectionNode<'columnProps> => option<Size.t>,
        getDefaultMinWidth: collectionNode<'columnProps> => option<Size.t>,
      }

      type t = {
        updateResizedColumns: (. ~key: key, ~width: float) => jsMap<(key, Size.t)>,
        getColumnMinWidth: key => float,
        getColumnWidth: key => float,
        // ...
      }

      @module("react-stately")
      external useState: (~props: props<'columnProps>, ~state: state<'columnProps>) => t =
        "useTableColumnResizeState"
    }

    @react.component @module("react-stately")
    external make: (
      ~children: React.element,
      ~minWidth: Size.t=?,
      ~width: Size.t=?,
      ~defaultWidth: Size.t=?,
      ~allowsSorting: bool=?,
      ~extraColumnProps: 'columnProps,
    ) => React.element = "Column"
  }

  // NOTE — Using the `columns` prop and providing a render function allows React Aria
  // to automatically cache the results of rendering each item and avoid re-rendering
  // all items in the collection when only one of them changes. This has big performance
  // benefits for large collections.
  module Header = {
    @react.component @module("react-stately")
    external make: (
      ~children: (~column: 'column) => React.element,
      ~columns: array<'column>,
    ) => React.element = "TableHeader"
  }

  // NOTE — Using the `columnKey` prop and providing a render function allows React Aria
  // to automatically cache the results of rendering each item and avoid re-rendering
  // all items in the collection when only one of them changes. This has big performance
  // benefits for large collections.
  module Row = {
    @react.component @module("react-stately")
    external make: (~children: (~columnKey: key) => React.element) => React.element = "Row"
  }

  module Cell = {
    @react.component @module("react-stately")
    external make: (~children: React.element) => React.element = "Cell"
  }

  // NOTE — Using the `items` prop and providing a render function allows React Aria
  // to automatically cache the results of rendering each item and avoid re-rendering
  // all items in the collection when only one of them changes. This has big performance
  // benefits for large collections.
  module Body = {
    @react.component @module("react-stately")
    external make: (
      ~children: (~item: 'item) => React.element,
      ~items: array<'item>,
    ) => React.element = "TableBody"
  }

  let collectionToArray: collection<'columnProps> => array<
    collectionNode<'columnProps>,
  > = %raw(` values => [...values] `)

  let useColumnResizeState = Column.Resize.useState
}

module DateSegment = {
  type type_ = [#hour | #literal | #minute] // ...
  type t = {
    text: string,
    @as("type") type_: type_,
    @as("isEditable") editable: bool,
    @as("isPlaceholder") hasPlaceholder: bool,
    // ...
  }
}

module TimeField = {
  type segment

  type props = {
    locale: Intl.locale,
    shouldForceLeadingZeros: bool,
    \"aria-label": string,
    value: option<Internationalization.Date.Time.t>,
    onChange: Js.Nullable.t<Internationalization.Date.Time.t> => unit,
    onFocusChange: bool => unit,
    onFocus?: unit => unit,
    onBlur?: unit => unit,
    // ...
  }
  type state = {
    value: option<Internationalization.Date.Time.t>,
    segments: array<DateSegment.t>,
    clearSegment: (. ~type_: DateSegment.type_) => unit,
    // ...
  }

  @module("react-stately")
  external useState: (~props: props) => state = "useTimeFieldState"
}

module NumberField = {
  type signDisplay = [
    | #auto
    | #never
    | #always
    | #exceptZero
  ]

  type style = [
    | #decimal
    | #currency
    | #percent
    | #unit
  ]

  type formatOptions = {
    minimumFractionDigits?: int,
    maximumFractionDigits?: int,
    useGrouping?: bool,
  }

  type props = {
    locale: Intl.locale,
    defaultValue?: float,
    value?: float,
    minValue?: float,
    maxValue?: float,
    step?: float,
    formatOptions?: formatOptions,
    placeholder?: string,
    \"aria-label"?: string,
    isDisabled?: bool,
    autoFocus?: bool,
    onChange?: float => unit,
    onFocus?: unit => unit,
    onFocusChange?: bool => unit,
    onBlur?: unit => unit,
    onKeyDown?: WebAPI.KeyboardEvent.t => unit,
  }

  type state = {
    inputValue: string,
    numberValue: float,
    minValue: option<float>,
    maxValue: option<float>,
    canIncrement: bool,
    canDecrement: bool,
    validate: string => bool,
    setInputValue: string => unit,
    commit: unit => unit,
    increment: unit => unit,
    decrement: unit => unit,
    incrementToMax: unit => unit,
    decrementToMin: unit => unit,
  }

  @module("react-stately")
  external useState: (~props: props) => state = "useNumberFieldState"
}

module Tooltip = {
  module Trigger = {
    type props = {
      @as("isDisabled") disabled?: bool,
      @as("isOpen") opened?: bool,
      defaultOpen?: bool,
      delay?: int,
      closeDelay?: int,
      trigger?: [#focus],
      onOpenChange?: bool => unit,
    }

    type state = {
      @as("isOpen") opened: bool,
      @as("open") onRequestOpen: (~immediate: bool) => unit,
      @as("close") onRequestClose: (~immediate: bool) => unit,
    }

    @module("react-stately")
    external useState: (~props: props) => state = "useTooltipTriggerState"
  }
}

module Toggle = {
  type props = {
    @as("isSelected") selected: bool,
    onChange?: bool => unit,
  }

  type state = {
    @as("isSelected") selected?: bool,
    @as("setSelected") onRequestSelected: bool => unit,
    @as("toggle") onRequestToggle: unit => unit,
  }

  @module("react-stately")
  external useState: (~props: props) => state = "useToggleState"
}

module ComboBox = {
  type props = {
    inputRef?: ReactDOM.domRef,
    listBoxRef?: ReactDOM.domRef,
    popoverRef?: ReactDOM.domRef,
    children: array<React.element>,
    \"aria-label"?: string,
    @as("isDisabled") disabled: bool,
    placeholder?: string,
    defaultFilter?: filterFn,
    allowsCustomValue: bool,
    menuTrigger: [#focus | #input | #manual],
    inputValue: string,
    onInputChange?: string => unit,
    allowsEmptyCollection: bool,
    defaultSelectedKey?: key,
    selectedKey?: Js.Nullable.t<key>,
    onSelectionChange?: Js.Nullable.t<key> => unit,
    onFocusChange?: bool => unit,
    onFocus?: unit => unit,
    onBlur?: unit => unit,
  }

  type state = {
    inputValue: string,
    selectedKey: Js.Nullable.t<key>,
    setInputValue: string => unit,
    setSelectedKey: key => unit,
    @as("isOpen") opened: bool,
    @as("toggle") onRequestToggle: unit => unit,
    @as("close") onRequestClose: unit => unit,
    isFocused: bool,
    collection: Collection.t,
    disabledKeys: jsSet<key>,
  }

  @module("react-stately")
  external useState: (~props: props) => state = "useComboBoxState"
}

module ListBox = {
  module Item = {
    @react.component @module("react-stately")
    external make: (~children: React.element, ~textValue: string) => React.element = "Item"
  }

  module Section = {
    @react.component @module("react-stately")
    external make: (~children: React.element, ~title: React.element) => React.element = "Section"
  }

  type state = {
    collection: Collection.t,
    disabledKeys: jsSet<key>,
    selectionManager: Selection.Manager.t,
  }

  external fromSelectState: Select.state => state = "%identity"
  external fromComboBoxState: ComboBox.state => state = "%identity"
}

module Overlay = {
  module Trigger = {
    // NOTE - this type is different from ReactAria.Overlay.Trigger.ariaProps
    type props = {
      isOpen?: option<bool>,
      defaultOpen: bool,
      onOpenChange?: option<bool => unit>,
    }

    type state = {
      @as("isOpen") opened: bool,
      @as("close") onRequestClose: unit => unit,
      @as("toggle") onRequestToggle: unit => unit,
    }

    @module("react-stately")
    external useState: (~props: props) => state = "useOverlayTriggerState"
  }
}

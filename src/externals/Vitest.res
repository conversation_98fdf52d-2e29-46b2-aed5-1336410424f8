// NOTE — Auto-completion (with rescript-vsccode) on pipe operator
// seem only worked well for type t in modules. So the test writing experience
// isn't great today. The bindings could be rewrited to improved DX at some point.
// A first step could be to create an Except module with all related matcher
// relying on t type (actual type except).

@val external describe: (string, @uncurry (unit => unit)) => unit = "describe"
@val external describeOnly: (string, @uncurry (unit => unit)) => unit = "describe.only"

@val external it: (string, @uncurry (unit => unit)) => unit = "it"
@val external itOnly: (string, @uncurry (unit => unit)) => unit = "it.only"
@val external itPromise: (string, @uncurry (unit => Js.Promise.t<unit>)) => unit = "it"
@val external itPromiseTimeout: (string, @uncurry (unit => Js.Promise.t<unit>), int) => unit = "it"
@val external itPromiseOnly: (string, @uncurry (unit => Js.Promise.t<unit>)) => unit = "it.only"

@val external test: (string, @uncurry (unit => unit)) => unit = "test"
@val external testOnly: (string, @uncurry (unit => unit)) => unit = "test.only"
@val external testPromise: (string, @uncurry (unit => Js.Promise.t<unit>)) => unit = "test"
@val external testPromiseOnly: (string, @uncurry (unit => Js.Promise.t<unit>)) => unit = "test.only"

@scope("test") @val
external testEach1: array<'a> => (. string, @uncurry ('a => unit)) => unit = "each"
@scope("test") @val
external testEachPromise1: array<'a> => (. string, @uncurry ('a => Js.Promise.t<unit>)) => unit =
  "each"
@scope("test") @val
external testEach2: array<('a, 'b)> => (. string, @uncurry ('a, 'b) => unit) => unit = "each"
@scope("test") @val
external testEachPromise2: array<('a, 'b)> => (
  . string,
  @uncurry ('a, 'b) => Js.Promise.t<unit>,
) => unit = "each"
@scope("test") @val
external testEach3: array<('a, 'b, 'c)> => (. string, @uncurry ('a, 'b, 'c) => unit) => unit =
  "each"
@scope("test") @val
external testEach4: array<('a, 'b, 'c, 'd)> => (
  . string,
  @uncurry ('a, 'b, 'c, 'd) => unit,
) => unit = "each"

@val external todo: string => unit = "it.todo"

@val external beforeEach: (@uncurry (unit => unit)) => unit = "beforeEach"
@val external beforeAll: (@uncurry (unit => unit)) => unit = "beforeAll"
@val external afterEach: (@uncurry (unit => unit)) => unit = "afterEach"
@val external afterAll: (@uncurry (unit => unit)) => unit = "afterAll"

type expect<'a>
@val external expect: 'a => expect<'a> = "expect"
@get external not: expect<'a> => expect<'a> = "not"

@send external toBe: (expect<'a>, 'a) => unit = "toBe"
@send external toBeNull: expect<Js.Nullable.t<'a>> => unit = "toBeNull"
@send external toBeDefined: expect<'a> => unit = "toBeDefined"
@send external toThrow: expect<'a> => unit = "toThrow"
@send external toStrictEqual: (expect<'a>, 'a) => unit = "toStrictEqual"
@send external toEqual: (expect<'a>, 'a) => unit = "toEqual"
@send external toUnsafeStrictEqual: (expect<'a>, 'any) => unit = "toStrictEqual"
@send external toMatchObject: (expect<'a>, 'a) => unit = "toMatchObject"
@send external toMatchSnapshot: expect<'a> => unit = "toMatchSnapshot"
@send external toHaveLength: (expect<'a>, int) => unit = "toHaveLength"
@send external toHaveProperty: (expect<'a>, string, 'property) => unit = "toHaveProperty"
@send external toBeLessThan: (expect<'float>, float) => unit = "toBeLessThan"
@send external toBeGreaterThan: (expect<'float>, float) => unit = "toBeGreaterThan"
@send external toBeGreaterThanOrEqual: (expect<'float>, float) => unit = "toBeGreaterThanOrEqual"
@send external toMatch: (expect<'string>, string) => unit = "toMatch"
@send external toContain: (expect<array<'a>>, 'a) => unit = "toContain"

// jest-dom matchers > https://github.com/testing-library/jest-dom#table-of-contents
@send external toHaveAttribute: (expect<'a>, string) => unit = "toHaveAttribute"
@send external toHaveAttributeValue: (expect<'a>, string, 'value) => unit = "toHaveAttribute"
@send external toHaveDisplayValue: (expect<'a>, string) => unit = "toHaveDisplayValue"

type fn<'fn, 'args, 'return>
// NOTE - this identity fun name can be confusing with other fn1, fn2 …
// but it's a very distinct thing.
external fn: fn<'fn, _, _> => 'fn = "%identity"

@scope("vi") external fn0: unit => fn<unit => unit, unit, unit> = "fn"
@scope("vi") external fn1: ('a => 'b) => fn<'a => 'b, 'a, 'b> = "fn"
@scope("vi")
external fn2: (('a, 'b) => 'return) => fn<('a, 'b) => 'return, ('a, 'b), 'return> = "fn"
@scope("vi")
external fn3: (('a, 'b, 'c) => 'return) => fn<('a, 'b, 'c) => 'return, ('a, 'b, 'c), 'return> = "fn"
@scope("mock") @get external calls1: fn<_, 'a, _> => array<array<'a>> = "calls"
@scope("mock") @get external calls2: fn<_, ('a, 'b), _> => array<('a, 'b)> = "calls"
@scope("mock") @get external calls3: fn<_, ('a, 'b, 'c), _> => array<('a, 'b, 'c)> = "calls"
@send external mockClear: fn<_, _, _> => unit = "mockClear"

@send external toHaveBeenCalled: expect<fn<_, _, _>> => unit = "toHaveBeenCalled"
@send external toHaveBeenCalledTimes: (expect<fn<_, _, _>>, int) => unit = "toHaveBeenCalledTimes"
@send
external toHaveBeenCalledWith1: (expect<fn<_, 'a, _>>, 'a) => unit = "toHaveBeenCalledWith"
@send
external toHaveBeenCalledWith2: (expect<fn<_, ('a, 'b), _>>, 'a, 'b) => unit =
  "toHaveBeenCalledWith"
@send
external toHaveBeenCalledWith3: (expect<fn<_, ('a, 'b, 'c), _>>, 'a, 'b, 'c) => unit =
  "toHaveBeenCalledWith"
@send
external toHaveBeenLastCalledWith1: (expect<fn<_, 'a, _>>, 'a) => unit = "toHaveBeenLastCalledWith"
@send
external toHaveBeenLastCalledWith2: (expect<fn<_, ('a, 'b), _>>, 'a, 'b) => unit =
  "toHaveBeenLastCalledWith"
@send
external toHaveBeenLastCalledWith3: (expect<fn<_, ('a, 'b, 'c), _>>, 'a, 'b, 'c) => unit =
  "toHaveBeenLastCalledWith"
@send
external toHaveBeenNthCalledWith1: (expect<fn<_, 'a, _>>, int, 'a) => unit =
  "toHaveBeenNthCalledWith"
@send
external toHaveBeenNthCalledWith2: (expect<fn<_, ('a, 'b), _>>, int, 'a, 'b) => unit =
  "toHaveBeenNthCalledWith"
@send
external toHaveBeenNthCalledWith3: (expect<fn<_, ('a, 'b, 'c), _>>, int, 'a, 'b, 'c) => unit =
  "toHaveBeenNthCalledWith"

@scope("vi") @val external spyOn0: ({..}, string) => fn<unit => unit, unit, unit> = "spyOn"
@scope("vi") @val external spyOn1: ({..}, string) => fn<'a => 'b, 'a, 'b> = "spyOn"

@send
external mockImplementation0: (
  fn<unit => unit, unit, unit>,
  unit => 'b,
) => fn<unit => unit, unit, unit> = "mockImplementation"
@send
external mockImplementation1: (fn<'a => 'b, 'a, 'b>, 'a => 'b) => fn<'a => 'b, 'a, 'b> =
  "mockImplementation"

@scope("vi") @val external mockPackage: (string, unit => 'a) => unit = "mock"

@send external mockRestore: 'fn => unit = "mockRestore"
@scope("vi") @val external restoreAllMocks: unit => unit = "restoreAllMocks"

// Custom matchers from /tests/extends.ts
@send external toBeNone: expect<option<'a>> => unit = "toBeNone"
@send external toRaise: (expect<'a>, exn) => unit = "toRaise"

// Helpers for futures
let itFuture = (testName: string, testFn: unit => Future.t<'a>) =>
  itPromise(testName, async () => (await testFn()->FuturePromise.toPromise)->ignore)
let itFutureOnly = (testName: string, testFn: unit => Future.t<'a>) =>
  itPromiseOnly(testName, async () => (await testFn()->FuturePromise.toPromise)->ignore)

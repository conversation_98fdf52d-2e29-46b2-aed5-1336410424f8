type iban = {last4: string}

type info = {
  corporateName: option<string>,
  shopName: option<string>,
  email: option<string>,
  phone: option<string>,
  billingAddress: option<CorporateEntity.Address.t>,
  shippingAddress: option<CorporateEntity.Address.t>,
  vatNumber: option<string>,
  iban: option<iban>,
}

let acceptedCountryCodes = [CountryCode.FR, BE, LU]

module BillingPlanKind = {
  type t =
    | Standard
    | Company
    | Boost
    | Warehouse

  let fromString = value =>
    switch value {
    | "standard" => Ok(Standard)
    | "company" => Ok(Company)
    | "boost" => Ok(Boost)
    | "warehouse" => Ok(Warehouse)
    | _ => Error()
    }

  let toString = value =>
    switch value {
    | Standard => "Standard"
    | Company => "Company"
    | Boost => "Boost"
    | Warehouse => "Warehouse"
    }
}

module BillingIssue = {
  type t =
    | MissingPaymentMethod
    | InvalidBillingMandate
    | InvoicePaymentOverdue

  let getPriority = issue => {
    switch issue {
    | MissingPaymentMethod => 1
    | InvalidBillingMandate => 2
    | InvoicePaymentOverdue => 3
    }
  }

  type issueMessage = {
    title: string,
    subtitle: string,
  }

  type shopIssue = {
    shopId: string,
    issue: t,
  }

  let fromString = value =>
    switch value {
    | "MissingPaymentMethod" => Ok(MissingPaymentMethod)
    | "InvalidBillingMandate" => Ok(InvalidBillingMandate)
    | "InvoicePaymentOverdue" => Ok(InvoicePaymentOverdue)
    | _ => Error()
    }

  let mostImportantShopIssue = (issues: array<shopIssue>): option<shopIssue> => {
    Array.reduce(issues, None, (mostImportant, current) => {
      switch mostImportant {
      | None => Some(current)
      | Some(most) =>
        if getPriority(current.issue) < getPriority(most.issue) {
          Some(current)
        } else {
          Some(most)
        }
      }
    })
  }

  let mostImportantIssue = (issues: array<t>): option<t> => {
    Array.reduce(issues, None, (mostImportant, current) => {
      switch mostImportant {
      | None => Some(current)
      | Some(most) =>
        if getPriority(current) < getPriority(most) {
          Some(current)
        } else {
          Some(most)
        }
      }
    })
  }
}

type customerPlan = {
  kind: BillingPlanKind.t,
  upcomingInvoiceDate: option<Js.Date.t>,
  quantity: int,
}

type customerOption = {
  name: string,
  upcomingInvoiceDate: option<Js.Date.t>,
  quantity: int,
}

type customerSubscriptions = {
  plans: array<customerPlan>,
  options: array<customerOption>,
}

module StatusesRequest = {
  let decodeBillingIssues = failure => {
    let {Request.data: data, kind} = failure
    switch (
      kind->BillingIssue.fromString,
      data->Option.flatMap(Json.decodeDict)->Json.flatDecodeDictFieldString("shopId"),
    ) {
    | (Ok(issue), Some(shopId)) => Some({BillingIssue.shopId, issue})
    | (_, _) => None
    }
  }

  let make = () =>
    Request.make(
      Env.gatewayUrl() ++ "/billing-accounts/audit",
      ~method=#GET,
    )->Future.map(result => {
      switch result {
      | Ok(issues) => {
          let arr = issues->Json.decodeArray
          switch arr {
          | Some(failures) =>
            switch failures->Request.decodeInvalidRequestFailures {
            | Some(failures) => Ok(Some(failures->Array.keepMap(decodeBillingIssues)))
            | _ => Ok(None)
            }
          | _ => Ok(None)
          }
        }

      | Error(error) => Error(error)
      }
    })
}

module InvoiceStatus = {
  type t = Open | Paid | PaidViaCreditNote | Uncollectible | Void
  let fromString = status =>
    switch status {
    | "open" => Some(Open)
    | "paid" => Some(Paid)
    | "paid_via_credit_note" => Some(Paid)
    | "uncollectible" => Some(Uncollectible)
    | "void" => Some(Void)
    | _ => None
    }
  let toString = status =>
    switch status {
    | Open => "Amount due"
    | Paid => "Paid"
    | PaidViaCreditNote => "Paid via credit note"
    | Uncollectible => "Uncollectible"
    | Void => "Credited balance"
    }
}

type invoice = {
  id: string,
  number: string,
  total: float,
  status: option<InvoiceStatus.t>,
  paymentLink: option<string>,
  pdfLink: option<string>,
  date: Js.Date.t,
}

module InvoicesRequest = {
  let decodeInvoice = json => {
    let dict = json->Json.decodeDict
    switch (
      dict->Json.flatDecodeDictFieldString("id"),
      dict->Json.flatDecodeDictFieldString("number"),
      dict->Json.flatDecodeDictFieldFloat("total"),
      dict->Json.flatDecodeDictFieldString("status"),
      dict->Json.flatDecodeDictFieldString("paymentLink"),
      dict->Json.flatDecodeDictFieldString("pdfLink"),
      dict->Json.flatDecodeDictFieldFloat("date"),
    ) {
    | (Some(id), Some(number), Some(total), status, paymentLink, pdfLink, Some(date)) =>
      Some({
        id,
        number,
        total,
        status: switch status {
        | Some(status) => status->InvoiceStatus.fromString
        | None => None
        },
        paymentLink,
        pdfLink,
        date: date->Js.Date.fromFloat,
      })
    | _ => None
    }
  }

  let decodeResult = json =>
    switch json->Json.decodeArray {
    | Some(resultItems) => resultItems->Array.keepMap(decodeInvoice)
    | None => []
    }

  let endpoint = Env.gatewayUrl() ++ "/billing-account/invoices/"

  type makeT = (~shopId: string) => Future.t<result<array<invoice>, Request.error>>

  let make = (~shopId) => Request.make(endpoint ++ shopId, ~method=#GET)->Future.mapOk(decodeResult)
}

let invoicesRequest = InvoicesRequest.make

open Intl
open BillingAccount

module TableRow = {
  type t = {
    number: string,
    date: string,
    total: float,
    status: option<InvoiceStatus.t>,
    paymentLink: option<string>,
    pdfLink: string,
  }

  let keyExtractor = row => row.number

  let sanitize = string =>
    string
    ->Js.String2.toLowerCase
    ->Js.String2.normalizeByForm("NFD")
    ->Js.String2.replaceByRe(%re("/[\u0300-\u036f]/g"), "")
    ->Js.String2.replaceByRe(%re("/[^a-zA-Z0-9 ]/g"), "")

  let match = (row, query) =>
    switch query {
    | "" => true
    | query =>
      sanitize(row.number)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.date)->Js.String2.includes(sanitize(query)) ||
      sanitize(row.total->Float.toString)->Js.String2.includes(sanitize(query))
    }

  let fromQueryItem = (queryItem: invoice) => {
    number: queryItem.number,
    date: queryItem.date->Intl.dateTimeFormat(~dateStyle=#short),
    total: queryItem.total,
    status: queryItem.status,
    paymentLink: queryItem.paymentLink,
    pdfLink: queryItem.pdfLink->Option.getWithDefault(""),
  }
}

module TableRows = {
  let rowsPerPage = 10
  let totalPages = rows =>
    (rows->Array.size->Float.fromInt /. rowsPerPage->Float.fromInt)->Js.Math.ceil_float->Float.toInt

  let search = (rows, searchQuery) => rows->Array.keep(row => TableRow.match(row, searchQuery))
  let paginate = (rows, currentPage) =>
    rows->Array.slice(~len=rowsPerPage, ~offset=(currentPage - 1) * rowsPerPage)

  let fromQueryAllShops = queryAllShops => queryAllShops->Array.map(TableRow.fromQueryItem)
}

module InvoiceStatusBadge = {
  @react.component
  let make = (~value, ~size=#medium) => {
    let variation = switch value {
    | Some(InvoiceStatus.Paid) | Some(PaidViaCreditNote) => #success
    | Some(Open) => #warning
    | _ => #information
    }
    <Badge variation size>
      {t(value->Option.getWithDefault(Void)->InvoiceStatus.toString)->React.string}
    </Badge>
  }
}

let tableColumns = [
  {
    Table.key: "number",
    name: t("Number"),
    layout: {minWidth: 260.->#px, width: 1.5->#fr},
    render: ({data: {TableRow.number: number, pdfLink}}) =>
      <Box spaceTop=#small spaceBottom=#small>
        <TextLink text=number to=Navigation.Url(pdfLink->Url.make) />
      </Box>,
  },
  {
    key: "date",
    name: t("Date"),
    layout: {minWidth: 160.->#px},
    render: ({data: {date}}) => <TextStyle> {date->React.string} </TextStyle>,
  },
  {
    Table.key: "amount",
    name: t("Amount"),
    layout: {minWidth: 160.->#px},
    render: ({data: {total: amount}}) =>
      <TextStyle>
        {amount
        ->Intl.currencyFormat(~currency=#EUR, ~minimumFractionDigits=2, ~maximumFractionDigits=2)
        ->React.string}
      </TextStyle>,
  },
  {
    key: "status",
    name: t("Status"),
    layout: {minWidth: 250.->#px},
    render: ({data: {status, paymentLink}}) =>
      switch (status, paymentLink) {
      | (Some(Open), Some(paymentLink)) =>
        <Inline space={#normal} alignY={#center}>
          <InvoiceStatusBadge value=status />
          <TextLink
            text={t("Pay now") ++ "→"} to=Navigation.Url(paymentLink->Url.make) highlighted=true
          />
        </Inline>
      | (_, _) => <InvoiceStatusBadge value=status />
      },
  },
  {
    key: "link",
    name: "",
    layout: {minWidth: 20.->#px, alignX: #flexEnd},
    render: ({data: {pdfLink}}) =>
      <Navigation.Link to=Navigation.Url(pdfLink->Url.make)>
        <RoundButton icon=#download onPress={_ => ()} />
      </Navigation.Link>,
  },
]

module Reducer = {
  type state = {
    searchQuery: string,
    currentPage: int,
    asyncResult: AsyncResult.t<array<invoice>, unit>,
  }

  let initialState = {
    searchQuery: "",
    currentPage: 1,
    asyncResult: AsyncResult.notAsked(),
  }

  type action =
    | SearchQueryChanged(string)
    | AsyncResultGet(AsyncResult.t<array<invoice>, unit>)
    | Paginated(LegacyPagination.action, int)

  let make = (prevState, action) =>
    switch action {
    | SearchQueryChanged(searchQuery) => {...prevState, currentPage: 1, searchQuery}
    | AsyncResultGet(asyncResult) => {...prevState, asyncResult, currentPage: 1}
    | Paginated(paginateAction, totalPages) => {
        ...prevState,
        currentPage: switch (paginateAction, prevState.currentPage, totalPages) {
        | (LegacyPagination.First, _, _) | (Prev, 1, _) => 1
        | (Prev, _, _) => prevState.currentPage - 1
        | (Next, prevPage, totalPages) if prevPage >= totalPages => prevPage
        | (Next, _, _) => prevState.currentPage + 1
        | (Last, _, _) => totalPages
        },
      }
    }
}

@react.component
let make = (~shopId, ~invoicesRequest: InvoicesRequest.makeT, ~preview=false) => {
  let (state, dispatch) = React.useReducer(Reducer.make, Reducer.initialState)
  let {asyncResult, currentPage, searchQuery} = state

  React.useEffect1(() => {
    let request = invoicesRequest(~shopId)->Future.mapError(_ => ())

    dispatch(AsyncResultGet(AsyncResult.loading()))
    request->Future.get(result => dispatch(AsyncResultGet(AsyncResult.done(result))))

    Some(() => request->Future.cancel)
  }, [shopId])

  let tableRows = asyncResult->AsyncResult.mapOk(TableRows.fromQueryAllShops)
  let searchedAndFilteredTableRows = tableRows->AsyncResult.mapOk(rows => {
    switch preview {
    | true => rows->Array.slice(~offset=0, ~len=5)
    | false => rows->TableRows.search(searchQuery)
    }
  })

  let totalPages = switch searchedAndFilteredTableRows {
  | Reloading(Ok(rows)) | Done(Ok(rows)) => rows->TableRows.totalPages
  | _ => 1
  }

  let searchedAndPaginatedTableRows =
    searchedAndFilteredTableRows->AsyncResult.mapOk(rows => rows->TableRows.paginate(currentPage))

  let onRequestSearch = queryString => dispatch(SearchQueryChanged(queryString))
  let onRequestPaginate = paginateAction => dispatch(Paginated(paginateAction, totalPages))

  let placeholderNoRows = switch (searchedAndPaginatedTableRows, state.asyncResult) {
  | (Done(Ok([])), Done(Ok(rows))) if rows->Array.size > 0 && preview == false =>
    <EmptyState
      illustration=Illustration.notFound
      title={t("No result were found.")}
      text={t("Try again with another keyword or:")}>
      <Button variation=#neutral onPress={_ => dispatch(SearchQueryChanged(""))}>
        {t("Clear search query")->React.string}
      </Button>
    </EmptyState>
  | (_, Done(Ok([]))) =>
    <EmptyState illustration=Illustration.notFound title={t("Sorry, no invoices were found.")} />

  | _ => EmptyState.error
  }

  let searchBar = if !preview {
    <Box spaceX=#large spaceBottom=#xmedium>
      <SearchBar value=searchQuery placeholder={t("Search an invoice")} onChange=onRequestSearch />
    </Box>
  } else {
    React.null
  }

  <>
    <TableView
      columns=tableColumns
      data=searchedAndPaginatedTableRows
      keyExtractor=TableRow.keyExtractor
      placeholderEmptyState=placeholderNoRows
      searchBar
    />
    {switch (tableRows, preview) {
    | (Done(_), false) => <LegacyPagination currentPage totalPages onRequestPaginate />
    | (Done(Ok(rows)), true) if preview && rows->Array.length > 5 =>
      <ShowAllDataLink
        to=Route(SettingsRoutes.billingAccountInvoicesRoute(~shopId)) text={t("Show all invoices")}
      />
    | _ => React.null
    }}
  </>
}

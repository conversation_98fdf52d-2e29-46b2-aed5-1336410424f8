open Accounting.Actions
open Accounting.Types

@react.component
let make = (~product, ~editable=false, ~onRequestDispatch as dispatch) => {
  let (productId, unitPrice) = switch product {
  | Unit({id, unitPrice}) | Bulk({id, unitPrice}, _) => (id, unitPrice)
  }
  let formattedUnitPrice = {
    let formattedUnitPrice =
      unitPrice->Intl.currencyFormat(
        ~currency=#EUR,
        ~minimumFractionDigits=3,
        ~maximumFractionDigits=3,
      )
    switch product {
    | Bulk({capacityUnit: Some(unit)}, _) => `${formattedUnitPrice}/${unit}`
    | _ => formattedUnitPrice
    }
  }
  let inputAppender = switch product {
  | Bulk({capacityUnit: Some(unit)}, _) =>
    InputNumberField.Custom(#EUR->Intl.toCurrencySymbol ++ `/${unit}`)
  | _ => Currency(#EUR)
  }

  if editable {
    <View>
      <InputNumberField
        appender=inputAppender
        minValue=0.
        precision=3
        hideStepper=true
        shrinkInput=true
        value=unitPrice
        onChange={newPrice => ProductUnitPriceUpdated(productId, newPrice)->dispatch}
      />
    </View>
  } else {
    <ProductPriceTableCell value=Some(formattedUnitPrice) />
  }
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["product"] === newProps["product"] && oldProps["editable"] === newProps["editable"]
)

open Accounting.Types
open Accounting.Actions

@react.component
let make = (
  ~product,
  ~popover: Popover.t,
  ~popoverAriaProps: ReactAria.Overlay.Trigger.t,
  ~popoverTriggerRef,
  ~onRequestDispatch as dispatch,
) => {
  let (productId, discount) = switch product {
  | Unit({id, discounts}) | Bulk({id, discounts}, _) => (id, discounts[0])
  }

  React.useEffect1(() => {
    if !popover.opened {
      switch discount {
      | Some({kind: Percent | Currency, value: 0.} as discount) =>
        ProductDiscountRemoved(productId, discount.id)->dispatch
      | Some({kind: Free} as discount) if discount.quantity->Big.toFloat === 0. =>
        ProductDiscountRemoved(productId, discount.id)->dispatch
      | _ => ()
      }
    }
    None
  }, [popover.opened])

  let onPressRemoveDiscount = React.useCallback0(() => popover.onRequestToggle())

  <Popover triggerRef=popoverTriggerRef state=popover modal=false placement=#"bottom end">
    <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
      <Box spaceX=#large spaceY=#xlarge>
        <CartProductDiscountFieldset
          product ?discount onRequestDispatch=dispatch onPressRemoveDiscount
        />
      </Box>
    </Popover.Dialog>
  </Popover>
}

let make = React.memo(make)

open Intl
open Style

let styles = StyleSheet.create({
  "wrapper": style(~flex=1., ~alignItems=#flexEnd, ()),
  "row": style(
    ~flexDirection=#row,
    ~alignItems=#center,
    ~paddingTop=3.->dp,
    ~paddingBottom=1.->dp,
    (),
  ),
  "subrow": style(
    ~flexDirection=#row,
    ~alignItems=#center,
    ~left=2.->dp,
    ~paddingTop=3.->dp,
    ~paddingBottom=5.->dp,
    (),
  ),
  "label": style(~minWidth=194.->dp, ()),
  "amount": style(~alignItems=#flexStart, ~minWidth=100.->dp, ()),
})

let bigToFloat = Accounting.Formatter.Utils.bigToFloat
let currencyFormat = value =>
  value->Intl.currencyFormat(~currency=#EUR, ~minimumFractionDigits=3, ~maximumFractionDigits=3)

@react.component
let make = (~cart: Accounting.Types.cart) => {
  let formattedTotalAmountExcludingTaxes =
    bigToFloat(cart.totalAmountExcludingTaxes->Option.getExn)->currencyFormat
  let formattedTotalAmountExcludingGlobalDiscounts =
    bigToFloat(cart.totalAmountExcludingGlobalDiscounts->Option.getExn)->currencyFormat
  let formattedTotalAmountOfGoods =
    bigToFloat(cart.totalAmountOfGoods->Option.getExn)->currencyFormat
  let formattedTotalAmountIncludingTaxes =
    bigToFloat(cart.totalAmountIncludingTaxes->Option.getExn)->currencyFormat

  <View style={styles["wrapper"]}>
    {switch cart.discounts[0] {
    | Some(discount) if discount.value > 0. =>
      let formattedDiscountAmount = bigToFloat(discount.amount->Option.getExn)->currencyFormat

      <Box spaceBottom=#small>
        <View style={styles["row"]}>
          <View style={styles["label"]}>
            <TextStyle size=#large> {t("Sub total excl. VAT")->React.string} </TextStyle>
          </View>
          <View style={styles["amount"]}>
            <TextStyle size=#large>
              {formattedTotalAmountExcludingGlobalDiscounts->React.string}
            </TextStyle>
          </View>
        </View>
        <View style={styles["row"]}>
          <View style={styles["label"]}>
            <TextStyle size=#large>
              {template(
                t("Discount {{amount}}"),
                ~values={
                  "amount": switch discount.kind {
                  | Percent =>
                    (discount.value /. 100.)->Intl.percentFormat(~maximumFractionDigits=2)
                  | _ => discount.value->Intl.currencyFormat(~currency=#EUR)
                  },
                },
                (),
              )->React.string}
            </TextStyle>
          </View>
          <View style={styles["amount"]}>
            <TextStyle size=#large> {formattedDiscountAmount->React.string} </TextStyle>
          </View>
        </View>
      </Box>
    | _ => React.null
    }}
    <View style={styles["row"]}>
      <View style={styles["label"]}>
        <TextStyle weight={cart.taxesFree ? #strong : #regular} size=#large>
          {t("Total amount excl. VAT:")->React.string}
        </TextStyle>
      </View>
      <View style={styles["amount"]}>
        <TextStyle weight={cart.taxesFree ? #strong : #regular} size=#large>
          {formattedTotalAmountExcludingTaxes->React.string}
        </TextStyle>
      </View>
    </View>
    <View style={styles["subrow"]}>
      <Stack space=#xxsmall>
        <Inline>
          <View style={styles["label"]}>
            <TextStyle variation=#normal size=#xsmall>
              {t("Goods total excl. VAT")->React.string}
            </TextStyle>
          </View>
          <View style={styles["amount"]}>
            <TextStyle variation=#normal size=#xsmall>
              {formattedTotalAmountOfGoods->React.string}
            </TextStyle>
          </View>
        </Inline>
        {cart.fees
        ->Option.getExn
        ->Array.mapWithIndex((index, fee) => {
          let formattedFeeAmount = bigToFloat(fee.amount)->currencyFormat

          <Inline key={index->Int.toString}>
            <View style={styles["label"]}>
              <TextStyle variation=#normal size=#xsmall>
                {template(
                  t("Total {{feeKind}}"),
                  ~values={
                    "feeKind": switch fee.kind {
                    | Transport => t("Transport")
                    | Taxes => t("Taxes")
                    | Other => t("Other")
                    },
                  },
                  (),
                )->React.string}
              </TextStyle>
            </View>
            <View style={styles["amount"]}>
              <TextStyle variation=#normal size=#xsmall>
                {formattedFeeAmount->React.string}
              </TextStyle>
            </View>
          </Inline>
        })
        ->React.array}
        <Box spaceY=#xxsmall />
      </Stack>
    </View>
    {switch cart.taxes {
    | Some(taxes) =>
      taxes
      ->Array.map(tax => {
        let formattedTaxAmount = bigToFloat(tax.amount->Option.getExn)->currencyFormat

        <View key={formattedTaxAmount ++ tax.rate->Js.Float.toString} style={styles["row"]}>
          <View style={styles["label"]}>
            <TextStyle size=#large>
              {template(
                t("VAT {{amount}}"),
                ~values={"amount": tax.rate->Js.Float.toString->Js.String2.replace(".", ",")},
                (),
              )->React.string}
            </TextStyle>
          </View>
          <View style={styles["amount"]}>
            <TextStyle size=#large> {formattedTaxAmount->React.string} </TextStyle>
          </View>
        </View>
      })
      ->React.array
    | None => React.null
    }}
    {if cart.taxesFree {
      <View style={style(~alignSelf=#flexStart, ~paddingHorizontal=28.->dp, ())}>
        <TextStyle variation=#normal size=#large>
          {template(
            t("{{amount}} of deductible VAT (0.00%)"),
            ~values={
              "amount": currencyFormat(0.00),
            },
            (),
          )->React.string}
        </TextStyle>
      </View>
    } else {
      <View style={styles["row"]}>
        <View style={styles["label"]}>
          <TextStyle weight=#strong size=#large>
            {t("Total amount incl. VAT:")->React.string}
          </TextStyle>
        </View>
        <View style={styles["amount"]}>
          <TextStyle weight=#strong size=#large>
            {formattedTotalAmountIncludingTaxes->React.string}
          </TextStyle>
        </View>
      </View>
    }}
  </View>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["cart"] === newProps["cart"]
)

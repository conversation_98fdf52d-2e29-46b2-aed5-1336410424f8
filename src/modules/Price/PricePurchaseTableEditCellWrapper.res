open Style

@react.component
let make = (
  ~value: float,
  ~formattedPurchasePrice: option<string>,
  ~bulkUnit: option<string>,
  ~minRetailPrice: option<float>=?,
  ~disabled: bool=false,
  ~variantId: string,
  ~compact=false,
) => {
  let (formattedValue, setFormattedValue) = React.useState(() => formattedPurchasePrice)
  let (focused, setFocused) = React.useState(_ => false)
  let onFocus = () => setFocused(_ => true)
  let onBlur = () => setFocused(_ => false)
  let (ref, hovered) = Hover.use()

  // Sets new formattedValue from input purchasePrice mutation
  let onChange = React.useCallback1(
    (value: float) =>
      setFormattedValue(_ => Some(
        value->Intl.currencyFormat(
          ~currency=#EUR,
          ~minimumFractionDigits=3,
          ~maximumFractionDigits=3,
        ) ++ bulkUnit->Option.mapWithDefault("", unit => "/" ++ unit),
      )),
    [bulkUnit],
  )

  // Sets focus when switching to input
  let onPressButton = _ => onFocus()

  <Inline>
    <View ref style={style(~marginLeft=-13.->dp, ())}>
      {switch focused {
      | true =>
        <PricePurchaseTableEditCell
          autoFocused=true value ?minRetailPrice bulkUnit variantId onChange onFocus onBlur
        />
      | _ =>
        <ButtonPhased ref hovered disabled onPress=onPressButton>
          <ProductPriceTableCell size={compact ? #xsmall : #normal} value=formattedValue />
        </ButtonPhased>
      }}
    </View>
  </Inline>
}

let make = React.memo(make)

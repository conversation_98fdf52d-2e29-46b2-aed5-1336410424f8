open Intl

type globalFeeKind =
  | FeePerUnit
  | FeeProratedByPrice

type value = {
  amount: float,
  globalFeeKind: globalFeeKind,
}

let globalFeeKindToText = kind =>
  switch kind {
  | FeePerUnit => t("Per unit")
  | FeeProratedByPrice => t("Price prorata")
  }

@react.component
let make = (~opened, ~onCommit, ~onRequestClose) => {
  let (value, setValue) = React.useState(() => {amount: 0., globalFeeKind: FeePerUnit})

  let captureEvent = SessionTracker.useCaptureEvent()

  let onCommit = React.useCallback1(() => {
    onCommit(value)
    captureEvent(#cart_global_shipping_costs_click_apply)
  }, [value])

  <Modal
    title={t("Global shipping costs")}
    variation=#secondary
    backgroundColor=Colors.neutralColor00
    renderStartText={() => <Inline grow=true> React.null </Inline>}
    abortButtonText={t("Cancel")}
    commitButtonText={t("Apply")}
    commitButtonVariation=#primary
    commitButtonCallback=onCommit
    opened
    onRequestClose>
    <Box spaceX=#large spaceY=#large>
      <Stack space=#large>
        <InputNumberField
          label={t("Total amount excl. VAT")}
          precision=2
          minValue=0.
          required=false
          appender=Currency(#EUR)
          value=value.amount
          onChange={amount =>
            setValue(prev => {
              ...prev,
              amount,
            })}
        />
        <InputSegmentedControlsField
          label={t("Allocation of costs")}
          tooltip={<>
            <Tooltip.Span
              text={t("order_cart.global_fee_picker_modal.allocation_of_costs.tooltip.label_1")}
              italic=true
              bold=true
            />
            <Tooltip.Br />
            <Tooltip.Span
              text={t("order_cart.global_fee_picker_modal.allocation_of_costs.tooltip.content_1")}
            />
            <Tooltip.Br />
            <Tooltip.Br space=#small />
            <Tooltip.Span
              text={t("order_cart.global_fee_picker_modal.allocation_of_costs.tooltip.label_2")}
              italic=true
              bold=true
            />
            <Tooltip.Br />
            <Tooltip.Span
              text={t("order_cart.global_fee_picker_modal.allocation_of_costs.tooltip.content_2")}
            />
          </>}
          required=false
          grow=true
          options=list{FeePerUnit, FeeProratedByPrice}
          optionToText=globalFeeKindToText
          value=value.globalFeeKind
          onChange={globalFeeKind =>
            setValue(prev => {
              ...prev,
              globalFeeKind,
            })}
        />
        <Box spaceBottom=#normal>
          <Banner textStatus={Info(t("order_cart.global_fee_picker_modal.info_banner"))} />
        </Box>
      </Stack>
    </Box>
  </Modal>
}

let make = React.memo(make)

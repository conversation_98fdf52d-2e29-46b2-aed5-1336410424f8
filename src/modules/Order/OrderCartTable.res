open Intl

exception ProductTaxNotFound

type disabledColumn =
  | StatusBeforeReception
  | StatusAtAfterReception

let keyExtractor = row =>
  switch row {
  | Accounting.Types.Unit({id}) | Bulk({id}, _) => id
  }

let tableColumns = (~statuses, ~hidden, ~taxesFree, ~editable, ~onRequestDispatch) => [
  {
    Table.key: "line",
    name: t("Line"),
    layout: {minWidth: 65.->#px, width: 65.->#px},
    render: ({index}) => {
      let leadingZero = 3
      let value = index + 1
      let formattedLineNumber = {
        let string = value->Int.toString
        let length = leadingZero + 1 - string->String.length
        let leading = length->String.make('0')
        leading ++ string
      }
      <TextStyle size=#xsmall variation=#normal> {formattedLineNumber->React.string} </TextStyle>
    },
  },
  {
    key: "reference",
    name: t("Product"),
    layout: {minWidth: 250.->#px, width: 3.->#fr, margin: #large, sticky: true},
    render: ({data: product, errorMessage}) => {
      let (identifier, name, description) = switch product {
      | Accounting.Types.Unit({identifier, name, description})
      | Bulk({identifier, name, description}, _) => (identifier, name, description)
      }
      <LegacyProductReferenceTableCell
        id=?identifier name description ?errorMessage openNewTab=true
      />
    },
  },
  {
    key: "stock",
    name: t("Current stock"),
    layout: {
      minWidth: 80.->#px,
      width: 7.->#pct,
      hidden: hidden === StatusAtAfterReception,
      alignX: editable ? #flexStart : #center,
    },
    render: ({data: product}) => {
      // TODO - use raw values instead of formatted
      let formattedStock = switch product {
      | Unit({formattedStock})
      | Bulk({formattedStock}, _) =>
        formattedStock->Option.map(value => value->Js.String2.replace(".", ","))
      }
      <ProductStockTableCell value=formattedStock />
    },
  },
  {
    key: "quantity",
    name: t("Ord. qt"),
    layout: {
      minWidth: 140.->#px,
      width: 1.5->#fr,
      hidden: hidden === StatusAtAfterReception,
      alignX: editable ? #flexStart : #center,
    },
    render: ({data: product}) =>
      <CartProductQuantityTableCell product editable onRequestDispatch />,
  },
  {
    key: "reception_quantity",
    name: t("Rec. qt"),
    layout: {
      minWidth: 170.->#px,
      hidden: hidden === StatusBeforeReception,
      alignX: editable && !OrderEdit.isLimitedEdition(~statuses) ? #flexStart : #center,
    },
    render: ({data: product}) =>
      <CartProductQuantityTableCell
        product
        beforeOrderReception=false
        editable={editable && !OrderEdit.isLimitedEdition(~statuses)}
        onRequestDispatch
      />,
  },
  {
    key: "unitPrice",
    name: t("U. price excl. VAT"),
    layout: {minWidth: 170.->#px, width: 1.5->#fr, margin: #normal},
    render: ({data: product}) =>
      <CartProductUnitPriceTableCell product editable onRequestDispatch />,
  },
  {
    key: "discounts",
    name: t("U. discounts"),
    layout: {minWidth: 120.->#px},
    render: ({data: product}) =>
      <CartProductDiscountsTableCell product editable onRequestDispatch />,
  },
  {
    key: "fees",
    name: t("U. fees"),
    layout: {minWidth: 120.->#px},
    render: ({data: product}) => <CartProductFeesTableCell product editable onRequestDispatch />,
  },
  {
    key: "amount",
    name: t("Total excl. VAT"),
    layout: {minWidth: 120.->#px},
    render: ({data: product}) => {
      let formattedTotalAmountExcludingGlobalDiscounts = switch product {
      | Unit({totalAmountExcludingGlobalDiscounts})
      | Bulk({totalAmountExcludingGlobalDiscounts}, _) =>
        totalAmountExcludingGlobalDiscounts->Option.map(value =>
          Big.toFloat(value)->Intl.currencyFormat(
            ~currency=#EUR,
            ~minimumFractionDigits=3,
            ~maximumFractionDigits=3,
          )
        )
      }
      <ProductPriceTableCell value=formattedTotalAmountExcludingGlobalDiscounts />
    },
  },
  {
    key: "amountVAT",
    name: t("VAT total"),
    layout: {minWidth: 135.->#px, hidden: taxesFree},
    render: ({data: product}) =>
      switch product {
      | Unit({taxes: Some(taxes), totalTaxesExcludingGlobalDiscount, totalTaxes})
      | Bulk({taxes: Some(taxes), totalTaxesExcludingGlobalDiscount, totalTaxes}, _) =>
        let productTax = switch (taxesFree, taxes[0]) {
        | (false, Some(tax)) => tax
        | _ => raise(ProductTaxNotFound)
        }
        let taxAmount = Accounting.Formatter.Utils.bigToFloat(
          totalTaxesExcludingGlobalDiscount->Option.getWithDefault(Big.fromFloat(0.)),
        )
        // let taxAmount = Accounting.Formatter.Utils.bigToFloat(
        //   totalTaxes->Option.getWithDefault(Big.fromFloat(0.)),
        // )
        <CartProductTaxTableCell taxAmount taxRate=productTax.rate />
      | _ => React.null
      },
  },
  {
    key: "actions",
    layout: {
      minWidth: 70.->#px,
      width: 70.->#px,
      hidden: (!editable ||
      (statuses->OrderStatus.has(#ARCHIVED) ||
      hidden === StatusAtAfterReception)) && !(statuses->OrderStatus.has(#DRAFT)),
    },
    render: ({data: product}) => {
      let productId = switch product {
      | Unit({id}) | Bulk({id}, _) => id
      }
      <RoundButton icon=#delete_light onPress={_ => ProductRemoved(productId)->onRequestDispatch} />
    },
  },
]

let make = TableView.make
let makeProps = TableView.makeProps

open Intl
open Style

let supplierVariantsCountLimit = 50

module AddProductModalButton = {
  @react.component
  let make = (~onRequestModalOpen) => {
    let (ref, hovered) = Hover.use()

    <Touchable ref onPress={_ => onRequestModalOpen()}>
      <OverlayTriggerView preset=#inputField({required: false}) icon=#search hovered>
        <TextStyle variation=#normal> {(t("Add product") ++ "  ")->React.string} </TextStyle>
      </OverlayTriggerView>
    </Touchable>
  }
}

@react.component
let make = (
  ~cartProducts,
  ~shopId,
  ~supplierId,
  ~supplierVariantsCount,
  ~importedCsv,
  ~onRequestCartRowsError,
  ~onRequestFileImport,
  ~onRequestCartLoading,
  ~onRequestProductPickerModalOpen,
  ~onRequestGlobalFeeModalOpen,
  ~onRequestSupplierProducts,
  ~onRequestDispatch as cartDispatch,
) => {
  let authState = Auth.useState()
  let notifier = Notifier.use()
  let client = ApolloClient.React.useApolloClient()

  React.useEffect1(() => {
    switch importedCsv {
    | Some(file) =>
      file->OrderCsvImporting.make(
        ~client,
        ~notifier,
        ~cartProducts,
        ~cartDispatch,
        ~onRequestCartLoading,
      )
    | _ => ()
    }
    None
  }, [importedCsv])

  let disabledSupplierProductsButton =
    supplierId === "" || supplierVariantsCount > supplierVariantsCountLimit
  let textTooltipSupplierProductsButton =
    supplierId !== "" && supplierVariantsCount > supplierVariantsCountLimit
      ? Some(
          template(
            t(
              "{{supplierVariantsCount}} products are to be imported,\n" ++
              "the order may be too large (maximum {{supplierVariantsCountLimit}} products).\n" ++ //
              "Please use the product picker to select manually the products you need.",
            ),
            ~values={
              "supplierVariantsCount": supplierVariantsCount->Float.fromInt->Intl.decimalFormat,
              "supplierVariantsCountLimit": supplierVariantsCountLimit
              ->Float.fromInt
              ->Intl.decimalFormat,
            },
            (),
          ),
        )
      : None

  <Inline space=#xlarge grow=true>
    <View style={style(~width=100.->pct, ())}>
      <AddProductModalButton onRequestModalOpen={() => onRequestProductPickerModalOpen(true)} />
    </View>
    <Inline space=#medium>
      {switch authState {
      | Logged({user: {username, organizationName}, shops})
        if Auth.hasCavavinPermission(~username, ~organizationName, ~shops) =>
        <TextIconButton
          icon=#export_light
          disabled={cartProducts->Array.size === 0}
          onPress={_ =>
            OrderCavavin.checkProductDataKvisteAPI(
              ~cartProducts,
              ~shopId,
              ~notifier,
              ~onRequestCartRowsError,
              ~onRequestCartLoading,
            )}>
          {t("Send to kviste")->React.string}
        </TextIconButton>
      | _ => React.null
      }}
      <TextIconButton
        icon=#plus_light
        disabled=disabledSupplierProductsButton
        textTooltip=?textTooltipSupplierProductsButton
        onPress={_ => onRequestSupplierProducts()}>
        {t("Add supplier's products")->React.string}
      </TextIconButton>
      <TextIconButton
        icon=#plus_light
        disabled={cartProducts->Array.length <= 0}
        onPress={_ => onRequestGlobalFeeModalOpen(true)}>
        {t("order_cart.actions_bar.allocation_of_costs.text_button")->React.string}
      </TextIconButton>
      // NOTE - key ensures the component remount after a file has been picked
      // NOTE - #excel to prevent CSV issues on Windows when it is installed
      <FilePicker
        key={importedCsv->Obj.magic}
        types=[#csv, #excel]
        disabled={shopId === ""}
        onChange={file => onRequestFileImport(file)}
        onError={msg => notifier.reset(Error(msg), ())}>
        <TextIcon icon=#import_light size=#large noMargin=true disabled={shopId === ""}>
          {t("Import csv")->React.string}
        </TextIcon>
      </FilePicker>
    </Inline>
  </Inline>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["cartProducts"] === newProps["cartProducts"] &&
  oldProps["importedCsv"] === newProps["importedCsv"] &&
  oldProps["shopId"] === newProps["shopId"] &&
  oldProps["supplierId"] === newProps["supplierId"] &&
  oldProps["supplierVariantsCount"] === newProps["supplierVariantsCount"]
)

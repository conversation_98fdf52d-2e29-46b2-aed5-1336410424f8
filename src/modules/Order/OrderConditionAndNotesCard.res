open Intl

@react.component
let make = (~editable) => {
  let state = OrderEditForm.useFormState()

  <Card title="Notes">
    {switch editable {
    | true =>
      <>
        <OrderEditForm.InputTextArea field=Condition label={t("Conditions")} />
        <Box spaceY=#normal />
        <OrderEditForm.InputTextArea field=NoteForSupplier label={t("Supplier note")} />
      </>
    | _ =>
      <>
        <Label text={t("Conditions")} />
        <Box spaceTop=#xsmall>
          {switch state.values.condition {
          | condition if condition->Js.String.length > 0 =>
            <TextStyle size=#large> {condition->React.string} </TextStyle>
          | _ => <TextStyle variation=#subdued> {t("Empty condition")->React.string} </TextStyle>
          }}
        </Box>
        <Box spaceY=#normal />
        <Label text={t("Supplier note")} />
        <Box spaceTop=#xsmall>
          {switch state.values.noteForSupplier {
          | noteForSupplier if noteForSupplier->Js.String.length > 0 =>
            <TextStyle size=#large> {noteForSupplier->React.string} </TextStyle>
          | _ => <TextStyle variation=#subdued> {t("Empty note")->React.string} </TextStyle>
          }}
        </Box>
      </>
    }}
  </Card>
}

let make = React.memo(make)

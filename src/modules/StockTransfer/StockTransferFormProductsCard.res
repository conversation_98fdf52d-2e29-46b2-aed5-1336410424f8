open Intl
open StockTransferFormProducts

module AddProductModalButton = {
  @react.component
  let make = (~triggerPickerDisabled, ~onRequestModalPickerOpen) => {
    let (ref, triggerHovered) = Hover.use()

    <Touchable ref disabled=triggerPickerDisabled onPress={_ => onRequestModalPickerOpen()}>
      <OverlayTriggerView
        preset=#inputField({required: false})
        icon=#search
        disabled=triggerPickerDisabled
        hovered=triggerHovered>
        <TextStyle variation=#normal> {t("Add product")->React.string} </TextStyle>
      </OverlayTriggerView>
    </Touchable>
  }
}

module TablePlaceholder = {
  let style = Style.style(
    ~backgroundColor=Colors.neutralColor05,
    ~paddingVertical=23.->Style.dp,
    ~alignItems=#center,
    ~borderStyle=#solid,
    ~borderBottomWidth=1.,
    ~borderBottomColor=Colors.neutralColor15,
    (),
  )

  @react.component
  let make = React.memo((~message, ~errorMessage=?) =>
    <View style>
      <TextStyle size=#small variation=#normal> {message->React.string} </TextStyle>
      {switch errorMessage {
      | Some(error) =>
        <Box spaceTop=#xsmall>
          <TextStyle size=#xsmall variation=#negative> {error->React.string} </TextStyle>
        </Box>
      | None => React.null
      }}
    </View>
  )
}

module Row = {
  type t = StockTransferFormProducts.product
  let keyExtractor = row => row.senderVariantId
}

let tableColumns = (~dispatch) => [
  {
    Table.key: "reference",
    name: t("Product"),
    render: ({data: product, errorMessage}) =>
      <LegacyProductReferenceTableCell
        id=product.senderVariantId
        name=product.name
        description=product.description
        ?errorMessage
        openNewTab=true
      />,
  },
  {
    key: "senderStock",
    name: t("Sender stock"),
    render: ({data: product}) =>
      <Box spaceLeft=#normal>
        <ProductStockTableCell
          value=Some(product.senderStockQuantity->Js.Int.toString)
          state=?product.senderStockState
          size=#xsmall
        />
      </Box>,
  },
  {
    key: "recipientStock",
    name: t("Recipient stock"),
    render: ({data: product}) =>
      <Box spaceLeft=#normal>
        {switch product.recipientStockQuantity {
        | Some(quantity) =>
          <ProductStockTableCell
            value=Some(quantity->Int.toString) state=?product.recipientStockState size=#xsmall
          />
        | _ => <TextStyle variation=#normal> {t("—")->React.string} </TextStyle>
        }}
      </Box>,
  },
  {
    key: "transferredQuantity",
    name: t("Transferred quantity"),
    render: ({data: product}) =>
      // TODO - Handles bulk mode
      <InputNumberField
        minValue=1.
        precision=0
        value={product.transferredQuantity->Float.fromInt}
        onChange={quantity =>
          dispatch(ProductTransferredQuantityUpdateRequested(product.cku, quantity->Int.fromFloat))}
      />,
  },
  {
    key: "actions",
    name: t(""),
    render: ({data: product}) =>
      <Columns align=#end>
        <Column width=#fluid>
          <RoundButton
            icon=#delete_light onPress={_ => dispatch(ProductRemoveRequested(product.cku))}
          />
        </Column>
      </Columns>,
  },
]

@react.component
let make = (~productsRowErrors) => {
  let ({Nav.Context.opened: navOpened}, _) = Nav.Context.use()

  let (productsState, productsDispatch) = StockTransferFormProducts.use()
  let (pickerOpened, setPickerOpened) = React.useState(() => false)

  let formState = StockTransferForm.useFormState()
  let notifier = Notifier.use()

  let (commonProducts, nonCommonProducts) = React.useMemo1(
    () =>
      productsState.products->Array.partition(product =>
        product.recipientStockQuantity->Option.isSome
      ),
    [productsState.products],
  )

  React.useEffect1(() => {
    let nonCommonProductsLength = nonCommonProducts->Array.length

    if !productsState.processing && nonCommonProductsLength > 0 {
      notifier.reset(
        Warning(
          template(
            t(
              isPlural(nonCommonProductsLength)
                ? "{{count}} products could not be added to the transfer because they are not common to the two selected shops."
                : "{{count}} product could not be added to the transfer because it is not common to the two selected shops.",
            ),
            ~values={"count": nonCommonProductsLength},
            (),
          ),
        ),
        ~details=nonCommonProducts->Array.map(product =>
          product.name ++ product.stockKeepingUnit->Option.mapWithDefault("", sku => ", " ++ sku)
        ),
        (),
      )

      nonCommonProducts->Array.forEach(product =>
        ProductRemoveRequested(product.cku)->productsDispatch
      )
    }

    if !productsState.processing && nonCommonProductsLength === 0 {
      notifier.clear()
    }

    None
  }, [productsState.processing])

  React.useEffect2(() => {
    if formState.values.products->Array.length > 0 {
      ResetRequested->productsDispatch
      notifier.clear()
    }
    None
  }, (formState.values.recipientShopId, formState.values.senderShopId))

  let renderActionsStart = () =>
    <AddProductModalButton
      triggerPickerDisabled=productsState.processing
      onRequestModalPickerOpen={() => setPickerOpened(_ => true)}
    />
  let renderActionsEnd = () => <StockTransferFormActions />

  let columns = tableColumns(~dispatch=productsDispatch)

  let productsError = switch formState.validation {
  | Ok() => None
  | Error(errors) =>
    errors
    ->Array.keepMap(((field, error)) =>
      switch (field, error) {
      | (StockTransferForm.Schema.Field(StockTransferForm.Lenses.Products), error) => Some(error)
      | _ => None
      }
    )
    ->Array.get(0)
  }

  <>
    <Card variation=#table>
      <Box spaceX=#large spaceBottom=#xlarge>
        <AddProductModalButton
          triggerPickerDisabled=productsState.processing
          onRequestModalPickerOpen={() => setPickerOpened(_ => true)}
        />
      </Box>
      <TableView
        columns
        data=AsyncData.Done(Ok(commonProducts))
        erroredRowsMap=productsRowErrors
        keyExtractor=Row.keyExtractor
        placeholderEmptyState={<TablePlaceholder
          message={t("Add products to complete your transfer")}
          errorMessage=?{switch (productsError, formState.submission) {
          | (Some(message), Failed(_)) => Some(t(message))
          | _ => None
          }}
        />}
      />
    </Card>
    <StockTransferFormProductPickerModalTable
      opened=pickerOpened
      senderShopId=formState.values.senderShopId
      recipientShopId=formState.values.recipientShopId
      disabledIds={productsState.products->Array.map(product => product.senderVariantId)}
      onCommit={products => ProductBulkAddRequested(products)->productsDispatch}
      onRequestClose={() => setPickerOpened(_ => false)}
    />
    <SpinnerModal title={t("Loading products")} opened=productsState.processing />
    <PageBottomActionsBar
      displayThreshold=180. renderStart=renderActionsStart renderEnd=renderActionsEnd navOpened
    />
  </>
}

let make = React.memo(make)

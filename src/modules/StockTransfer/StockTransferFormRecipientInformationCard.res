open Intl

@react.component
let make = () => {
  let shops = Auth.useShops()
  let formState = StockTransferForm.useFormState()

  let items = shops->Array.map(shop => {
    Select.key: shop.id,
    label: shop.name,
    value: shop.id,
    disabled: shop.id === formState.values.senderShopId,
  })
  let sections = [{Select.title: t("Shops"), items}]

  <Card title={t("Recipient information")}>
    <Stack space=#large>
      <StockTransferForm.InputSelect
        field=RecipientShopId
        label={t("Recipient")}
        placeholder={t("Select the destination shop")}
        tooltip={<Tooltip.Span
          text={t("Changing the recipient will reset the transfer basket.")}
        />}
        sections
      />
    </Stack>
  </Card>
}

let make = React.memo(make)

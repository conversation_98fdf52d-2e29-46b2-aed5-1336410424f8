open Intl

exception UnknownStringValue

type t = [
  | #LOSS
  | #SALE
  | #REFUND
  | #DELIVERY
  | #DELIVERY_RECEIPT
  | #RESET
  | #RECEPTION
  | #CREDIT_MEMO
  | #INCOMING_TRANSFER
  | #OUTGOING_TRANSFER
]

let values: array<t> = [
  #LOSS,
  #SALE,
  #REFUND,
  #DELIVERY,
  #DELIVERY_RECEIPT,
  #RESET,
  #RECEPTION,
  #CREDIT_MEMO,
  #INCOMING_TRANSFER,
  #OUTGOING_TRANSFER,
]

let toString: t => string = kind =>
  switch kind {
  | #LOSS => "LOSS"
  | #SALE => "SALE"
  | #REFUND => "REFUND"
  | #DELIVERY => "DELIVERY"
  | #DELIVERY_RECEIPT => "DELIVERY_RECEIPT"
  | #RESET => "RESET"
  | #RECEPTION => "RECEPTION"
  | #CREDIT_MEMO => "CREDIT_MEMO"
  | #INCOMING_TRANSFER => "INCOMING_TRANSFER"
  | #OUTGOING_TRANSFER => "OUTGOING_TRANSFER"
  }

let fromStringExn: string => t = value =>
  switch value {
  | "LOSS" => #LOSS
  | "SALE" => #SALE
  | "REFUND" => #REFUND
  | "DELIVERY" => #DELIVERY
  | "DELIVERY_RECEIPT" => #DELIVERY_RECEIPT
  | "RESET" => #RESET
  | "RECEPTION" => #RECEPTION
  | "CREDIT_MEMO" => #CREDIT_MEMO
  | "INCOMING_TRANSFER" => #INCOMING_TRANSFER
  | "OUTGOING_TRANSFER" => #OUTGOING_TRANSFER
  | _ => raise(UnknownStringValue)
  }

let toLabel: t => string = kind =>
  switch kind {
  | #LOSS => t("Loss")
  | #SALE => t("Sale")
  | #REFUND => t("Refund")
  | #DELIVERY => t("Delivery")
  | #DELIVERY_RECEIPT => t("Delivery receipt")
  | #RESET => t("Reseting")
  | #RECEPTION => t("Reception")
  | #CREDIT_MEMO => t("Credit note")
  | #INCOMING_TRANSFER => t("Incoming transfer")
  | #OUTGOING_TRANSFER => t("Outgoing transfer")
  }

let toColor: t => Style.Color.t = kind =>
  switch kind {
  | #REFUND
  | #DELIVERY
  | #INCOMING_TRANSFER
  | #CREDIT_MEMO
  | #RECEPTION => Colors.successColor50
  | #LOSS
  | #SALE
  | #DELIVERY_RECEIPT
  | #OUTGOING_TRANSFER => Colors.dangerColor50
  | #RESET => Colors.neutralColor90
  }

open Style

@react.component
let make = (~value, ~capacityPrecision=?, ~capacityUnit=?, ~kind) => {
  <Text
    style={[
      FontFaces.libreFranklinSemiBoldStyle,
      #normal->FontSizes.styleFromSize,
      style(~color=kind->StockActivityKind.toColor, ()),
    ]->arrayStyle}>
    {value->StockActivityQuantity.format(~capacityPrecision?, ~capacityUnit?, ~kind)->React.string}
  </Text>
}

let make = React.memo(make)

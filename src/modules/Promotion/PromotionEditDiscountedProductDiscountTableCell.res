open Intl
open PromotionEditDiscountedProduct

@react.component
let make = (
  ~editable: bool,
  ~disabled: bool,
  ~discount: Discount.t,
  ~bulkUnit: option<string>,
  ~onRequestUpdate: Discount.t => unit,
  ~onRequestReplicate: option<unit => unit>=?,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())
  let discountKindRef = React.useRef(discount.kind)

  // Sets kind as ref to be used inside InputNumber onChange callback
  // NOTE - passing the kind in the useCallback observer makes switching kind bugging
  React.useEffect1(() => {
    discountKindRef.current = discount.kind
    None
  }, [discount.kind])

  let onDiscountKindChange = React.useCallback1(kind =>
    onRequestUpdate({
      id: discount.id,
      kind,
      amount: switch (kind, discount.amount > 100.) {
      | (#PERCENT, true) => 0.
      | _ => discount.amount
      },
    })
  , [discount.amount])

  let onDiscountAmountChange = React.useCallback0(amount =>
    switch (discountKindRef.current, amount <= 100.) {
    | (#PERCENT, true)
    | (#CURRENCY, _) =>
      onRequestUpdate({id: discount.id, kind: discountKindRef.current, amount})
    | _ => ()
    }
  )

  let kindToText = kind =>
    switch kind {
    | #PERCENT => "%"
    | #CURRENCY => "€"
    }

  // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
  let popover = {
    ...popover,
    onRequestClose: () =>
      if !triggerHovered {
        popover.onRequestClose()
      },
  }

  <>
    {if editable {
      <Box spaceTop=#xxsmall>
        <OpeningButton
          ref=popoverTriggerRef
          ariaProps=popoverAriaProps.triggerProps
          opened=popover.opened
          disabled
          onPress={_ => popover.onRequestToggle()}>
          <TextStyle variation={disabled ? #normal : #neutral}>
            {switch discount.kind {
            | #PERCENT => (discount.amount /. 100.)->percentFormat
            | #CURRENCY => discount.amount->currencyFormat(~currency=#EUR)
            }->React.string}
          </TextStyle>
        </OpeningButton>
      </Box>
    } else {
      <TextStyle>
        {switch discount.kind {
        | #PERCENT => (discount.amount /. 100.)->percentFormat
        | #CURRENCY => discount.amount->currencyFormat(~currency=#EUR)
        }->React.string}
      </TextStyle>
    }}
    {if popover.opened {
      <Popover
        triggerRef=popoverTriggerRef
        state=popover
        shouldUpdatePosition=false
        modal=false
        placement=#"bottom end">
        <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
          <Box spaceX=#large spaceY=#xlarge>
            <Inline space=#large>
              <InputSegmentedControlsField
                label={t("Discount type")}
                compact=true
                required=false
                options=list{#PERCENT, #CURRENCY}
                optionToText=kindToText
                value=discount.kind
                onChange=onDiscountKindChange
              />
              <InputNumberField
                label={t("Discount amount")}
                appender={switch (discount.kind, bulkUnit) {
                | (#PERCENT, _) => Percent
                | (#CURRENCY, None) => Currency(#EUR)
                | (#CURRENCY, Some(bulkUnit)) => Custom(`€ / ${bulkUnit}`)
                }}
                minValue=0.
                maxValue=?{discount.kind === #PERCENT ? Some(100.) : None}
                minPrecision=?{discount.kind === #PERCENT ? Some(0) : None}
                value={discount.amount}
                onChange={onDiscountAmountChange}
              />
            </Inline>
            <Box spaceTop=#medium spaceBottom=#none>
              <TextIconButton
                icon=#plus_light
                disabled={onRequestReplicate->Option.isNone}
                onPress={_ => {
                  switch onRequestReplicate {
                  | Some(onRequestReplicate) =>
                    onRequestReplicate()
                    popover.onRequestClose()
                  | _ => ()
                  }
                }}>
                {t("Apply to all products")->React.string}
              </TextIconButton>
            </Box>
          </Box>
        </Popover.Dialog>
      </Popover>
    } else {
      React.null
    }}
  </>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["discount"] == newProps["discount"] &&
  oldProps["editable"] === newProps["editable"] &&
  oldProps["disabled"] === newProps["disabled"] &&
  oldProps["bulkUnit"] === newProps["bulkUnit"] &&
  oldProps["onRequestReplicate"] === newProps["onRequestReplicate"]
)

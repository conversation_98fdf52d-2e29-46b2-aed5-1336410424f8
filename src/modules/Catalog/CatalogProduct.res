open Intl

module Status = {
  type t = Active | Inactive | Archived | Unarchived

  let toLabel = status =>
    switch status {
    | Active => t("On sale")
    | Inactive => t("Taken out")
    | Archived => t("Archived")
    | Unarchived => t("Not archived")
    }

  let toString = status =>
    switch status {
    | Active => "ACTIVE"
    | Inactive => "INACTIVE"
    | Archived => "ARCHIVED"
    | Unarchived => "UNARCHIVED"
    }
  let fromString = str =>
    switch str {
    | "ACTIVE" => Ok(Active)
    | "INACTIVE" => Ok(Inactive)
    | "ARCHIVED" => Ok(Archived)
    | "UNARCHIVED" => Ok(Unarchived)
    | _ => Error("Invalid product status")
    }
}

module Kind = {
  type t = [#WINE | #SPIRITUOUS | #BEER | #SIMPLE]

  let toLabel = (~titleCase=false, ~translate=true, kind) => {
    let label = switch kind {
    | #WINE => "wine"
    | #SPIRITUOUS => "spirituous"
    | #BEER => "beer"
    | #SIMPLE => "simple product"
    }
    let label = switch translate {
    | true => t(label)
    | _ => label
    }
    let label = switch titleCase {
    | true =>
      label->Js.String2.charAt(0)->Js.String2.toUpperCase ++ label->Js.String2.substr(~from=1)
    | _ => label
    }
    label
  }

  let toString: t => string = kind => (kind :> string)
  let fromString: string => Result.t<t, string> = str =>
    switch str {
    | "WINE" => Ok(#WINE)
    | "SPIRITUOUS" => Ok(#SPIRITUOUS)
    | "BEER" => Ok(#BEER)
    | "SIMPLE" => Ok(#SIMPLE)
    | _ => Error("Invalid kind")
    }
}

module Color = {
  open Colors.Product

  type t = [
    | #WHITE
    | #BLOND
    | #AMBER
    | #DARK
    | #BLACK
    | #RED
    | #ROSE
    | #ORANGE
  ]

  type variation = [#badge | #pastille]

  type colorSet = {
    foregroundColor: Style.Color.t,
    backgroundColor: Style.Color.t,
  }

  let toLabel = x =>
    switch x {
    | #WHITE => t("White")
    | #BLOND => t("Blond")
    | #AMBER => t("Amber")
    | #DARK => t("Dark")
    | #BLACK => t("Black")
    | #RED => t("Red")
    | #ROSE => t("Rose")
    | #ORANGE => t("Orange")
    }

  let toColorSet = (value, ~variation) =>
    switch (value, variation) {
    | (#WHITE, #pastille) => {
        foregroundColor: whitePastilleColor,
        backgroundColor: whitePastilleColor,
      }
    | (#WHITE, _) => {foregroundColor: whiteTextColor, backgroundColor: whiteBadgeColor}
    | (#BLOND | #ORANGE, _) => {foregroundColor: blondTextColor, backgroundColor: blondBadgeColor}
    | (#AMBER, _) => {foregroundColor: amberTextColor, backgroundColor: amberBadgeColor}
    | (#DARK, _) => {foregroundColor: darkTextColor, backgroundColor: darkBadgeColor}
    | (#BLACK, _) => {foregroundColor: blackTextColor, backgroundColor: blackBadgeColor}
    | (#RED, _) => {foregroundColor: redTextColor, backgroundColor: redBadgeColor}
    | (#ROSE, _) => {foregroundColor: roseTextColor, backgroundColor: roseBadgeColor}
    }

  let toString: t => string = color => (color :> string)
  let fromString: string => Result.t<t, string> = str =>
    switch str {
    | "WHITE" => Ok(#WHITE)
    | "BLOND" => Ok(#BLOND)
    | "AMBER" => Ok(#AMBER)
    | "DARK" => Ok(#DARK)
    | "BLACK" => Ok(#BLACK)
    | "RED" => Ok(#RED)
    | "ROSE" => Ok(#ROSE)
    | "ORANGE" => Ok(#ORANGE)
    | _ => Error("Invalid color")
    }
}

module WineType = {
  type t = [#STILL | #EFFERVESCENT]

  let toLabel = x =>
    switch x {
    | #STILL => t("Still")
    | #EFFERVESCENT => t("Effervescent")
    }

  let toString: t => string = wineType => (wineType :> string)
  let fromString: string => Result.t<t, string> = str =>
    switch str {
    | "STILL" => Ok(#STILL)
    | "EFFERVESCENT" => Ok(#EFFERVESCENT)
    | _ => Error("Invalid wine type")
    }
}

module WhiteWineType = {
  type t = [#DRY | #SEMIDRY | #SOFT | #SWEET]

  let toLabel = x =>
    switch x {
    | #DRY => t("Dry")
    | #SEMIDRY => t("Semi dry")
    | #SOFT => t("Soft")
    | #SWEET => t("Sweet")
    }

  let toString: t => string = whiteWineType => (whiteWineType :> string)
  let fromString: string => Result.t<t, string> = str =>
    switch str {
    | "DRY" => Ok(#DRY)
    | "SEMIDRY" => Ok(#SEMIDRY)
    | "SOFT" => Ok(#SOFT)
    | "SWEET" => Ok(#SWEET)
    | _ => Error("Invalid white wine type")
    }
}

module Information = {
  type t = {
    productName: string,
    variantName: string,
    sku?: string,
    plu?: string,
    internalCode?: string,
    color?: Color.t,
    producerName?: string,
    designation?: string,
    productFamily?: string,
    wineType?: WineType.t,
    whiteWineType?: WhiteWineType.t,
    beerType?: string,
    region?: string,
    country: string,
    categoryName: string,
    supplierName?: string,
    alcoholVolume?: string,
  }

  let formatDescription = (
    ~productKind,
    ~information,
    ~hideColor=false,
    ~hideProducer=false,
    ~hideSupplier=false,
    (),
  ) => {
    let formattedSkuCode = switch (information.sku, information.internalCode) {
    | (Some(sku), Some(code)) =>
      sku === code
        ? Some(`SKU/CODE${t(":")} ${sku}`)
        : Some(`SKU${t(":")} ${sku}, CODE${t(":")} ${code}`)
    | (Some(sku), None) => Some(`SKU${t(":")} ${sku}`)
    | (None, Some(code)) => Some(`CODE${t(":")} ${code}`)
    | (None, None) => None
    }
    let {
      ?color,
      ?producerName,
      ?supplierName,
      ?beerType,
      ?productFamily,
      ?designation,
      ?region,
      country,
    } = information
    let color = color->Option.flatMap(color => !hideColor ? Some(Color.toLabel(color)) : None)
    let producerName =
      producerName->Option.flatMap(producerName => !hideProducer ? Some(producerName) : None)
    let supplierName =
      supplierName->Option.flatMap(supplierName => !hideSupplier ? Some(supplierName) : None)
    let supplierName = switch (supplierName, producerName) {
    | (Some(supplierName), Some(producerName)) =>
      Js.String.toLowerCase(supplierName) !== Js.String.toLowerCase(producerName)
        ? Some(supplierName)
        : None
    | (supplierName, _) => supplierName
    }
    let designation = switch (designation, region) {
    | (Some(designation), Some(region)) =>
      Js.String.toLowerCase(designation) !== Js.String.toLowerCase(region)
        ? Some(designation)
        : None
    | (designation, _) => designation
    }
    let descriptionValues = switch productKind {
    | #WINE => [
        color,
        formattedSkuCode,
        producerName,
        supplierName,
        designation,
        region,
        Some(country),
      ]
    | #BEER => [color, formattedSkuCode, producerName, supplierName, beerType, Some(country)]
    | #SPIRITUOUS => [formattedSkuCode, producerName, supplierName, productFamily, Some(country)]
    | #SIMPLE => [formattedSkuCode, producerName, supplierName, Some(country)]
    }
    descriptionValues->Array.keepMap(x => x)->Js.Array2.joinWith(", ")
  }
}

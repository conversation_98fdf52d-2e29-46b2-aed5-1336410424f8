open Intl
open Style

@react.component
let make = (~supplierId, ~supplierName, ~variantId, ~shopId) => {
  let (focused, setFocused) = React.useState(() => false)
  let (ref, hovered) = Hover.use()

  // Closes the edition when value has mutated
  ReactUpdateEffect.use1(() => {
    setFocused(_ => false)
    None
  }, [supplierId])

  <Inline>
    <Box ref spaceRight=#xxlarge>
      <View style={style(~marginLeft=-13.->dp, ())}>
        {switch focused {
        | true =>
          <CatalogSupplierTableEditCell
            supplierId supplierName variantId shopId onDismiss={_ => setFocused(_ => false)}
          />
        | _ =>
          <ButtonPhased ref hovered onPress={_ => setFocused(_ => true)}>
            {switch supplierName {
            | "" => <TextStyle variation=#subdued> {t("To be filled")->React.string} </TextStyle>
            | _ => <TextStyle> {supplierName->React.string} </TextStyle>
            }}
          </ButtonPhased>
        }}
      </View>
    </Box>
  </Inline>
}

let make = React.memo(make)

open Intl
open CatalogVariantEditFormInputs

type fieldInteraction = {
  focused: bool,
  modified: bool,
}

@react.component
let make = (~editionMode, ~productKind) => {
  let dispatch = CatalogVariantEditForm.useFormDispatch()
  let {name, capacityValue, capacityUnit, year, bulk} = CatalogVariantEditForm.useFormState().values
  let (nameFieldInteracted, setNameFieldInteracted) = React.useState(() => {
    focused: false,
    modified: false,
  })

  ReactUpdateEffect.use1(() => {
    if nameFieldInteracted.focused && name !== "" {
      setNameFieldInteracted(prev => {...prev, modified: true})
    }
    None
  }, [name])

  ReactUpdateEffect.use1(() => {
    if bulk {
      FieldValueChanged(CapacityValue, _ => None)->dispatch
    }
    None
  }, [bulk])

  ReactUpdateEffect.use4(() => {
    if !nameFieldInteracted.modified {
      FieldValueChanged(
        Name,
        _ =>
          CatalogVariant.Name.make(
            ~capacityValue,
            ~capacityUnit,
            ~bulk,
            ~year=switch year {
            | Some(0.) | None => None
            | Some(year) => Some(year->Int.fromFloat)
            },
          ),
      )->dispatch
    }
    None
  }, (capacityValue, capacityUnit, year, bulk))

  <FieldsetLayoutPanel
    title={t("General information")}
    description={t(
      "By entering a capacity value and a year, the variant name will be automatically generated.",
    )}>
    {if !editionMode {
      <Inline>
        <CatalogVariantEditForm.InputToggleSwitch
          field=Bulk label={t("Bulk product management") ++ "  "}
        />
      </Inline>
    } else {
      React.null
    }}
    <Group>
      {if !bulk && capacityUnit->Option.isSome {
        <InputVariantCapacityValueSelect productKind />
      } else {
        React.null
      }}
      <InputVariantCapacityUnitSelect productKind disabled={bulk && editionMode} />
      {switch productKind {
      | #WINE | #SPIRITUOUS => <InputVariantYear productKind />
      | #SIMPLE | #BEER => React.null
      }}
    </Group>
    <CatalogVariantEditForm.InputText
      field=Name
      label={t("Variant's name")}
      placeholder={bulk
        ? t("Bulk by kg")
        : productKind->CatalogVariant.Name.placeholderFromProductKind}
      onFocus={_ => setNameFieldInteracted(prev => {...prev, focused: true})}
    />
    <Group grid=["50%", "50%"]>
      {switch productKind {
      | #WINE | #SPIRITUOUS | #BEER => <InputVariantAlcoholVolume productKind />
      | #SIMPLE => React.null
      }}
      <InputVariantPriceLookup />
      <CatalogVariantEditForm.InputText
        field=StockKeepingUnit label={t("SKU code")} placeholder="B1VAPSOBUOD202153C6"
      />
      <CatalogVariantEditForm.InputText
        field=Ean13 label={t("Barcode")} placeholder="3670257725480"
      />
      <CatalogVariantEditForm.InputText
        field=InternalCode label={t("Internal code")} placeholder="CAB04-750ML-2019"
      />
    </Group>
  </FieldsetLayoutPanel>
}

let make = React.memo(make)

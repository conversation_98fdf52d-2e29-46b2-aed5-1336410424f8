open Intl
open CatalogDuplication

module SelectionTable = CatalogDuplicationSelectionTable

exception CatalogDuplication_ProductIdNotFound

module ProductsQuery = %graphql(`
  query products($filterBy: InputProductsQueryFilter) {
    products(first: 50, filterBy: $filterBy) {
      edges {
        node {
          id
          shop { id, name }
          category {
            id
            formattedName
            name
            parent { name }
          }
          variants(filterBy: { archived: INCLUDED }) {
            edges {
              node { cku }
            }
          }
        }
      }
    }
  }
`)

module CategoriesQuery = %graphql(`
  query CategoriesQuery($filterByShopIds: InFilter, $filterByName: StringEqualsFilter, $filterByParentName: StringEqualsFilter) {
    categories(first: 1,
      filterBy: {
        archived: EXCLUDED,
        hasChildren: false,
        shopIds: $filterByShopIds,
        name: $filterByName,
        parentName: $filterByParentName,
      }
    ) {
      edges {
        node {
          id
          formattedName
          shop { id }
        }
      }
    }
  }
`)

module Fetcher = {
  type status =
    | Idle
    | Loading
    | Error
    | Done

  let useCategoriesUpdater = (~onSuccess) => {
    let client = ApolloClient.React.useApolloClient()
    let (status, setStatus) = React.useState(() => Idle)

    let executeAll = React.useCallback0(requestsVariables => {
      setStatus(_ => Loading)
      requestsVariables
      ->Array.map(variables =>
        client.query(~query=module(CategoriesQuery), ~fetchPolicy=NetworkOnly, variables)
        ->FuturePromise.fromPromise
        ->Future.map(
          response =>
            switch response {
            | Ok(Ok({data: {categories}, error: None})) => Ok(categories)
            | _ => Error(Request.serverErrorMessage)
            },
        )
      )
      ->Future.all
      ->Future.get(results => {
        results->Array.forEach(
          result =>
            switch result {
            | Ok(result) => onSuccess(result)
            | _ => setStatus(_ => Error)
            },
        )
        setStatus(
          current =>
            switch current {
            | Error => Error
            | _ => Done
            },
        )
      })
    })

    (executeAll, status)
  }
}

type reference = {
  variantsCku: array<string>,
  productName: string,
  productKind: CatalogProduct.Kind.t,
  productColor: option<CatalogProduct.Color.t>,
  productProducer: option<string>,
}

type data = {
  productId: string,
  productShopId: string,
  productCategoryFormattedName: string,
  productCategoryName: option<string>,
  productCategoryParentName: option<string>,
}

@react.component
let make = (
  ~reference: option<reference>,
  ~duplicationMode: Mode.t,
  ~onError: Status.t => unit,
) => {
  let (executeQuery, queryResults) = ProductsQuery.useLazy(~fetchPolicy=CacheAndNetwork, ())
  let (executeFilteringQuery, queryFilteringResults) = ProductsQuery.useLazy(
    ~fetchPolicy=NoCache,
    (),
  )
  let (rowsCategoryData, setRowsCategoryData) = React.useState(() => [])
  let state = CatalogDuplicationForm.useFormState()
  let dispatch = CatalogDuplicationForm.useFormDispatch()
  let shops = Auth.useShops()
  let sourceShopId = switch state.values.source {
  | Some(
      Product({shopId: Some(shopId)})
      | Variant({shopId: Some(shopId)}),
    ) =>
    Some(shopId)
  | _ => None
  }

  // Executes Products queries checking for product sibling:
  // - from the source shop's product information
  // - from the source shop's product variants cku
  React.useEffect1(() => {
    switch reference {
    | Some({variantsCku, productName, productKind, productColor, productProducer}) =>
      executeQuery(
        ProductsQuery.makeVariables(
          ~filterBy=ProductsQuery.makeInputObjectInputProductsQueryFilter(
            ~name=ProductsQuery.makeInputObjectStringEqualsFilter(~_equals=productName, ()),
            ~kind=ProductsQuery.makeInputObjectProductKindEqualsFilter(~_equals=productKind, ()),
            ~color=?switch productColor {
            | Some(color) =>
              Some(ProductsQuery.makeInputObjectProductColorEqualsFilter(~_equals=color, ()))
            | _ => None
            },
            ~producer=?switch productProducer {
            | Some(producer) =>
              Some(ProductsQuery.makeInputObjectStringEqualsFilter(~_equals=producer, ()))
            | _ => None
            },
            ~archived=#INCLUDED,
            (),
          ),
          (),
        ),
      )
      executeFilteringQuery(
        ProductsQuery.makeVariables(
          ~filterBy=ProductsQuery.makeInputObjectInputProductsQueryFilter(
            ~variantCkus=ProductsQuery.makeInputObjectInFilter(~_in=variantsCku, ()),
            ~archived=#INCLUDED,
            (),
          ),
          (),
        ),
      )
    | _ => ()
    }
    None
  }, [sourceShopId])

  // Makes shops list with 'duplicability' status from products queryResults :
  // - ExistsAlready case for Product duplication
  // - MissingProduct case for Variant duplication
  let shopList = React.useMemo2(() =>
    switch (queryResults, queryFilteringResults) {
    | (
        Executed({data: Some({products: {edges: productsFromInformation}})}),
        Executed({data: Some({products: {edges: productsFromCkus}})}),
      ) =>
      let products = Array.concat(productsFromInformation, productsFromCkus)

      shops->Array.map(shop => {
        open Shop
        {
          dataId: shop.id,
          dataName: shop.name,
          duplicability: switch (
            duplicationMode,
            products->Array.getBy(({node: product}) => product.shop.id === shop.id),
          ) {
          | (Product, Some(_)) => ExistsAlready
          | (Product, None) => Duplicable
          | (Variant, Some({node: {variants: {edges: variants}}})) =>
            switch variants->Array.getBy(({node: variant}) => Some(variant.cku) === state.id) {
            | Some(_) => ExistsAlready
            | _ => Duplicable
            }
          | (Variant, None) => MissingProduct
          },
        }
      })

    | _ => []
    }
  , (queryResults, queryFilteringResults))

  // Removes the source shop from shops selection list
  let availableShopList: array<Shop.t> = React.useMemo2(
    () =>
      sourceShopId->Option.mapWithDefault([], sourceShopId =>
        shopList->Array.keep(shop => shop.dataId !== sourceShopId)
      ),
    (shopList, sourceShopId),
  )

  // Requests error if there is no duplicable catalog
  ReactUpdateEffect.use1(() => {
    switch (
      availableShopList->Array.size > 0,
      availableShopList->Array.every(shop => shop.duplicability !== Duplicable),
      availableShopList->Array.some(shop => shop.duplicability === MissingProduct),
    ) {
    | (true, _, true) => onError(MissingProduct)
    | (true, true, _) => onError(ExistsAlready)
    | _ => ()
    }
    None
  }, [availableShopList])

  let onRequestCategoryUpdate = React.useCallback0((newCategory: SelectionTable.categoryData) =>
    setRowsCategoryData(categories =>
      switch categories->Array.getIndexBy(
        ({SelectionTable.shopId: shopId}) => shopId === newCategory.shopId,
      ) {
      | Some(index) =>
        categories->Array.mapWithIndex(
          (currentIndex, category) => currentIndex === index ? newCategory : category,
        )
      | _ => categories->Array.concat([newCategory])
      }
    )
  )

  // Hook to update corresponding row from the fetched category data
  let (
    executeCategoriesUpdate,
    categoriesUpdatingStatus,
  ) = Fetcher.useCategoriesUpdater(~onSuccess=categories =>
    categories.edges[0]
    ->Option.map(({node}) =>
      onRequestCategoryUpdate({
        id: Some(node.id),
        name: node.formattedName,
        shopId: node.shop.id,
      })
    )
    ->ignore
  )

  // Makes productsData to fulfill rows information
  let productsData = React.useMemo2(() =>
    switch (queryResults, queryFilteringResults) {
    | (
        Executed({data: Some({products: {edges: productsFromInformation}})}),
        Executed({data: Some({products: {edges: productsFromCkus}})}),
      ) =>
      Array.concat(productsFromInformation, productsFromCkus)->Array.map(({node: product}) => {
        productId: product.id,
        productShopId: product.shop.id,
        productCategoryFormattedName: product.category
        ->Option.map(({formattedName}) => formattedName)
        ->Option.getWithDefault(t("Not classified")),
        productCategoryName: product.category->Option.map(({name}) => name),
        productCategoryParentName: product.category
        ->Option.flatMap(({parent}) => parent)
        ->Option.map(({name}) => name),
      })
    | _ => []
    }
  , (queryResults, queryFilteringResults))

  // Fetches the category of each duplicable shop
  ReactUpdateEffect.use3(() => {
    if productsData->Array.size > 0 {
      let sourceProductData =
        productsData->Array.getBy(({productShopId}) => Some(productShopId) == sourceShopId)
      let requestsVariables = availableShopList->Array.keepMap(shop =>
        switch (duplicationMode, shop.duplicability, sourceProductData) {
        | (
            Product,
            Duplicable,
            Some({productCategoryName: Some(categoryName), productCategoryParentName}),
          ) =>
          Some(
            CategoriesQuery.makeVariables(
              ~filterByName=CategoriesQuery.makeInputObjectStringEqualsFilter(
                ~_equals=categoryName,
                (),
              ),
              ~filterByParentName=?switch productCategoryParentName {
              | Some(parentName) =>
                Some(CategoriesQuery.makeInputObjectStringEqualsFilter(~_equals=parentName, ()))
              | None => None
              },
              ~filterByShopIds=CategoriesQuery.makeInputObjectInFilter(~_in=[shop.dataId], ()),
              (),
            ),
          )
        | _ => None
        }
      )

      executeCategoriesUpdate(requestsVariables)
    }
    None
  }, (productsData, availableShopList, sourceShopId))

  let columns = React.useMemo2(() => {
    let everyShopsDuplicable =
      availableShopList->Array.length > 0 &&
        availableShopList->Array.every(shop => shop.duplicability === Duplicable)

    SelectionTable.tableColumns(
      ~everyShopsDuplicable,
      ~hideCategory=duplicationMode === Variant,
      ~onRequestCategoryUpdate,
    )
  }, (availableShopList, sourceShopId))

  let rows = React.useMemo2(() =>
    availableShopList->Array.keepMap(shop => {
      let note = switch shop.duplicability {
      | ExistsAlready => Some(t("Reference already existing"))
      | MissingProduct => Some(t("Reference not duplicable, missing product"))
      | _ => None
      }

      let return: option<SelectionTable.Row.t> = switch (
        duplicationMode,
        productsData->Array.getBy(({productShopId}) => productShopId === shop.dataId),
      ) {
      | (Product, data) =>
        Some({
          id: Some(Destination.Product({id: "", shopId: Some(shop.dataId)})),
          shopId: shop.dataId,
          shopName: shop.dataName,
          categoryName: data->Option.map(
            ({productCategoryFormattedName}) => productCategoryFormattedName,
          ),
          note,
        })
      | (Variant, data) =>
        Some({
          id: data->Option.map(
            ({productId}) => Destination.Product({
              id: productId,
              shopId: Some(shop.dataId),
            }),
          ),
          shopId: shop.dataId,
          shopName: shop.dataName,
          categoryName: None,
          note,
        })
      }

      return
    })
  , (availableShopList, sourceShopId))

  // Rows with with selected categories added in Product duplication
  let rows = React.useMemo2(() =>
    switch duplicationMode {
    | Product =>
      rows->Array.map(row =>
        switch rowsCategoryData->Array.getBy(
          ({SelectionTable.shopId: shopId}) => shopId === row.shopId,
        ) {
        | Some({id: Some(id), name, shopId}) => {
            ...row,
            id: Some(Destination.Category({id, shopId: Some(shopId)})),
            categoryName: Some(name),
          }
        | Some({id: None, name, shopId}) => {
            ...row,
            id: Some(Destination.Product({id: "", shopId: Some(shopId)})),
            categoryName: Some(name),
          }
        | None => row
        }
      )
    | Variant => rows
    }
  , (rows, rowsCategoryData))

  // Disables rows for which the reference isn't duplicable
  let disabledRowsKeys = React.useMemo1(() =>
    shopList->Array.keepMap(shop =>
      switch shop.duplicability {
      | ExistsAlready | MissingProduct => Some(shop.dataId)
      | Duplicable => None
      }
    )
  , [availableShopList])

  let placeholderEmptyState = switch (state.values, queryResults, categoriesUpdatingStatus) {
  | ({source: Some(_)}, _, _) if rows == [] => <Placeholder status=Loading />
  | (_, Executed({loading: true}), _)
  | (_, _, Loading | Error) =>
    <Placeholder status=Loading />
  | _ =>
    <Box
      spaceX=#large spaceY=#xlarge style={Style.style(~backgroundColor=Colors.neutralColor05, ())}>
      <Inline align=#center>
        <TextStyle variation=#normal size=#small align=#center>
          {t(
            "Please select the shop catalog to duplicate from in order to select the targeted catalogs.",
          )->React.string}
        </TextStyle>
      </Inline>
    </Box>
  }

  let onSelectChange = React.useCallback1(selectedRowsKeys =>
    FieldValueChanged(
      Destinations,
      _ =>
        switch selectedRowsKeys {
        | Table.All =>
          rows->Array.keepMap(row =>
            if !(disabledRowsKeys->Array.some(shopId => shopId === row.shopId)) {
              row.id
            } else {
              None
            }
          )
        | Selected(keys) =>
          keys->Array.keepMap(shopId =>
            rows->Array.getBy(row => row.shopId === shopId)->Option.flatMap(row => row.id)
          )
        },
    )->dispatch
  , [rows])

  <Card variation=#table>
    <SelectionTable
      columns
      data=AsyncData.Done(Ok(rows))
      keyExtractor=SelectionTable.Row.keyExtractor
      disabledRowsKeys
      hideCard=true
      selectionEnabled=true
      initialAllSelected=true
      placeholderEmptyState
      onSelectChange
    />
  </Card>
}

let make = React.memo(make)

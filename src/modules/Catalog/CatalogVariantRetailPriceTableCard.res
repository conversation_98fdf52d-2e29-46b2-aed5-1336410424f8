open Intl
open CatalogVariantRetailPrice

module RetailPriceReducer = CatalogVariantRetailPriceReducer
module VariantQuery = CatalogVariant__Queries.VariantQuery
module Mutation = %graphql(`
  mutation updateVariant_retailPrices($id: ID!, $input: InputUpdateVariant!, $variantPricesInput: [InputUpdateVariantPrices!]) {
    updateVariant(id: $id, input: $input, variantPricesInput: $variantPricesInput) {
      id
      purchasedPrice
      variantPrices {
        edges {
          node {
            id
            archivedAt
            price {
              id
              name
              enableByDefault
              taxIncluded
            }
            valueIncludingTax
            valueExcludingTax
            fromQuantity
            toQuantity
          }
        }
      }
    }
  }
`)

module Row = {
  type t = CatalogVariantRetailPrice.variantPrice
  let keyExtractor = row => row.priceId ++ "-retail"
}

module DecreasingPriceListTextLine = {
  @react.component
  let make = React.memo((~name, ~fromQuantity, ~taxIncluded) => {
    let formattedName =
      name ++
      fromQuantity->Option.mapWithDefault("", current =>
        current > 1. ? ` +${current->Float.toString}` : ""
      )
    let taxTypename = t(taxIncluded ? "VAT incl." : "VAT excl.")

    <Inline space=#small>
      <TextStyle weight=#semibold> {formattedName->React.string} </TextStyle>
      <Badge variation=#neutral size=#small> {taxTypename->React.string} </Badge>
    </Inline>
  })
}

let tableColumns = (~loading, ~allShopsFiltered, ~onRequestDispatch) => [
  {
    Table.key: "retailprice-list",
    name: t("Price list"),
    layout: {minWidth: 220.->#px, width: 1.5->#fr},
    render: ({data: {shopName, name, taxIncluded, fromQuantity}}) =>
      <Stack space=#xxsmall>
        <DecreasingPriceListTextLine name fromQuantity taxIncluded />
        {if allShopsFiltered {
          <TextStyle size=#xxsmall variation=#normal> {shopName->React.string} </TextStyle>
        } else {
          React.null
        }}
      </Stack>,
  },
  {
    key: "retailprice-prices",
    name: t("Retail prices"),
    layout: {minWidth: 130.->#px},
    render: ({data: variantPrice}) =>
      <CatalogVariantPriceRetailTableEditCellWrapper
        variantPrice disabled=loading onRequestDispatch
      />,
  },
  {
    key: "retailprice-marginrate",
    name: t("Margin rt"),
    layout: {minWidth: 110.->#px},
    render: ({data: {valueExcludingTax, purchasePrice, taxRate}}) =>
      <PriceRateTableCell
        rateType=PriceCalculator.Retail.Rate.MarginRate valueExcludingTax purchasePrice taxRate
      />,
  },
  {
    key: "retailprice-markuprate",
    name: t("Markup rt"),
    layout: {minWidth: 110.->#px},
    render: ({data: {valueExcludingTax, purchasePrice, taxRate}}) =>
      <PriceRateTableCell
        rateType=PriceCalculator.Retail.Rate.MarkupRate valueExcludingTax purchasePrice taxRate
      />,
  },
]

type status =
  | Pristine
  | Editing
  | Submitting
  | SubmitSucceeded
  | SubmitFailed

let makeVariantPricesMutationInput = (variantPrices: array<variantPrice>) =>
  variantPrices
  ->Array.keep(current => current.edited || current.valueExcludingTax > 0.)
  ->Array.map(({id, priceId, fromQuantity, toQuantity, valueExcludingTax, valueIncludingTax}) =>
    Mutation.makeInputObjectInputUpdateVariantPrices(
      ~id?,
      ~priceId,
      ~valueIncludingTax,
      ~valueExcludingTax,
      ~fromQuantity?,
      ~toQuantity?,
      (),
    )
  )

let makeVariantPricesMutationsVariables = (~variantPrices: array<variantPrice>) =>
  variantPrices
  ->Array.reduce([], (acc, retailPrice) =>
    switch (
      retailPrice.edited,
      acc->Array.some(current => current.variantId === retailPrice.variantId),
    ) {
    | (true, false) => Array.concat(acc, [retailPrice])
    | _ => acc
    }
  )
  ->Array.map(variantPrice => {
    let input = Mutation.makeInputObjectInputUpdateVariant()
    let variantPricesInput =
      variantPrices
      ->Array.keep(current => current.variantId === variantPrice.variantId)
      ->makeVariantPricesMutationInput

    Mutation.makeVariables(~id=variantPrice.variantId, ~input, ~variantPricesInput, ())
  })

module SaveActionBar = {
  @react.component
  let make = React.memo((~status, ~onRequestDiscard, ~onRequestSave) =>
    switch status {
    | Editing | Submitting =>
      <View style={Style.style(~alignSelf=#flexEnd, ())}>
        <Box spaceX=#large spaceTop=#medium spaceBottom=#xxsmall>
          <Inline space=#small>
            <Button
              variation=#neutral
              size=#xsmall
              disabled={status === Submitting}
              onPress={onRequestDiscard}>
              {t("Discard")->React.string}
            </Button>
            <Button
              variation=#success
              size=#xsmall
              loading={status === Submitting}
              onPress={onRequestSave}>
              {t("Save prices")->React.string}
            </Button>
          </Inline>
        </Box>
      </View>
    | SubmitSucceeded =>
      <Box spaceTop=#medium spaceX=#large>
        <Banner textStatus=Success(t("The retail prices have been successfully updated.")) />
      </Box>
    | SubmitFailed =>
      <Box spaceTop=#medium spaceX=#large>
        <Banner
          textStatus=Danger(
            t(
              "Wino could not update the retail prices, please refresh the page before trying again.",
            ),
          )
        />
      </Box>
    | _ => React.null
    }
  )
}

module PreviousHook = {
  let use = value => {
    let ref = React.useRef(None)

    React.useEffect(() => {
      ref.current = Some(value)
      None
    })

    ref.current
  }

  let useChange = (fn, value) => {
    let previousValue = use(value)

    React.useEffect1(() => {
      switch previousValue {
      | Some(_) => fn()
      | _ => None
      }
    }, [previousValue->Option.getWithDefault(value) != value])
  }
}

@react.component
let make = (~cku, ~variantPrices: array<variantPrice>) => {
  let activeShop = Auth.useActiveShop()
  let captureEvent = SessionTracker.useCaptureEvent()
  let (mutate, mutation) = Mutation.use()
  let (status, setStatus) = React.useState(() => Pristine)
  let (rows, dispatch) = React.useReducer(RetailPriceReducer.make, variantPrices)

  // NOTE - This trigger is depending on upper tree variantQuery (re)fetching
  // TODO - It should share variant fragments and not depends on parent data (with useQuery)
  PreviousHook.useChange(() => {
    Reset(variantPrices)->dispatch
    None
  }, [variantPrices->Array.map(({id, valueExcludingTax}) => (id, valueExcludingTax))])

  PreviousHook.useChange(() => {
    RetailPricesPurchaseValueUpdated(variantPrices)->dispatch
    None
  }, [variantPrices->Array.map(({purchasePrice}) => purchasePrice)])

  ReactUpdateEffect.use1(() => {
    setStatus(_ => Pristine)
    None
  }, [activeShop])

  React.useEffect1(() => {
    if rows->Array.some(row => row.edited) {
      setStatus(_ => Editing)
    }
    None
  }, [rows])

  React.useEffect1(() => {
    if mutation.loading {
      setStatus(_ => Submitting)
    }
    None
  }, [mutation.loading])

  let onRequestDiscard = React.useCallback1(_ => {
    Reset(variantPrices)->dispatch
    setStatus(_ => Pristine)
  }, [variantPrices])

  let onRequestSave = React.useCallback1(_ => {
    makeVariantPricesMutationsVariables(~variantPrices=rows)
    ->Array.map(mutationVariables =>
      mutate(
        mutationVariables,
        ~refetchQueries=[
          VariantQuery.refetchQueryDescription(
            VariantQuery.makeVariables(~cku=cku->Scalar.CKU.serialize, ()),
          ),
        ],
      )->ApolloHelpers.mutationPromiseToFutureResult
    )
    ->ApolloHelpers.mergeFutureResults
    ->Future.get(result =>
      setStatus(
        _ =>
          switch result {
          | Ok(_) => SubmitSucceeded
          | Error(_) => SubmitFailed
          },
      )
    )
    captureEvent(#catalog_variant_page_retailprice_click_save)
  }, [rows])

  let allShopsFiltered = switch Auth.useScope() {
  | Organisation({activeShop: None}) => true
  | _ => false
  }
  let columns = React.useMemo2(
    () =>
      tableColumns(
        ~loading={status === Submitting},
        ~allShopsFiltered,
        ~onRequestDispatch=dispatch,
      ),
    (status, allShopsFiltered),
  )

  let placeholderEmptyState =
    <Placeholder status=NoDataAvailable customText={t("No price list has been yet recorded")} />

  <>
    <Navigation.Prompt
      message={t("Some changes in the retail prices have not been saved.")}
      shouldBlockOnRouteChange={_ => status === Editing || status === Submitting}
    />
    <Card variation=#table title={t("Retail prices")} shadowed={status === Editing}>
      <Box spaceX=#large spaceY=#small>
        <DraftBanner
          variation=#info maxLines=1 text={t("catalog_variant_page.retail_price_card.banner.text")}
        />
      </Box>
      <TableView
        columns
        data=AsyncData.Done(Ok(rows))
        keyExtractor=Row.keyExtractor
        maxHeight=325.
        placeholderEmptyState
      />
      <SaveActionBar status onRequestDiscard onRequestSave />
    </Card>
  </>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["variantPrices"] == newProps["variantPrices"] && oldProps["cku"] === newProps["cku"]
)

// TODO - to be revamped
open Intl
open Style

let styles = StyleSheet.create({
  "root": style(~marginLeft=-12.->dp, ~width=100.->pct, ()),
  "itemPlaceholderText": merge([
    FontFaces.libreFranklinRegularStyle,
    style(
      ~marginLeft=2.->dp,
      ~fontStyle=#italic,
      ~fontSize=FontSizes.small,
      ~color=Colors.neutralColor50,
      ~lineHeight=36.,
      ~letterSpacing=0.125,
      (),
    ),
  ]),
  "inputTriggerAppend": style(
    ~position=#absolute,
    ~top=21.->dp,
    ~right=3.->dp,
    ~bottom=0.->dp,
    ~justifyContent=#center,
    (),
  ),
})

module CategoriesQuery = %graphql(`
    query CategoriesQuery($first: Int, $after: String, $search: String!, $filterByShopIds: InFilter) {
      categories(first: $first, after: $after, search: $search,
        filterBy: {
          archived: EXCLUDED,
          hasChildren: false,
          shopIds: $filterByShopIds
        }
      ) {
        edges {
          node {
            id
            formattedName
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `)

let throttle = (callback, limit) => {
  let wait = ref(false)

  if !wait.contents {
    callback()
    wait.contents = true
    Js.Global.setTimeout(() => wait.contents = false, limit)->ignore
  }
}

let edgesPerFetch = 20

type data = CategoriesQuery.CategoriesQuery_inner.t

type category = {
  id: string,
  formattedName: string,
}

let categoriesFromData: data => array<category> = data =>
  data.categories.edges->Array.map(({node: category}) => {
    id: category.id,
    formattedName: category.formattedName,
  })

type categoryChange =
  | CommonCategory(category)
  | NotClassifiedCategory

type triggerType = [#cell | #input]

@react.component
let make = (
  ~variation=#normal,
  ~compact=false,
  ~triggerType: triggerType=#cell,
  ~placeholder: option<string>=?,
  ~value: string,
  ~shopId: option<string>=?,
  ~errored=false,
  ~onChange: categoryChange => unit,
) => {
  let (executeQuery, queryResults) = CategoriesQuery.useLazy(
    ~fetchPolicy=CacheAndNetwork,
    ~notifyOnNetworkStatusChange=true,
    (),
  )
  let (searchQuery, setSearchQuery) = React.useState(_ => "")
  let (popoverOpened, setPopoverOpened) = React.useState(() => false)
  let (focused, setFocused) = React.useState(() => false)
  let (ref, hovered) = Hover.use()
  let activeShop = Auth.useActiveShop()

  let categoriesFilterByShopIds = switch (shopId, activeShop) {
  | (Some(shopId), _)
  | (_, Some({id: shopId})) =>
    Some(CategoriesQuery.makeInputObjectInFilter(~_in=[shopId], ()))
  | _ => None
  }

  // Refs required for fetching on scroll
  let queryStatusRef = React.useRef(None)
  let hasNextPageRef = React.useRef(false)
  let endCursorRef = React.useRef("")

  React.useEffect1(() => {
    switch queryResults {
    | Executed({
        networkStatus,
        data: Some({
          categories: {pageInfo: {hasNextPage: Some(hasNextPage), endCursor: Some(endCursor)}},
        }),
      }) =>
      queryStatusRef.current = Some(networkStatus)
      hasNextPageRef.current = hasNextPage
      endCursorRef.current = endCursor
    | _ => ()
    }
    None
  }, [queryResults])

  // Fetch first edges categories
  React.useEffect2(() => {
    if focused {
      executeQuery(
        CategoriesQuery.makeVariables(
          ~first=edgesPerFetch,
          ~search=searchQuery,
          ~filterByShopIds=?categoriesFilterByShopIds,
          (),
        ),
      )->ignore
    }
    None
  }, (searchQuery, focused))

  // Fetch more data with concatenation
  let fetchMore = React.useCallback1(() =>
    switch (queryResults, hasNextPageRef.current) {
    | (Executed({fetchMore, data: Some(_)}), true) =>
      fetchMore(
        ~variables=CategoriesQuery.makeVariables(
          ~first=edgesPerFetch,
          ~after=endCursorRef.current,
          ~search=searchQuery,
          ~filterByShopIds=?categoriesFilterByShopIds,
          (),
        ),
        ~updateQuery=(prevResult, {fetchMoreResult}) =>
          switch fetchMoreResult {
          | Some({categories: newCategories}) => {
              categories: {
                ...newCategories,
                edges: prevResult.categories.edges->Array.concat(newCategories.edges),
              },
            }
          | None => prevResult
          },
        (),
      )->ignore
    | _ => ()
    }
  , [queryResults])

  // Handles fetching from scroll
  let onEndReached = offset =>
    switch (offset < 50, queryStatusRef.current) {
    | (true, Some(Ready)) => throttle(fetchMore, 850)
    | _ => ()
    }

  // Renders status text at the end of the query results list
  let renderEndItem = React.useCallback1(() =>
    <Box spaceLeft=#small>
      <Text style={styles["itemPlaceholderText"]}>
        {switch queryResults {
        | Unexecuted(_)
        | Executed({loading: true}) =>
          t("Loading...")->React.string
        | Executed({error: Some(_)}) => t("Loading error")->React.string
        | Executed({data: Some({categories: {edges: []}})}) => t("No result found")->React.string
        | _ => React.null
        }}
      </Text>
    </Box>
  , [queryResults])

  // Renders secondary actions after the query results list
  let renderEndActions = React.useMemo1(() =>
    switch queryResults {
    | Executed({data: Some({categories: {pageInfo: {hasNextPage: Some(false)}}})}) =>
      Some(
        () => [
          <SearchListItem
            name={t("Not classified")}
            onPress={_ => {
              setPopoverOpened(_ => false)
              onChange(NotClassifiedCategory)
            }}
          />,
        ],
      )
    | _ => None
    }
  , [queryResults])

  let onToggle = (opened: bool) => {
    setFocused(_ => opened)
    setPopoverOpened(_ => opened)
  }

  let items = React.useMemo1(() =>
    switch queryResults {
    | Executed({data: Some(data)}) => Some(categoriesFromData(data))
    | _ => None
    }
  , [queryResults])
  let itemToValue = category => category.id
  let itemToOption = category => category.formattedName

  <View style={styles["root"]}>
    <SearchListPopover
      opened=popoverOpened
      expendableWidth={triggerType === #input}
      items
      itemToValue
      itemToOption
      onToggle
      onChange={value => onChange(CommonCategory(value))}
      onSearchQueryChange={value => setSearchQuery(_ => value)}
      onEndReached
      renderEndItem
      ?renderEndActions>
      {switch triggerType {
      | #cell =>
        <ButtonPhased variation compact hovered focused errored ref>
          {value->React.string}
        </ButtonPhased>
      | #input =>
        <Field label={t("Category")} ref>
          <OverlayTriggerView
            preset={#inputField({required: false})}
            size={compact ? #compact : #normal}
            focused
            hovered
            icon={focused ? #arrow_up_light : #arrow_down_light}>
            <TextInput value readOnly=true ?placeholder />
          </OverlayTriggerView>
        </Field>
      }}
    </SearchListPopover>
  </View>
}

let make = React.memo(make)

open Style
open Intl

let pluRange = (1, 9997)

let styles = StyleSheet.create({
  "wrapper": style(~flex=1., ~flexDirection=#row, ()),
  "button": style(~paddingLeft=Spaces.small->dp, ()),
})

let buttonStyleFromParams = (~errored) =>
  switch errored {
  | true => style(~top=2.->dp, ~alignSelf=#center, ())
  | false => style(~alignSelf=#flexEnd, ())
  }

module ConsummedPriceLookUpCodesQuery = %graphql(`
  query variantsPriceLookUpCodes($shopId: String!) {
    variantsPriceLookUpCodes(shopId: $shopId)
  }
`)

let makeValue = (~alreadyConsummedValues, ~pluRange=(pluRange->fst, pluRange->snd), ()) => {
  let (minPlu, maxPlu) = pluRange

  Array.makeBy(maxPlu - minPlu + 1, index => index + 1)
  ->Array.getBy(plu => !(alreadyConsummedValues->Array.some(current => current === plu)))
  ->Option.mapWithDefault(Error(), plu => Ok(plu))
}

@react.component
let make = (
  ~value: option<int>,
  ~errorMessage,
  ~onAlreadyConsummedValuesFetched,
  ~onChange: option<int> => unit,
) => {
  let originalPluRef = React.useRef(value)
  let shopId = Auth.useActiveShop()->Option.map(shop => shop.id)
  let queryResults = ConsummedPriceLookUpCodesQuery.use(
    ConsummedPriceLookUpCodesQuery.makeVariables(~shopId=shopId->Option.getWithDefault(""), ()),
    ~skip=shopId->Option.isNone,
    ~fetchPolicy=CacheAndNetwork,
  )

  // NOTE - In case of edition the original value is removed from consummed list
  let alreadyConsummedValues = switch queryResults {
  | {data: Some({variantsPriceLookUpCodes: pluValues})} =>
    pluValues->Array.keep(plu => Some(plu) !== originalPluRef.current)
  | _ => []
  }

  ReactUpdateEffect.use1(() => {
    switch (alreadyConsummedValues, queryResults.data) {
    | ([], None) => ()
    | _ => onAlreadyConsummedValuesFetched(alreadyConsummedValues)
    }
    None
  }, [queryResults])

  let onPressGeneratePlu = React.useCallback1(
    _ => makeValue(~alreadyConsummedValues, ())->Result.map(plu => onChange(Some(plu)))->ignore,
    [alreadyConsummedValues],
  )

  let onChange = value => onChange(value->Option.map(Int.fromFloat))

  let generatePluButtonDisabled = queryResults.loading || shopId->Option.isNone

  <View style={styles["wrapper"]}>
    <InputOptionalNumberField
      label={t("PLU code")}
      placeholder={queryResults.loading ? t("Loading") ++ "..." : "1-9997"}
      value={value->Option.map(Float.fromInt)}
      ?errorMessage
      disabled=queryResults.loading
      minValue=1.
      maxValue={pluRange->snd->Float.fromInt}
      precision=0
      useGrouping=false
      onChange
    />
    <View
      style={[
        styles["button"],
        buttonStyleFromParams(~errored=errorMessage->Option.isSome),
      ]->arrayStyle}>
      <Button
        variation=#neutral
        size=#small
        disabled=generatePluButtonDisabled
        onPress=onPressGeneratePlu>
        {t("Generate code")->React.string}
      </Button>
    </View>
  </View>
}

let make = React.memo(make)

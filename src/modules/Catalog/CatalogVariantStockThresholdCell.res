@react.component
let make = (
  ~compact=false,
  ~stockQuantity,
  ~stockState,
  ~formattedShopsNames,
  ~id,
  ~maxStockThreshold,
  ~minStockThreshold,
  ~stockOrderTriggerThreshold,
  ~bulk,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())

  // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
  let popover = {
    ...popover,
    onRequestClose: () =>
      if !triggerHovered {
        popover.onRequestClose()
      },
  }

  switch (Auth.useScope(), bulk) {
  | (Single(_), false)
  | (Organisation({activeShop: Some(_)}), false) =>
    <>
      <OpeningButton
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        opened=popover.opened
        onPress={_ => popover.onRequestToggle()}>
        <ProductStockTableCell
          size={compact ? #xsmall : #normal} value=stockQuantity state=?stockState
        />
      </OpeningButton>
      {if popover.opened {
        <CatalogVariantStockThresholdFormPopover
          popover
          popoverTriggerRef
          popoverAriaProps
          variantId=id
          maxStockThreshold
          minStockThreshold
          stockOrderTriggerThreshold
        />
      } else {
        React.null
      }}
    </>
  | (Single(_), true)
  | (Organisation({activeShop: Some(_)}), true) =>
    <>
      <ProductStockTableCell
        size={compact ? #xsmall : #normal} value=stockQuantity state=?stockState
      />
    </>
  | _ =>
    <ProductStockTableCell
      size={compact ? #xsmall : #normal} value=stockQuantity state=?stockState ?formattedShopsNames
    />
  }
}

let make = React.memo(make)

open Intl

module Row = {
  type t = {
    supplierId: string,
    purchasePrice: float,
  }
  let keyExtractor = row => row.supplierId ++ "-retail"
}

let tableColumns = (
  ~shopId,
  ~bulkUnit,
  ~onRequestSupplierUpdate,
  ~onRequestPurchasePriceUpdate,
) => [
  {
    Table.key: "supplier-id",
    name: t("Supplier"),
    layout: {minWidth: 350.->#px},
    render: ({data: {Row.supplierId: supplierId}}) =>
      switch shopId {
      | Some(shopId) =>
        <View style={Style.style(~width=100.->Style.pct, ())}>
          <SupplierSelect
            preset=#inputField({OverlayTriggerView.required: false})
            shopId=Some(shopId)
            value=Some({SupplierSelect.id: supplierId->Js.Nullable.return, name: ""})
            onChange=onRequestSupplierUpdate
          />
        </View>
      | _ => React.null
      },
  },
  {
    key: "purchasedprice",
    name: t("Purchase price") ++ " " ++ t("HT"),
    render: ({data: {purchasePrice}}) =>
      <InputNumberField
        appender={Custom(
          #EUR->Intl.toCurrencySymbol ++ bulkUnit->Option.mapWithDefault("", unit => ` / ${unit}`),
        )}
        minValue=0.
        precision=3
        value=purchasePrice
        onChange=onRequestPurchasePriceUpdate
      />,
  },
]

@react.component
let make = () => {
  let {supplierId, purchasePrice, capacityUnit, bulk} = CatalogVariantEditForm.useFormState().values
  let dispatch = CatalogVariantEditForm.useFormDispatch()
  let shopId = Auth.useActiveShop()->Option.map(shop => shop.id)

  let onRequestSupplierUpdate = React.useCallback0(supplier =>
    switch supplier->Option.flatMap(supplier => supplier.SupplierSelect.id->Js.Nullable.toOption) {
    | Some(supplierId) => FieldValueChanged(SupplierId, _ => supplierId)->dispatch
    | None => ()
    }
  )
  let onRequestPurchasePriceUpdate = React.useCallback0(purchasePrice =>
    FieldValueChanged(PurchasePrice, _ => purchasePrice)->dispatch
  )

  let columns = React.useMemo3(() =>
    tableColumns(
      ~shopId,
      ~bulkUnit=switch (capacityUnit, bulk) {
      | (Some(unit), true) => Some(unit)
      | _ => None
      },
      ~onRequestSupplierUpdate,
      ~onRequestPurchasePriceUpdate,
    )
  , (capacityUnit, bulk, shopId))

  let rows = [
    {
      Row.supplierId,
      purchasePrice,
    },
  ]

  let placeholderEmptyState =
    <Placeholder status=NoDataAvailable customText={t("No purchase price has been yet recorded")} />
  let data = AsyncData.Done(Ok(rows))

  <TableLayoutPanel
    title={t("Purchase price")} description={t("Enter the product purchase price.")}>
    React.null
    <TableView columns data keyExtractor=Row.keyExtractor placeholderEmptyState />
  </TableLayoutPanel>
}

let make = React.memo(make)

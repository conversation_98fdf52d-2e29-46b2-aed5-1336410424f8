open Intl

type t =
  | Amex
  | BankTransfer
  | Barnaby
  | Cash
  | Cheque
  | ContactlessAmex
  | ContactlessDebitCard
  | CreditNote
  | DebitCard
  | DistanceSelling
  | EuskoCash
  | EuskoDigital
  | GiftVoucher
  | Lydia
  | LesHabitues
  | Paypal
  | TicketRestaurant
  | TownHallGiftVoucher
  | WinoPay

let fromRawValue = value =>
  switch value {
  | #AMEX => Amex
  | #BANK_TRANSFER => BankTransfer
  | #BARNABY => Barnaby
  | #CASH => Cash
  | #CHEQUE => Cheque
  | #CONTACTLESS_AMEX => ContactlessAmex
  | #CONTACTLESS_DEBIT_CARD => ContactlessDebitCard
  | #CREDIT_MEMO => CreditNote
  | #DEBIT_CARD => DebitCard
  | #DISTANCE_SELLING => DistanceSelling
  | #EUSKO_CASH => EuskoCash
  | #EUSKO_DIGITAL => EuskoDigital
  | #GIFT_VOUCHER => GiftVoucher
  | #LYDIA => Lydia
  | #LESHABITUES => LesHabitues
  | #PAYPAL => Paypal
  | #TICKET_RESTAURANT => TicketRestaurant
  | #TOWN_HALL_GIFT_VOUCHER => TownHallGiftVoucher
  | #WINO_PAY => WinoPay
  }

let decodeFromJson = json =>
  switch json->Json.decodeString {
  | Some("AMEX") => Some(Amex)
  | Some("BANK_TRANSFER") => Some(BankTransfer)
  | Some("BARNABY") => Some(Barnaby)
  | Some("CASH") => Some(Cash)
  | Some("CHEQUE") => Some(Cheque)
  | Some("CONTACTLESS_AMEX") => Some(ContactlessAmex)
  | Some("CONTACTLESS_DEBIT_CARD") => Some(ContactlessDebitCard)
  | Some("CREDIT_MEMO") => Some(CreditNote)
  | Some("DEBIT_CARD") => Some(DebitCard)
  | Some("DISTANCE_SELLING") => Some(DistanceSelling)
  | Some("EUSKO_CASH") => Some(EuskoCash)
  | Some("EUSKO_DIGITAL") => Some(EuskoDigital)
  | Some("GIFT_VOUCHER") => Some(GiftVoucher)
  | Some("LYDIA") => Some(Lydia)
  | Some("LESHABITUES") => Some(LesHabitues)
  | Some("PAYPAL") => Some(Paypal)
  | Some("TICKET_RESTAURANT") => Some(TicketRestaurant)
  | Some("TOWN_HALL_GIFT_VOUCHER") => Some(TownHallGiftVoucher)
  | Some("WINO_PAY") => Some(WinoPay)
  | Some(_) | None => None
  }

let encodeToJson = value =>
  switch value {
  | Amex => "AMEX"
  | BankTransfer => "BANK_TRANSFER"
  | Barnaby => "BARNABY"
  | Cash => "CASH"
  | Cheque => "CHEQUE"
  | ContactlessAmex => "CONTACTLESS_AMEX"
  | ContactlessDebitCard => "CONTACTLESS_DEBIT_CARD"
  | CreditNote => "CREDIT_MEMO"
  | DebitCard => "DEBIT_CARD"
  | DistanceSelling => "DISTANCE_SELLING"
  | EuskoCash => "EUSKO_CASH"
  | EuskoDigital => "EUSKO_DIGITAL"
  | GiftVoucher => "GIFT_VOUCHER"
  | Lydia => "LYDIA"
  | LesHabitues => "LESHABITUES"
  | Paypal => "PAYPAL"
  | TicketRestaurant => "TICKET_RESTAURANT"
  | TownHallGiftVoucher => "TOWN_HALL_GIFT_VOUCHER"
  | WinoPay => "WINO_PAY"
  }->Json.encodeString

let equal = (a, b) =>
  switch (a, b) {
  | (Amex, Amex)
  | (BankTransfer, BankTransfer)
  | (Barnaby, Barnaby)
  | (Cash, Cash)
  | (Cheque, Cheque)
  | (ContactlessAmex, ContactlessAmex)
  | (ContactlessDebitCard, ContactlessDebitCard)
  | (CreditNote, CreditNote)
  | (DebitCard, DebitCard)
  | (DistanceSelling, DistanceSelling)
  | (EuskoCash, EuskoCash)
  | (EuskoDigital, EuskoDigital)
  | (GiftVoucher, GiftVoucher)
  | (Lydia, Lydia)
  | (LesHabitues, LesHabitues)
  | (Paypal, Paypal)
  | (TicketRestaurant, TicketRestaurant)
  | (TownHallGiftVoucher, TownHallGiftVoucher)
  | (WinoPay, WinoPay) => true
  | _ => false
  }

let toLabel = value =>
  switch value {
  | Amex => "Amex"
  | BankTransfer => t("Bank Transfer")
  | Barnaby => "Barnaby"
  | Cash => t("Cash")
  | Cheque => t("Cheque")
  | ContactlessAmex => t("Amex contactless")
  | ContactlessDebitCard => t("Debit Card contactless")
  | CreditNote => t("Credit note")
  | DistanceSelling => t("Distance Selling")
  | DebitCard => t("Debit Card")
  | EuskoCash => "Eusko Cash"
  | EuskoDigital => "Eusko Digital"
  | GiftVoucher => t("Gift voucher")
  | Lydia => "Lydia"
  | LesHabitues => "Les Habitués"
  | Paypal => "Paypal"
  | TicketRestaurant => "Ticket Restaurant"
  | TownHallGiftVoucher => t("Council voucher")
  | WinoPay => "WinoPay"
  }

let toShortLabel = value =>
  switch value {
  | Amex => "Amex"
  | BankTransfer => t("Bank transfer")
  | Barnaby => "Barnaby"
  | Cash => t("Cash")
  | Cheque => t("Cheque")
  | ContactlessAmex => t("Amex - CTLS")
  | ContactlessDebitCard => t("DC - CTLS")
  | CreditNote => t("Credit note")
  | DistanceSelling => t("Dist. sell.")
  | DebitCard => t("DC")
  | EuskoCash => "Eusko cash"
  | EuskoDigital => "Eusko digital"
  | GiftVoucher => t("Gift voucher")
  | Lydia => "Lydia"
  | LesHabitues => "Les Habitués"
  | Paypal => "Paypal"
  | TicketRestaurant => "Ticket resto"
  | TownHallGiftVoucher => t("Council voucher")
  | WinoPay => "WinoPay"
  }

let values = SortArray.stableSortBy(
  [
    Amex,
    BankTransfer,
    Cash,
    Cheque,
    ContactlessAmex,
    ContactlessDebitCard,
    CreditNote,
    DebitCard,
    DistanceSelling,
    EuskoCash,
    EuskoDigital,
    GiftVoucher,
    Lydia,
    Paypal,
    TicketRestaurant,
    TownHallGiftVoucher,
    WinoPay,
  ],
  (next, current) => Js.String.localeCompare(toLabel(current), toLabel(next))->Float.toInt,
)

module ColorSet = {
  type t = {
    borderCard: Style.Color.t,
    badgeTextAndFillSvg: Style.Color.t,
    backgroundBadge: Style.Color.t,
    borderBadgeAndBorderSvg: Style.Color.t,
  }

  let make = value =>
    switch value {
    | Amex => {
        borderCard: "#4962B6",
        badgeTextAndFillSvg: "#1D2749",
        backgroundBadge: "#D8DFF9",
        borderBadgeAndBorderSvg: "#B9C0DD",
      }
    | BankTransfer => {
        borderCard: "#8C8B9B",
        badgeTextAndFillSvg: "#302F37",
        backgroundBadge: "#E7E7EE",
        borderBadgeAndBorderSvg: "#BDBDCA",
      }
    | Cash => {
        borderCard: "#D2ACF9",
        badgeTextAndFillSvg: "#650EBE",
        backgroundBadge: "#ECDBFC",
        borderBadgeAndBorderSvg: "#E3C9FB",
      }
    | Cheque => {
        borderCard: "#FAEAA4",
        badgeTextAndFillSvg: "#C19F0B",
        backgroundBadge: "#FFF6CC",
        borderBadgeAndBorderSvg: "#DBD09D",
      }
    | ContactlessAmex => {
        borderCard: "#91A3E4",
        badgeTextAndFillSvg: "#1E327B",
        backgroundBadge: "#ECF0FB",
        borderBadgeAndBorderSvg: "#DBE2F4",
      }
    | ContactlessDebitCard => {
        borderCard: "#AEE78F",
        badgeTextAndFillSvg: "#35850c",
        backgroundBadge: "#D6F3C7",
        borderBadgeAndBorderSvg: "#C1E2B8",
      }
    | CreditNote => {
        borderCard: "#04804C",
        badgeTextAndFillSvg: "#02311D",
        backgroundBadge: "#CDFBE8",
        borderBadgeAndBorderSvg: "#A5E3C9",
      }
    | DebitCard => {
        borderCard: "#1CBB7A",
        badgeTextAndFillSvg: "#0D593A",
        backgroundBadge: "#C8F3E1",
        borderBadgeAndBorderSvg: "#B5E2CF",
      }
    | DistanceSelling => {
        borderCard: "#F88145",
        badgeTextAndFillSvg: "#943505",
        backgroundBadge: "#FFDAC7",
        borderBadgeAndBorderSvg: "#F9B390",
      }
    | EuskoCash => {
        borderCard: "#25C400",
        badgeTextAndFillSvg: "#0A3300",
        backgroundBadge: "#9ADA8B",
        borderBadgeAndBorderSvg: "#8DCB7F",
      }
    | EuskoDigital => {
        borderCard: "#87C878",
        badgeTextAndFillSvg: "#25491D",
        backgroundBadge: "#BBE7B1",
        borderBadgeAndBorderSvg: "#ABD6A1",
      }
    | GiftVoucher => {
        borderCard: "#DFCF37",
        badgeTextAndFillSvg: "#847915",
        backgroundBadge: "#FFF7AC",
        borderBadgeAndBorderSvg: "#E9E08A",
      }
    | Lydia => {
        borderCard: "#2792FF",
        badgeTextAndFillSvg: "#003266",
        backgroundBadge: "#BBDDFF",
        borderBadgeAndBorderSvg: "#91BAEC",
      }
    | Paypal => {
        borderCard: "#003899",
        badgeTextAndFillSvg: "#001333",
        backgroundBadge: "#DBE7FC",
        borderBadgeAndBorderSvg: "#BED4FC",
      }
    | TicketRestaurant => {
        borderCard: "#5AD3E5",
        badgeTextAndFillSvg: "#157684",
        backgroundBadge: "#C6F6FD",
        borderBadgeAndBorderSvg: "#ABDFE6",
      }
    | TownHallGiftVoucher => {
        borderCard: "#D2844C",
        badgeTextAndFillSvg: "#7A451F",
        backgroundBadge: "#FFD7BB",
        borderBadgeAndBorderSvg: "#EBC4A7",
      }
    | WinoPay => {
        borderCard: "#A01E72",
        badgeTextAndFillSvg: "#7F1C5C",
        backgroundBadge: "#F4E4EE",
        borderBadgeAndBorderSvg: "#EACEE1",
      }
    // NOTE - deprecated method payments
    | Barnaby
    | LesHabitues => {
        borderCard: "#",
        badgeTextAndFillSvg: "",
        backgroundBadge: "#FFF",
        borderBadgeAndBorderSvg: "#BDBDCA",
      }
    }
}

module Svg = {
  @react.component
  let make = (~value, ~fill=?) => {
    let fill = Option.getWithDefault(fill, ColorSet.make(value).badgeTextAndFillSvg)
    switch value {
    | DebitCard =>
      <svg width="20px" height="20px" viewBox="0 0 40 40">
        <path
          d="M 34.5 4 C 37.538 4 40 6.462 40 9.5 L 40 30.5 C 40 33.538 37.538 36 34.5 36 L 5.5 36 C 2.462 36 0 33.538 0 30.5 L 0 9.5 C 0 6.462 2.462 4 5.5 4 L 34.5 4 Z M 37 16.5 L 3 16.5 L 3 30.5 C 3 31.881 4.119 33 5.5 33 L 34.5 33 C 35.881 33 37 31.881 37 30.5 L 37 16.5 Z M 15 26 C 15.828 26 16.5 26.672 16.5 27.5 C 16.5 28.328 15.828 29 15 29 L 7 29 C 6.172 29 5.5 28.328 5.5 27.5 C 5.5 26.672 6.172 26 7 26 L 15 26 Z M 34.5 7 L 5.5 7 C 4.119 7 3 8.119 3 9.5 L 3 13.5 L 37 13.5 L 37 9.5 C 37 8.119 35.881 7 34.5 7 Z"
          fill
        />
      </svg>
    | ContactlessDebitCard =>
      <svg width="30px" height="30px" viewBox="0 0 40 40">
        <path
          d="M 29.836 19.016 C 31.828 19.016 33.443 20.631 33.443 22.623 L 33.443 36.393 C 33.443 38.385 31.828 40 29.836 40 L 10.82 40 C 8.828 40 7.213 38.385 7.213 36.393 L 7.213 22.623 C 7.213 20.631 8.828 19.016 10.82 19.016 L 29.836 19.016 Z M 31.475 27.213 L 9.18 27.213 L 9.18 36.393 C 9.18 37.299 9.914 38.033 10.82 38.033 L 29.836 38.033 C 30.741 38.033 31.475 37.299 31.475 36.393 L 31.475 27.213 Z M 17.049 33.443 C 17.592 33.443 18.033 33.883 18.033 34.426 C 18.033 34.969 17.592 35.41 17.049 35.41 L 11.803 35.41 C 11.26 35.41 10.82 34.969 10.82 34.426 C 10.82 33.883 11.26 33.443 11.803 33.443 L 17.049 33.443 Z M 29.836 20.984 L 10.82 20.984 C 9.914 20.984 9.18 21.718 9.18 22.623 L 9.18 25.246 L 31.475 25.246 L 31.475 22.623 C 31.475 21.718 30.741 20.984 29.836 20.984 Z M 20 5.798 C 23.723 5.798 27.254 7.07 30.09 9.363 C 30.513 9.705 30.578 10.324 30.237 10.747 C 29.895 11.169 29.276 11.234 28.853 10.893 C 26.364 8.88 23.269 7.765 20 7.765 C 16.731 7.765 13.635 8.88 11.147 10.893 C 10.724 11.234 10.105 11.169 9.763 10.747 C 9.422 10.324 9.487 9.705 9.91 9.363 C 12.745 7.07 16.277 5.798 20 5.798 Z M 20 0 C 25.068 0 29.874 1.732 33.733 4.853 C 34.156 5.195 34.221 5.814 33.88 6.236 C 33.538 6.659 32.919 6.724 32.496 6.383 C 28.984 3.542 24.614 1.967 20 1.967 C 15.386 1.967 11.016 3.542 7.504 6.383 C 7.081 6.724 6.462 6.659 6.12 6.236 C 5.779 5.814 5.844 5.195 6.267 4.853 C 10.126 1.732 14.932 0 20 0 Z M 20 11.595 C 22.379 11.595 24.636 12.408 26.447 13.874 C 26.87 14.215 26.935 14.835 26.594 15.257 C 26.252 15.679 25.633 15.745 25.21 15.403 C 23.746 14.219 21.924 13.562 20 13.562 C 18.076 13.562 16.254 14.219 14.79 15.403 C 14.367 15.745 13.748 15.679 13.406 15.257 C 13.065 14.835 13.13 14.215 13.553 13.874 C 15.364 12.408 17.621 11.595 20 11.595 Z"
          fill
        />
      </svg>
    | Amex =>
      <svg width="24px" height="24px" viewBox="0 0 40 40">
        <path
          d="M 35.882 6.471 C 38.156 6.471 40 8.314 40 10.588 L 40 29.412 C 40 31.686 38.156 33.529 35.882 33.529 L 4.118 33.529 C 1.844 33.529 0 31.686 0 29.412 L 0 10.588 C 0 8.314 1.844 6.471 4.118 6.471 L 35.882 6.471 Z M 35.882 9.007 L 4.118 9.007 C 3.245 9.007 2.537 9.715 2.537 10.588 L 2.537 29.412 C 2.537 30.285 3.245 30.993 4.118 30.993 L 35.882 30.993 C 36.755 30.993 37.463 30.285 37.463 29.412 L 37.463 10.588 C 37.463 9.715 36.755 9.007 35.882 9.007 Z M 12.76 15.772 C 12.813 15.772 12.866 15.777 12.918 15.787 L 12.958 15.799 L 13.002 15.786 L 13.154 15.772 L 16.697 15.772 C 16.983 15.772 17.246 15.916 17.401 16.15 L 17.465 16.268 L 17.526 16.155 C 17.654 15.958 17.86 15.823 18.091 15.784 L 18.233 15.772 L 27.075 15.772 C 27.319 15.772 27.552 15.878 27.713 16.063 L 27.992 16.384 L 28.315 16.04 C 28.443 15.903 28.612 15.814 28.794 15.784 L 28.933 15.772 L 33.043 15.772 C 33.769 15.772 34.158 16.628 33.679 17.175 L 31.192 20.012 L 33.676 22.823 C 34.127 23.333 33.818 24.116 33.182 24.217 L 33.041 24.228 L 28.867 24.219 C 28.624 24.219 28.394 24.114 28.234 23.933 L 27.921 23.579 L 27.57 23.953 C 27.442 24.089 27.274 24.178 27.093 24.208 L 26.955 24.219 L 18.827 24.219 C 18.775 24.219 18.724 24.215 18.675 24.206 L 18.652 24.198 L 18.623 24.207 L 18.479 24.219 L 16.458 24.219 C 16.409 24.219 16.361 24.215 16.314 24.207 L 16.283 24.198 L 16.261 24.206 L 16.109 24.219 L 12.201 24.219 C 11.858 24.219 11.549 24.011 11.419 23.693 L 11.258 23.301 L 11.098 23.693 C 10.986 23.966 10.743 24.158 10.46 24.207 L 10.315 24.219 L 6.765 24.219 C 6.154 24.219 5.745 23.592 5.99 23.034 L 8.956 16.278 C 9.091 15.97 9.395 15.772 9.73 15.772 L 12.76 15.772 Z M 12.148 17.577 L 10.418 17.577 L 8.338 22.424 L 9.603 22.424 L 10.05 21.338 L 12.468 21.338 L 12.916 22.425 L 15.277 22.425 L 15.277 18.626 L 16.96 22.425 L 17.987 22.425 L 19.66 18.626 L 19.669 22.425 L 20.817 22.425 L 20.817 17.577 L 18.931 17.577 L 17.531 20.862 L 16.011 17.577 L 14.115 17.577 L 14.115 22.158 L 12.148 17.577 Z M 26.534 17.577 L 21.867 17.577 L 21.867 22.422 L 26.462 22.422 L 27.943 20.816 L 29.37 22.422 L 30.863 22.422 L 28.694 20.014 L 30.863 17.577 L 29.435 17.577 L 27.962 19.164 L 26.534 17.577 Z M 25.932 18.58 L 27.202 19.995 L 25.875 21.418 L 23.02 21.418 L 23.02 20.452 L 25.566 20.452 L 25.566 19.466 L 23.02 19.466 L 23.02 18.58 L 25.932 18.58 Z M 11.26 18.397 L 12.056 20.333 L 10.463 20.333 L 11.26 18.397 Z"
          fill
        />
      </svg>
    | ContactlessAmex =>
      <svg width="24px" height="24px" viewBox="0 0 40 40">
        <path
          d="M 32.627 18.487 C 34.435 18.487 35.901 19.953 35.901 21.761 L 35.901 36.726 C 35.901 38.534 34.435 40 32.627 40 L 7.373 40 C 5.565 40 4.099 38.534 4.099 36.726 L 4.099 21.761 C 4.099 19.953 5.565 18.487 7.373 18.487 L 32.627 18.487 Z M 32.627 20.504 L 7.373 20.504 C 6.678 20.504 6.116 21.068 6.116 21.761 L 6.116 36.726 C 6.116 37.42 6.678 37.983 7.373 37.983 L 32.627 37.983 C 33.322 37.983 33.884 37.42 33.884 36.726 L 33.884 21.761 C 33.884 21.068 33.322 20.504 32.627 20.504 Z M 14.244 25.882 C 14.287 25.882 14.329 25.886 14.369 25.894 L 14.401 25.904 L 14.436 25.893 L 14.557 25.882 L 17.374 25.882 C 17.601 25.882 17.811 25.997 17.933 26.182 L 17.985 26.276 L 18.033 26.186 C 18.135 26.03 18.298 25.923 18.482 25.892 L 18.595 25.882 L 25.624 25.882 C 25.819 25.882 26.005 25.967 26.132 26.114 L 26.354 26.369 L 26.611 26.096 C 26.712 25.987 26.846 25.916 26.992 25.892 L 27.102 25.882 L 30.369 25.882 C 30.947 25.882 31.255 26.563 30.875 26.997 L 28.898 29.253 L 30.873 31.488 C 31.231 31.894 30.986 32.516 30.481 32.597 L 30.368 32.605 L 27.049 32.598 C 26.857 32.598 26.674 32.515 26.547 32.371 L 26.298 32.089 L 26.018 32.387 C 25.917 32.495 25.783 32.565 25.639 32.589 L 25.529 32.598 L 19.068 32.598 C 19.027 32.598 18.986 32.594 18.947 32.588 L 18.928 32.581 L 18.906 32.589 L 18.791 32.598 L 17.183 32.598 C 17.146 32.598 17.107 32.594 17.07 32.589 L 17.045 32.581 L 17.027 32.588 L 16.906 32.598 L 13.8 32.598 C 13.527 32.598 13.281 32.433 13.178 32.18 L 13.05 31.868 L 12.922 32.18 C 12.834 32.397 12.64 32.549 12.415 32.588 L 12.3 32.598 L 9.478 32.598 C 8.992 32.598 8.667 32.099 8.862 31.656 L 11.22 26.284 C 11.326 26.04 11.568 25.882 11.836 25.882 L 14.244 25.882 Z M 13.757 27.317 L 12.382 27.317 L 10.728 31.17 L 11.734 31.17 L 12.089 30.307 L 14.011 30.307 L 14.368 31.172 L 16.245 31.172 L 16.245 28.152 L 17.583 31.172 L 18.4 31.172 L 19.73 28.152 L 19.738 31.172 L 20.649 31.172 L 20.649 27.317 L 19.15 27.317 L 18.037 29.929 L 16.828 27.317 L 15.321 27.317 L 15.321 30.959 L 13.757 27.317 Z M 25.195 27.317 L 21.484 27.317 L 21.484 31.169 L 25.137 31.169 L 26.315 29.893 L 27.45 31.169 L 28.636 31.169 L 26.912 29.254 L 28.636 27.317 L 27.501 27.317 L 26.33 28.579 L 25.195 27.317 Z M 24.715 28.114 L 25.726 29.24 L 24.671 30.372 L 22.4 30.372 L 22.4 29.603 L 24.425 29.603 L 24.425 28.819 L 22.4 28.819 L 22.4 28.114 L 24.715 28.114 Z M 13.051 27.969 L 13.683 29.509 L 12.418 29.509 L 13.051 27.969 Z M 20.234 5.944 C 24.051 5.944 27.671 7.248 30.579 9.599 C 31.012 9.95 31.079 10.584 30.728 11.017 C 30.378 11.45 29.744 11.517 29.311 11.168 C 26.759 9.104 23.586 7.961 20.234 7.961 C 16.882 7.961 13.709 9.104 11.157 11.168 C 10.724 11.517 10.089 11.45 9.74 11.017 C 9.389 10.584 9.456 9.95 9.889 9.599 C 12.796 7.248 16.417 5.944 20.234 5.944 Z M 20.234 0 C 25.429 0 30.356 1.775 34.314 4.975 C 34.747 5.326 34.814 5.96 34.463 6.393 C 34.114 6.826 33.478 6.893 33.045 6.544 C 29.444 3.632 24.964 2.017 20.234 2.017 C 15.504 2.017 11.024 3.632 7.423 6.544 C 6.989 6.893 6.354 6.826 6.005 6.393 C 5.654 5.96 5.721 5.326 6.154 4.975 C 10.111 1.775 15.037 0 20.234 0 Z M 20.234 11.887 C 22.673 11.887 24.987 12.721 26.844 14.224 C 27.277 14.573 27.344 15.208 26.994 15.641 C 26.643 16.075 26.009 16.141 25.576 15.792 C 24.074 14.577 22.206 13.904 20.234 13.904 C 18.262 13.904 16.394 14.577 14.892 15.792 C 14.459 16.141 13.825 16.075 13.474 15.641 C 13.124 15.208 13.191 14.573 13.624 14.224 C 15.481 12.721 17.795 11.887 20.234 11.887 Z"
          fill
        />
      </svg>
    | Cash =>
      <svg width="24px" height="24px" viewBox="0 0 40 40">
        <path
          d="M 13.333 0 C 20.133 0 25.846 2.515 26.142 6.353 L 26.154 6.667 L 26.154 14.369 L 26.667 14.359 C 33.748 14.359 39.487 20.098 39.487 27.179 C 39.487 34.261 33.748 40 26.667 40 C 23.721 40 21.007 39.007 18.841 37.335 L 19.225 37.243 C 17.432 37.707 15.422 37.949 13.333 37.949 C 6.349 37.949 0.513 35.294 0.513 31.282 L 0.513 6.667 C 0.513 2.654 6.351 0 13.333 0 Z M 26.667 17.436 C 21.286 17.436 16.923 21.799 16.923 27.179 C 16.923 32.56 21.286 36.923 26.667 36.923 C 32.047 36.923 36.41 32.56 36.41 27.179 C 36.41 21.799 32.047 17.436 26.667 17.436 Z M 3.59 29.577 L 3.59 31.282 C 3.59 32.933 7.852 34.872 13.333 34.872 C 14.345 34.872 15.331 34.808 16.271 34.683 C 15.623 33.787 15.089 32.806 14.687 31.758 C 14.246 31.783 13.791 31.795 13.333 31.795 C 9.471 31.795 5.957 30.983 3.59 29.577 Z M 3.59 23.413 L 3.59 25.128 C 3.59 26.779 7.852 28.718 13.333 28.718 L 13.936 28.708 C 13.877 28.207 13.846 27.696 13.846 27.179 C 13.846 26.652 13.879 26.131 13.941 25.621 L 13.333 25.631 C 9.471 25.631 5.957 24.818 3.59 23.413 Z M 3.59 17.27 L 3.59 18.964 C 3.59 20.615 7.852 22.554 13.333 22.554 C 13.807 22.554 14.271 22.542 14.726 22.515 C 15.179 21.344 15.801 20.263 16.56 19.292 C 15.526 19.419 14.445 19.487 13.333 19.487 C 9.471 19.487 5.957 18.675 3.59 17.27 Z M 13.333 13.333 C 9.471 13.333 5.959 12.521 3.592 11.116 L 3.59 12.821 C 3.59 14.472 7.852 16.41 13.333 16.41 C 18.814 16.41 23.077 14.472 23.077 12.821 L 23.077 11.116 C 20.71 12.521 17.196 13.333 13.333 13.333 Z M 13.333 3.077 C 7.854 3.077 3.59 5.015 3.59 6.667 C 3.59 8.318 7.854 10.256 13.333 10.256 C 18.812 10.256 23.077 8.318 23.077 6.667 C 23.077 5.015 18.812 3.077 13.333 3.077 Z"
          fill
        />
      </svg>
    | GiftVoucher
    | TownHallGiftVoucher =>
      <svg width="25px" height="25px" viewBox="0 0 40 40">
        <path
          d="M 24.211 0 C 27.408 0 30 2.592 30 5.789 C 30 6.739 29.773 7.634 29.368 8.423 L 33.158 8.421 C 36.356 8.421 38.947 11.013 38.947 14.211 L 38.947 21.053 L 36.842 21.053 L 36.842 34.211 C 36.842 37.408 34.251 40 31.053 40 L 8.947 40 C 5.749 40 3.158 37.408 3.158 34.211 L 3.158 21.053 L 1.053 21.053 L 1.053 14.211 C 1.053 11.013 3.644 8.421 6.842 8.421 L 10.632 8.423 C 10.227 7.634 10 6.739 10 5.789 C 10 2.592 12.592 0 15.789 0 C 17.328 0 18.728 0.6 19.766 1.581 L 20 1.815 C 21.055 0.697 22.552 0 24.211 0 Z M 18.421 21.053 L 6.316 21.053 L 6.316 34.211 C 6.316 35.573 7.352 36.695 8.678 36.829 L 8.947 36.842 L 18.421 36.842 L 18.421 21.053 Z M 33.684 21.053 L 21.579 21.053 L 21.579 36.842 L 31.053 36.842 C 32.505 36.842 33.684 35.663 33.684 34.211 L 33.684 21.053 Z M 18.421 11.579 L 6.842 11.579 C 5.389 11.579 4.211 12.758 4.211 14.211 L 4.211 17.895 L 18.421 17.895 L 18.421 11.579 Z M 21.579 17.895 L 35.789 17.895 L 35.789 14.211 C 35.789 12.848 34.754 11.726 33.427 11.592 L 33.158 11.579 L 21.579 11.579 L 21.579 17.895 Z M 15.789 3.158 C 14.335 3.158 13.158 4.335 13.158 5.789 C 13.158 7.244 14.335 8.421 15.789 8.421 L 18.421 8.421 L 18.421 5.789 C 18.421 4.335 17.244 3.158 15.789 3.158 Z M 24.211 3.158 C 22.756 3.158 21.579 4.335 21.579 5.789 L 21.579 8.421 L 24.211 8.421 C 25.665 8.421 26.842 7.244 26.842 5.789 C 26.842 4.335 25.665 3.158 24.211 3.158 Z"
          fill
        />
      </svg>
    | CreditNote =>
      <svg width="25px" height="25px" viewBox="0 0 40 40">
        <path
          d="M 34.5 4 C 37.538 4 40 6.462 40 9.5 L 40 30.5 C 40 33.538 37.538 36 34.5 36 L 5.5 36 C 2.462 36 0 33.538 0 30.5 L 0 9.5 C 0 6.462 2.462 4 5.5 4 L 34.5 4 Z M 34.5 7 L 5.5 7 C 4.12 7 3 8.12 3 9.5 L 3 30.5 C 3 31.88 4.12 33 5.5 33 L 34.5 33 C 35.88 33 37 31.88 37 30.5 L 37 9.5 C 37 8.12 35.88 7 34.5 7 Z M 20.75 14.24 C 22.41 12.58 25.1 12.58 26.76 14.24 C 28.42 15.9 28.42 18.59 26.76 20.25 L 20 27.01 L 13.24 20.25 C 11.58 18.59 11.58 15.9 13.24 14.24 C 14.9 12.58 17.59 12.58 19.25 14.24 C 19.57 14.56 19.8 14.92 20 15.3 C 20.2 14.92 20.43 14.56 20.75 14.24 Z"
          fill
        />
      </svg>
    | BankTransfer =>
      <svg width="25px" height="25px" viewBox="0 0 40 40">
        <path
          d="M 32.755 0 C 35.852 0 38.363 2.51 38.363 5.608 L 38.363 23.45 L 30.206 23.45 L 30.206 36.466 C 30.206 37.875 29.064 39.015 27.657 39.015 C 27.21 39.015 26.77 38.899 26.383 38.675 L 24.425 37.543 L 20.938 39.557 L 20.173 40 L 19.409 39.557 L 15.92 37.543 L 12.435 39.557 L 11.67 40 L 10.905 39.557 L 7.416 37.543 L 5.463 38.675 C 4.325 39.331 2.892 39.011 2.131 37.975 L 1.98 37.743 C 1.756 37.355 1.637 36.915 1.637 36.466 L 1.637 5.608 C 1.637 2.51 4.148 0 7.245 0 L 32.755 0 Z M 27.759 3.059 L 7.245 3.059 C 5.836 3.059 4.696 4.199 4.696 5.608 L 4.694 35.583 L 6.654 34.454 L 7.418 34.011 L 8.183 34.454 L 11.67 36.466 L 15.157 34.454 L 15.922 34.011 L 16.686 34.454 L 20.173 36.466 L 23.66 34.454 L 24.425 34.011 L 25.19 34.454 L 27.147 35.583 L 27.147 15.679 L 27.147 5.608 C 27.147 4.69 27.367 3.823 27.759 3.059 Z M 15.932 23.44 C 16.776 23.44 17.461 24.125 17.461 24.969 C 17.461 25.814 16.776 26.499 15.932 26.499 L 7.775 26.499 C 6.931 26.499 6.246 25.814 6.246 24.969 C 6.246 24.125 6.931 23.44 7.775 23.44 L 15.932 23.44 Z M 23.069 23.44 C 23.913 23.44 24.598 24.125 24.598 24.969 C 24.598 25.814 23.913 26.499 23.069 26.499 L 21.03 26.499 C 20.186 26.499 19.5 25.814 19.5 24.969 C 19.5 24.125 20.186 23.44 21.03 23.44 L 23.069 23.44 Z M 32.755 3.059 C 31.346 3.059 30.206 4.199 30.206 5.608 L 30.204 13.079 L 30.206 20.392 L 35.304 20.392 L 35.304 5.608 C 35.304 4.286 34.301 3.201 33.016 3.071 L 32.755 3.059 Z M 15.932 17.323 C 16.776 17.323 17.461 18.008 17.461 18.852 C 17.461 19.696 16.776 20.381 15.932 20.381 L 7.775 20.381 C 6.931 20.381 6.246 19.696 6.246 18.852 C 6.246 18.008 6.931 17.323 7.775 17.323 L 15.932 17.323 Z M 23.069 17.323 C 23.913 17.323 24.598 18.008 24.598 18.852 C 24.598 19.696 23.913 20.381 23.069 20.381 L 21.03 20.381 C 20.186 20.381 19.5 19.696 19.5 18.852 C 19.5 18.008 20.186 17.323 21.03 17.323 L 23.069 17.323 Z M 15.932 11.205 C 16.776 11.205 17.461 11.89 17.461 12.734 C 17.461 13.579 16.776 14.264 15.932 14.264 L 7.775 14.264 C 6.931 14.264 6.246 13.579 6.246 12.734 C 6.246 11.89 6.931 11.205 7.775 11.205 L 15.932 11.205 Z M 23.069 11.205 C 23.913 11.205 24.598 11.89 24.598 12.734 C 24.598 13.579 23.913 14.264 23.069 14.264 L 21.03 14.264 C 20.186 14.264 19.5 13.579 19.5 12.734 C 19.5 11.89 20.186 11.205 21.03 11.205 L 23.069 11.205 Z"
          fill
        />
      </svg>
    | Cheque =>
      <svg width="25px" height="25px" viewBox="0 0 40 40">
        <path
          d="M 37.255 8.627 C 38.77 8.627 40 9.857 40 11.373 L 40 28.627 C 40 30.143 38.77 31.373 37.255 31.373 L 2.745 31.373 C 1.23 31.373 0 30.143 0 28.627 L 0 11.373 C 0 9.857 1.23 8.627 2.745 8.627 L 37.255 8.627 Z M 37.647 14.902 L 2.353 14.902 L 2.353 28.627 C 2.353 28.844 2.529 29.02 2.745 29.02 L 37.255 29.02 C 37.471 29.02 37.647 28.844 37.647 28.627 L 37.647 14.902 Z M 29.043 23.109 C 29.224 23.313 29.366 23.548 29.465 23.802 L 29.492 23.871 C 29.52 23.944 29.6 23.98 29.672 23.953 C 29.678 23.95 29.685 23.947 29.691 23.944 L 30.325 23.587 C 31.085 23.161 32.005 23.122 32.8 23.481 L 35.365 24.642 C 35.956 24.91 36.218 25.608 35.95 26.199 C 35.683 26.791 34.985 27.054 34.394 26.786 L 31.829 25.625 C 31.716 25.573 31.584 25.578 31.476 25.639 L 30.842 25.995 C 30.737 26.053 30.629 26.105 30.518 26.147 C 29.305 26.615 27.951 26.071 27.387 24.925 L 27.305 24.739 L 25.216 26.595 L 24.657 27.089 L 23.972 26.795 L 20.505 25.307 C 19.907 25.051 19.631 24.359 19.887 23.762 C 20.144 23.164 20.836 22.888 21.434 23.145 L 24.213 24.337 L 25.82 22.915 C 26.764 22.078 28.207 22.165 29.043 23.109 Z M 10.588 18.424 C 11.238 18.424 11.765 18.951 11.765 19.6 C 11.765 20.249 11.238 20.776 10.588 20.776 L 4.314 20.776 C 3.664 20.776 3.137 20.249 3.137 19.6 C 3.137 18.951 3.664 18.424 4.314 18.424 L 10.588 18.424 Z M 16.863 18.424 C 17.512 18.424 18.039 18.951 18.039 19.6 C 18.039 20.249 17.512 20.776 16.863 20.776 L 14.51 20.776 C 13.86 20.776 13.333 20.249 13.333 19.6 C 13.333 18.951 13.86 18.424 14.51 18.424 L 16.863 18.424 Z M 37.255 10.98 L 2.745 10.98 C 2.529 10.98 2.353 11.156 2.353 11.373 L 2.353 12.549 L 37.647 12.549 L 37.647 11.373 C 37.647 11.156 37.471 10.98 37.255 10.98 Z"
          fill
        />
      </svg>
    | Barnaby
    | DistanceSelling
    | EuskoCash
    | EuskoDigital
    | Lydia
    | LesHabitues
    | Paypal
    | TicketRestaurant
    | WinoPay =>
      <svg width="25px" height="25px" viewBox="0 0 40 40">
        <path
          d="M 29.583 15.833 C 35.336 15.833 40 20.497 40 26.25 C 40 32.003 35.336 36.667 29.583 36.667 C 23.83 36.667 19.167 32.003 19.167 26.25 C 19.167 20.497 23.83 15.833 29.583 15.833 Z M 29.583 18.333 C 25.211 18.333 21.667 21.878 21.667 26.25 C 21.667 30.622 25.211 34.167 29.583 34.167 C 33.956 34.167 37.5 30.622 37.5 26.25 C 37.5 21.878 33.956 18.333 29.583 18.333 Z M 3.333 7.083 L 3.333 9.583 C 2.873 9.583 2.5 9.956 2.5 10.417 L 2.5 25.417 C 2.5 26.567 3.433 27.5 4.583 27.5 L 17.145 27.502 C 17.231 28.364 17.404 29.2 17.656 30.002 L 4.583 30 C 2.052 30 0 27.948 0 25.417 L 0 10.417 C 0 8.576 1.492 7.083 3.333 7.083 Z M 30.417 3.333 C 32.027 3.333 33.333 4.639 33.333 6.25 L 33.335 14.323 C 32.533 14.071 31.697 13.897 30.834 13.812 L 30.833 12.5 L 7.5 12.5 L 7.5 22.083 C 7.5 22.313 7.687 22.5 7.917 22.5 L 17.656 22.5 C 17.404 23.301 17.231 24.137 17.145 24.999 L 7.917 25 C 6.306 25 5 23.694 5 22.083 L 5 6.25 C 5 4.639 6.306 3.333 7.917 3.333 L 30.417 3.333 Z M 30.417 5.833 L 7.917 5.833 C 7.687 5.833 7.5 6.02 7.5 6.25 L 7.5 10 L 30.833 10 L 30.833 6.25 C 30.833 6.02 30.647 5.833 30.417 5.833 Z"
          fill
        />
      </svg>
    }
  }
}

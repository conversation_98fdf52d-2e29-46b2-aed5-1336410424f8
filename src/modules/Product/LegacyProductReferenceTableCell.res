open Style

type badge = {
  variation: Badge.variation,
  text: string,
}

type variation = [#primary | #secondary]

@react.component
let make = (
  ~id=?,
  ~cku=?,
  ~variation: variation=#primary,
  ~badge=?,
  ~name,
  ~pastilleColor=?,
  ~description,
  ~disabled=false,
  ~errorMessage=?,
  ~openNewTab=false,
) => {
  let redirectPageRoute = switch (cku, id) {
  | (Some(cku), _) => Catalog->LegacyRouter.routeToPathname ++ "/" ++ cku
  | (_, Some(id)) => Catalog->LegacyRouter.routeToPathname ++ "/redirect/" ++ id
  | _ => Catalog->LegacyRouter.routeToPathname
  }

  <View style={style(~width=100.->pct, ())}>
    <Box spaceY=#xsmall grow=true>
      <Stack space=#none>
        {switch variation {
        | #primary =>
          <span>
            {switch pastilleColor {
            | Some(color) =>
              <View
                style={merge([
                  style(~top=-2.->dp, ~paddingRight=5.->dp, ()),
                  unsafeCss({"display": "inline-flex"}),
                ])}>
                <Pastille color />
              </View>
            | None => React.null
            }}
            <TextLink text=name to=Route(redirectPageRoute) openNewTab />
          </span>
        | #secondary =>
          <InlineText>
            <TextStyle
              size=#xsmall
              lineHeight=#large
              weight={disabled ? #regular : #medium}
              variation={disabled ? #subdued : #neutral}>
              {(name ++ "  ")->React.string}
            </TextStyle>
            {switch badge {
            | Some({variation, text}) => <Badge variation size=#small> {text->React.string} </Badge>
            | None => React.null
            }}
          </InlineText>
        }}
        <InlineText linespace=#xxsmall maxLines=2>
          {if !disabled {
            <TextStyle size=#tiny lineHeight=#small variation=#normal>
              {description->React.string}
            </TextStyle>
          } else {
            React.null
          }}
        </InlineText>
        {switch errorMessage {
        | Some(message) =>
          <Box spaceTop=#xsmall>
            <TextStyle size=#tiny variation=#negative> {message->React.string} </TextStyle>
          </Box>
        | None => React.null
        }}
      </Stack>
    </Box>
  </View>
}

let make = React.memo(make)

import {
  GestureResponderEvent,
  FlexStyle,
  TextStyle,
  ViewStyle,
  ImageStyle,
  ImageSourcePropType,
  ImageURISource,
} from 'react-native'

export type Color_t = string
export type Event_pressEvent = GestureResponderEvent
export type FontVariant_t = TextStyle['fontVariant']
export type Style_backfaceVisibility = ViewStyle['backfaceVisibility']
export type Style_borderStyle = ViewStyle['borderStyle']
export type Style_flexWrap = FlexStyle['flexWrap']
export type Style_display = FlexStyle['display']
export type Style_fontStyle = TextStyle['fontStyle']
export type Style_margin = ViewStyle['margin']
export type Style_offset = ViewStyle['shadowOffset']
export type Style_overflow = ViewStyle['overflow']
export type Style_position = ViewStyle['position']
export type Style_resizeMode = ImageStyle['resizeMode']
export type Style_size = number | undefined
// TODO - the difference between ViewStyle and TextStyle does not exist today,
// so there is a risk of errors at runtime.
// Should fork rescript-react-native to correct this issue later.
export type Style_t = ViewStyle | TextStyle | ImageStyle
export type Style_textAlignVertical = TextStyle['textAlignVertical']
export type Style_textAlign = TextStyle['textAlign']
export type Style_textDecorationStyle = TextStyle['textDecorationStyle']
export type Style_textTransform = TextStyle['textTransform']
export type Style_transform = TextStyle['transform']
export type Style_writingDirection = TextStyle['writingDirection']
export type Image_Source_t = ImageSourcePropType
export type Image_uriSource = ImageURISource

// NOTE - defines hrefAttrs: https://necolas.github.io/react-native-web/docs/text/#props
export type Web_hrefAttrs = {
  download?: boolean
  rel?: string
  target?: string
}

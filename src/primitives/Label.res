let style =
  ReactDOM.Style.make(
    ~color=Colors.neutralColor50,
    ~fontSize=`${FontSizes.xsmall->Float.toString}px`,
    ~fontFamily=#LibreFranklin->FontFaces.fontFamilyFromFontName,
    ~fontWeight="500",
    ~marginBottom="7px",
    ~cursor="inherit",
    (),
  )->ReactDOM.Style.unsafeAddStyle({"textWrap": "nowrap"})

@react.component
let make = (~text="", ~ariaProps=?) => {
  <ReactAria.Spread props=ariaProps>
    <label style> {text->React.string} </label>
  </ReactAria.Spread>
}

let make = React.memo(make)

// TODO - To rename <Pressable />

let makeStyle = (~wrap, ~disabled) =>
  ReactDOM.Style.make(
    ~display=wrap ? "block" : "inline-flex",
    ~position="relative",
    ~cursor=disabled ? "default" : "pointer",
    ~userSelect="text",
    (),
  )

@react.component
let make = React.forwardRef((
  ~children,
  ~ariaProps=?,
  ~disabled=?,
  ~excludeFromTabOrder=?,
  ~wrap=true,
  ~style=?,
  ~onPress,
  ref,
) => {
  let disabled =
    disabled
    ->Option.orElse(ariaProps->Option.flatMap(props => props.ReactAria.Button.disabled))
    ->Option.getWithDefault(false)

  let defaultProps = {
    ReactAria.Button.elementType: #div,
    disabled,
    ?excludeFromTabOrder,
    onPress,
  }
  let props =
    ariaProps
    ->Option.map(props => ReactAria.mergeProps2(props, defaultProps))
    ->Option.getWithDefault(defaultProps)

  let {buttonProps} = ReactAria.Button.use(~props, ())

  let style = ReactDOM.Style.combine(
    makeStyle(~wrap, ~disabled),
    style->Option.getWithDefault(ReactDOM.Style.make()),
  )

  <ReactAria.Spread props=buttonProps>
    <div style ref=?{ref->Js.Nullable.toOption->Option.map(ReactDOM.Ref.domRef)}> children </div>
  </ReactAria.Spread>
})

let make = React.memo(make)

React.setDisplayName(make, "Touchable")

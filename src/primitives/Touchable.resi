let makeProps: (
  ~children: React.element,
  ~ariaProps: ReactAria.Button.props=?,
  ~disabled: bool=?,
  ~excludeFromTabOrder: bool=?,
  ~wrap: bool=?,
  ~style: ReactDOM.Style.t=?,
  ~onPress: ReactAria.Button.pressEvent => unit,
  ~key: string=?,
  ~ref: 'ref=?,
  unit,
) => {
  "children": React.element,
  "ariaProps": option<ReactAria.Button.props>,
  "disabled": option<bool>,
  "excludeFromTabOrder": option<bool>,
  "wrap": option<bool>,
  "style": option<ReactDOM.Style.t>,
  "onPress": ReactAria.Button.pressEvent => unit,
}

@genType
let make: {
  "children": React.element,
  "ariaProps": option<ReactAria.Button.props>,
  "disabled": option<bool>,
  "excludeFromTabOrder": option<bool>,
  "wrap": option<bool>,
  "style": option<ReactDOM.Style.t>,
  "onPress": ReactAria.Button.pressEvent => unit,
} => React.element

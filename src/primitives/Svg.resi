module Circle: {
  @react.component
  let make: (~cx: string=?, ~cy: string=?, ~r: string=?, ~fill: Style.Color.t=?) => React.element
}

module Path: {
  @react.component
  let make: (
    ~d: string=?,
    ~fill: Style.Color.t=?,
    ~transform: string=?,
    ~fillRule: string=?,
    ~filter: string=?,
    ~stroke: Style.Color.t=?,
    ~strokeWidth: string=?,
    ~strokeDasharray: string=?,
  ) => React.element
}

@react.component
let make: (
  ~children: React.element,
  ~role: string=?,
  ~width: string=?,
  ~height: string=?,
  ~viewBox: string=?,
  ~fill: Style.Color.t=?,
  ~stroke: Style.Color.t=?,
  ~style: ReactDOM.style=?,
  ~className: string=?,
) => React.element

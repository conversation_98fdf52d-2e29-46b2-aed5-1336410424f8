open! Big.Operators
open Accounting__Types

let computeTotalAmountExcludingTransportFee = (
  tax: tax,
  ~product: product,
  ~decimalPrecision,
  ~excludingGlobalDiscount=false,
  (),
) =>
  switch product {
  | Unit({totalAmountExcludingTaxes, totalAmountExcludingGlobalDiscounts, fees})
  | Bulk({totalAmountExcludingTaxes, totalAmountExcludingGlobalDiscounts, fees}, _) =>
    let productQuantity = switch product {
    | Unit(product) => product.quantity->Int.toFloat->Big.fromFloat
    | Bulk(product, _) => product.quantity
    }
    let totalAmountExcludingTransportFeeAndTaxes = {
      let totalTransportFeeAmount = {
        let transportFeeAmount =
          fees
          ->Array.getBy(fee => fee.kind === Transport)
          ->Option.map(transportFee => transportFee.amount->Big.fromFloat)
        transportFeeAmount->Option.getWithDefault(Big.fromFloat(0.)) *. productQuantity
      }
      let totalAmount = excludingGlobalDiscount
        ? totalAmountExcludingGlobalDiscounts
        : totalAmountExcludingTaxes
      totalAmount->Option.getExn -. totalTransportFeeAmount
    }
    Some(
      (totalAmountExcludingTransportFeeAndTaxes *. (tax.rate->Big.fromFloat /. 100.->Big.fromFloat))
        ->Big.round(decimalPrecision),
    )
  }

let computeTransportFeeTaxAmount = (~product: product, ~decimalPrecision, ~standardTaxRate) =>
  switch product {
  | Unit({fees})
  | Bulk({fees}, _) =>
    let productQuantity = switch product {
    | Unit(product) => product.quantity->Int.toFloat->Big.fromFloat
    | Bulk(product, _) => product.quantity
    }
    let totalTransportFeeAmount = {
      let transportFeeAmount =
        fees
        ->Array.getBy(fee => fee.kind === Transport)
        ->Option.map(transportFee => transportFee.amount->Big.fromFloat)
      transportFeeAmount->Option.getWithDefault(Big.fromFloat(0.)) *. productQuantity
    }
    Some(
      (totalTransportFeeAmount *. (standardTaxRate->Big.fromFloat /. 100.->Big.fromFloat))
        ->Big.round(decimalPrecision),
    )
  }

// Computes the total amount excluding taxes
let computeTotalAmountExcludingTaxes = (product: product, ~cart: cart) =>
  switch product {
  | Unit({totalGlobalDiscounts, totalAmountExcludingGlobalDiscounts})
  | Bulk({totalGlobalDiscounts, totalAmountExcludingGlobalDiscounts}, _) =>
    Some(
      (totalAmountExcludingGlobalDiscounts->Option.getExn -. totalGlobalDiscounts->Option.getExn)
        ->Big.round(cart.decimalPrecision),
    )
  }

// Computes the total amount including taxes
let computeTotalAmountIncludingTaxes = (product: product, ~cart: cart) =>
  switch product {
  | Unit({totalTaxes, totalAmountExcludingTaxes})
  | Bulk({totalTaxes, totalAmountExcludingTaxes}, _) =>
    Some(
      (totalAmountExcludingTaxes->Option.getExn +. totalTaxes->Option.getExn)
        ->Big.round(cart.decimalPrecision),
    )
  }

// Computes the total amount of all product taxes
let computeTotalAmount = (product: product, ~cart: cart) =>
  switch cart {
  | {taxesFree: false} =>
    switch product {
    | Unit({taxes})
    | Bulk({taxes}, _) =>
      Some(
        taxes
        ->Option.getExn
        ->Array.reduce(0.->Big.fromFloat, (acc, tax) => acc +. tax.amount->Option.getExn)
        ->Big.round(cart.decimalPrecision),
      )
    }
  | {taxesFree: true} => Some(0.->Big.fromFloat)
  }

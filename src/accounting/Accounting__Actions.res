open Accounting__Types

type productQuantity = UnitQuantity(int) | BulkQuantity(float)

type action =
  // Adds a product to the cart
  | ProductAdded(productInput)
  // Adds a bunch of products to the cart
  | BatchProductAdded(array<productInput>)
  // Removes a product from the cart by
  // providing its key(.id|.stockKeepingUnit)
  | ProductRemoved(string)
  // Updates the quantity of a
  // product already contained in the cart
  | ProductQuantityUpdated(string, productQuantity)
  // Updates the expected quantity of a
  // product already contained in the cart
  | ProductExpectedQuantityUpdated(string, productQuantity)
  // Use this when you wish updating many
  // products's expected quantity. Just provide
  // an array of (.id, .quantity)
  | BatchProductExpectedQuantityUpdated(array<(string, productQuantity)>)
  // Updates the unit price of a
  // product in the cart
  | ProductUnitPriceUpdated(string, float)
  // Renames a product in the cart
  | ProductNameUpdated(string, string)
  // Updates the description of a product in the cart
  | ProductDescriptionUpdated(string, string)
  // Its generates a fee for the product
  // which you provide a key and merges it to the
  // product's fees
  | ProductFeeAdded(string)
  // Removes a fee from the product
  // First parameter is the product's key
  | ProductFeeRemoved(string, string)
  // Updates a fee
  // First parameter is the product's key
  | ProductFeeUpdated(string, string, fee)
  // Given a fee as parameter, this action replicates it
  // to all the products in the cart. The replication
  // consist in adding or updating
  | ProductFeeReplicated(fee)
  // Allocate by unite the amount (float)
  // to all products in the cart, replacing existing fees
  // by generating a fee of `Transport` feeKind.
  | ProductsFeePerUnitAllocated(float)
  // Allocate proportionally by price the amount (float)
  // to all products in the cart, replacing existing fees
  // by generating a fee of `Transport` feeKind.
  | ProductsFeeProratedByPriceAllocated(float)
  // Adds a discount to the identified product,
  // starting from a discountInput as parameter
  | ProductDiscountAdded(string, discountInput)
  // Removes a discount from the product
  | ProductDiscountRemoved(string, string)
  // Updates a discount in the product
  | ProductDiscountUpdated(string, string, discount)
  // Adds a global discount to the cart
  // This global discount will affect
  // each product's total amounts
  | GlobalDiscountAdded(discountInput)
  // Removes a global discount, identified by the .id
  | GlobalDiscountRemoved(string)
  // Updates a global discount with
  // the new object passed as parameter
  | GlobalDiscountUpdated(string, discount)
  | StandardTaxRateUpdated(float)
  | TaxesFreeToggleRequested

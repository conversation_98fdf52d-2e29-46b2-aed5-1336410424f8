open! Big.Operators
open Accounting__Types
open Accounting__Exception

module Utils = Accounting__Utils

// Validation strategy type
type strategy =
  | Relax // With this strategy we ignore specially raising error when product.quantity is zeroish
  | Strict // This strategy should be used when we want to restrict product.quantity to be > 0

module Fee = {
  // Validates a fee input
  let validateInput = (input: feeInput, ()) =>
    input.amount < 0. ? Error(NotPermitted("Fee amount must be >= 0")) : Ok()

  // Validates a fee
  let validate = (input: fee, ()) =>
    input.amount < 0. ? Error(NotPermitted("Fee amount must be >= 0")) : Ok()

  // Given a product and a feeKind
  // check if the feeKind can be added to the product.
  // Remember we cannot have more than one
  // feeKind in the same product
  let feeKindCanBeAddedTo = (~product: product, feeKind: feeKind) =>
    switch product {
    | Unit({fees}) | Bulk({fees}, _) =>
      fees->Array.keep(fee => feeKind === fee.kind)->Array.length === 0
    }
}

module Discount = {
  // Validates a discount input
  let validateInput = (input: discountInput, ()) =>
    switch input.kind {
    | Percent if input.value > 100. || input.value < 0. =>
      Error(NotPermitted("Discount percent value must be >= 0 and <= 100"))
    | Currency if input.value < 0. => Error(NotPermitted("Discount currency value must be >= 0"))
    | Free if input.quantity < 0 => Error(NotPermitted("Discount quantity must be >= 0"))
    | _ => Ok()
    }

  // Validates a discount
  let validate = (input: discount, ()) =>
    switch input.kind {
    | Percent if input.value > 100. || input.value < 0. =>
      Error(NotPermitted("Discount percent value must be >= 0 and <= 100"))
    | Currency if input.value < 0. => Error(NotPermitted("Discount currency value must be >= 0"))
    | Free if input.quantity->Big.toFloat < 0. =>
      Error(NotPermitted("Discount quantity must be >= 0"))
    | _ => Ok()
    }

  // Checks if a discount can be added to a certain product
  // TODO - factorize with localDiscountCanBeUpdatedIn
  let localDiscountCanBeAddedTo = (~product: product, discount: discount) => {
    let discountValueChecked = switch product {
    | Unit({totalPrice}) | Bulk({totalPrice}, _) =>
      switch discount.kind {
      | Percent => discount.value >= 0. && discount.value <= 100.
      | Currency => discount.value >= 0. && discount.value <= totalPrice->Option.getExn->Big.toFloat
      | Free => true
      }
    }

    switch product {
    | Unit({discounts, quantity}) =>
      discounts->Array.length === 0 &&
        (discount.quantity->Big.toFloat >= 0. &&
        (discount.quantity->Big.toFloat <= quantity->Int.toFloat && discountValueChecked))
    | Bulk({discounts, quantity}, _) =>
      discounts->Array.length === 0 &&
        (discount.quantity->Big.toFloat >= 0. &&
        (discount.quantity->Big.toFloat <= quantity->Big.toFloat && discountValueChecked))
    }
  }

  // Checks if a discount can be updated in a certain product
  // TODO - factorize with localDiscountCanBeAddedTo
  let localDiscountCanBeUpdatedIn = (~product: product, discount: discount) => {
    let discountValueChecked = switch product {
    | Unit({totalPrice}) | Bulk({totalPrice}, _) =>
      switch discount.kind {
      | Percent => discount.value >= 0. && discount.value <= 100.
      | Currency => discount.value >= 0. && discount.value <= totalPrice->Option.getExn->Big.toFloat
      | Free => true
      }
    }

    switch product {
    | Unit({discounts, quantity}) =>
      discounts->Array.length === 1 &&
        (discount.quantity->Big.toFloat >= 0. &&
        (discount.quantity->Big.toFloat <= quantity->Int.toFloat && discountValueChecked))
    | Bulk({discounts, quantity}, _) =>
      discounts->Array.length === 1 &&
        (discount.quantity->Big.toFloat >= 0. &&
        (discount.quantity->Big.toFloat <= quantity->Big.toFloat && discountValueChecked))
    }
  }

  // Checks if a global discount can be added to the cart
  let globalDiscountCanBeAddedTo = (~cart: cart, discount: discount) =>
    // What if there is no products in the cart
    cart.products->Array.length > 0 &&
      // And according to the kind
      switch discount.kind {
      // If the discount kind is Currency always be sure that its value
      // is not greater than the total amount of the cart
      | Currency =>
        discount.value >= 0. &&
          discount.value
          ->Big.fromFloat
          ->Big.lte(cart.totalAmountExcludingGlobalDiscounts->Option.getExn)

      // It the discount kind is Percent, be sure itsn't greater than 100
      | Percent => discount.value >= 0. && discount.value <= 100.

      // We cannot apply a Free discount globally 😕
      | Free => false
      }
}

module Product = {
  // come with more than one fee with same fee kind to corrupt the final product
  // Validates a product input shape
  let validateInput = (input: productInput, ~strategy: strategy=Relax, ()) => {
    let errors = ref([])

    switch input {
    | Unit({product: {tax, unitPrice, discounts, fees, name, description}})
    | Bulk({product: {tax, unitPrice, discounts, fees, name, description}, _}) =>
      if tax < 0. {
        errors := [NotPermitted("Product tax must be >= 0")]->Array.concat(errors.contents)
      }
      if unitPrice < 0. {
        errors := [NotPermitted("Product unitPrice must >= 0")]->Array.concat(errors.contents)
      }
      if discounts->Array.length > 1 {
        errors :=
          [NotPermitted("Product must not have more than one discounts")]->Array.concat(
            errors.contents,
          )
      }
      discounts->Array.forEach(discountInput =>
        switch discountInput->Discount.validateInput() {
        | Ok() => ()
        | Error(error) => errors := [error]->Array.concat(errors.contents)
        }
      )
      Utils.getAllFeeKinds()->Array.forEach(feeKind =>
        if fees->Array.keep(fee => fee.kind === feeKind)->Array.length > 1 {
          errors :=
            [
              NotPermitted("Product should not have more than one fee with the same fee kind"),
            ]->Array.concat(errors.contents)
        }
      )
      fees->Array.forEach(feeInput =>
        switch feeInput->Fee.validateInput() {
        | Ok() => ()
        | Error(error) => errors := [error]->Array.concat(errors.contents)
        }
      )
      if name->String.trim->String.length === 0 {
        errors := [NotPermitted("Product name cannot be empty")]->Array.concat(errors.contents)
      }
      if description->String.trim->String.length === 0 {
        errors :=
          [NotPermitted("Product description cannot be empty")]->Array.concat(errors.contents)
      }
    }

    switch input {
    | Unit({product: {quantity, packaging}}) =>
      if quantity < 0 {
        errors := [NotPermitted("Product quantity must be >= 0")]->Array.concat(errors.contents)
      }
      if quantity <= 0 && strategy === Strict {
        errors :=
          [NotPermitted("Strict mode - Product quantity must be > 0")]->Array.concat(
            errors.contents,
          )
      }
      switch packaging {
      | Some(value) if value <= 0 =>
        errors :=
          [NotPermitted("Product quantity packaging must be > 0")]->Array.concat(errors.contents)
      | _ => ()
      }
    | Bulk({product: {quantity, packaging}, _}) =>
      if quantity < 0. {
        errors := [NotPermitted("Product quantity must be >= 0")]->Array.concat(errors.contents)
      }
      if quantity <= 0. && strategy === Strict {
        errors :=
          [NotPermitted("Strict mode - Product quantity must be > 0")]->Array.concat(
            errors.contents,
          )
      }
      switch packaging {
      | Some(value) if value <= 0. =>
        errors :=
          [NotPermitted("Product quantity packaging must be > 0")]->Array.concat(errors.contents)
      | _ => ()
      }
    }

    switch errors.contents {
    | [] => Ok()
    | errors => Error(errors->Array.getExn(0))
    }
  }

  // Validates a product shape
  let validate = (product: product, ~strategy: strategy=Relax, ()) => {
    let errors = ref([])

    switch product {
    | Unit({taxes, unitPrice, discounts, fees, name, description})
    | Bulk({taxes, unitPrice, discounts, fees, name, description}, _) =>
      let taxes = taxes->Option.getExn

      if unitPrice < 0. {
        errors := [NotPermitted("Product unitPrice must >= 0")]->Array.concat(errors.contents)
      }
      if taxes->Array.length > 0 && (taxes->Array.getExn(0)).rate < 0. {
        errors := [NotPermitted("Product tax must be >= 0")]->Array.concat(errors.contents)
      }
      if discounts->Array.length > 1 {
        errors :=
          [NotPermitted("Product must not have more than one discounts")]->Array.concat(
            errors.contents,
          )
      }
      discounts->Array.forEach(discount =>
        switch discount->Discount.validate() {
        | Ok() => ()
        | Error(error) => errors := [error]->Array.concat(errors.contents)
        }
      )
      fees->Array.forEach(fee =>
        switch fee->Fee.validate() {
        | Ok() => ()
        | Error(error) => errors := [error]->Array.concat(errors.contents)
        }
      )
      Utils.getAllFeeKinds()->Array.forEach(feeKind =>
        if fees->Array.keep(fee => fee.kind === feeKind)->Array.length > 1 {
          errors :=
            [
              NotPermitted("Product should not have more than one fee with the same fee kind"),
            ]->Array.concat(errors.contents)
        }
      )
      if name->String.trim->String.length === 0 {
        errors := [NotPermitted("Product name cannot be empty")]->Array.concat(errors.contents)
      }
      if description->String.trim->String.length === 0 {
        errors :=
          [NotPermitted("Product description cannot be empty")]->Array.concat(errors.contents)
      }
    }

    switch product {
    | Unit({quantity, expectedQuantity, packaging}) =>
      switch packaging {
      | Some(value) =>
        if value <= 0 {
          errors :=
            [NotPermitted("Product quantity packaging must be > 0")]->Array.concat(errors.contents)
        }
      | None => ()
      }
      if quantity < 0 {
        errors := [NotPermitted("Product quantity must be >= 0")]->Array.concat(errors.contents)
      }
      if quantity === 0 && strategy === Strict {
        errors :=
          [NotPermitted("Strict mode - Product quantity must be > 0")]->Array.concat(
            errors.contents,
          )
      }
      if expectedQuantity < 0 {
        errors :=
          [NotPermitted("Product expected quantity must be >= 0")]->Array.concat(errors.contents)
      }
      if expectedQuantity === 0 && strategy === Strict {
        errors :=
          [NotPermitted("Strict mode - Product expected quantity must be > 0")]->Array.concat(
            errors.contents,
          )
      }
    | Bulk({quantity, expectedQuantity, packaging}, _) =>
      switch packaging {
      | Some(value) =>
        if value->Big.toFloat <= 0. {
          errors :=
            [NotPermitted("Product quantity packaging must be > 0")]->Array.concat(errors.contents)
        }
      | None => ()
      }
      if quantity->Big.toFloat < 0. {
        errors := [NotPermitted("Product quantity must be >= 0")]->Array.concat(errors.contents)
      }
      if quantity->Big.toFloat === 0. && strategy === Strict {
        errors :=
          [NotPermitted("Strict mode - Product quantity must be > 0")]->Array.concat(
            errors.contents,
          )
      }
      if expectedQuantity->Big.toFloat < 0. {
        errors :=
          [NotPermitted("Product expected quantity must be >= 0")]->Array.concat(errors.contents)
      }
      if expectedQuantity->Big.toFloat === 0. && strategy === Strict {
        errors :=
          [NotPermitted("Strict mode - Product expected quantity must be > 0")]->Array.concat(
            errors.contents,
          )
      }
    }

    switch errors.contents {
    | [] => Ok()
    | errors => Error(errors->Array.getExn(0))
    }
  }
}

module Cart = {
  // Validates a cart input
  let validateInput = (input: cartInput, ()) => {
    // Validate product inputs
    let productsErrors = input.products->Array.reduce(list{}, (acc, productInput) =>
      switch productInput->Product.validateInput() {
      | Ok() => acc
      | Error(error) => list{error, ...acc}->List.reverse
      }
    )

    // Validates discount inputs
    let discountsErrors =
      input.discounts
      ->Array.reduce(list{}, (acc, discountInput) =>
        switch discountInput->Discount.validateInput() {
        | Ok() => acc
        | Error(error) => list{error, ...acc}
        }
      )
      ->List.concat(
        input.discounts->Array.length > 1
          ? list{NotPermitted("Cart must not have more than one discount")}
          : list{},
      )

    let errors = list{productsErrors, discountsErrors}
    switch errors->List.flatten {
    | list{} => Ok()
    | list{error, ..._} => Error(error)
    }
  }
}

// This is the entry point of the module
// Below are listed the modules and functions we want to externalize
// Calling a module by "Accounting__xxx" is prohibited 👎
module Actions = Accounting__Actions
module Computer = Accounting__Computer
module Formatter = Accounting__Formatter
module Maker = Accounting__Maker
module Reducer = Accounting__Reducer
module Serializer = Accounting__Serializer
module Types = Accounting__Types

@genType.as("Cart")
type cart = Types.cart

@genType.as("CartProduct")
type product = Types.product

@genType.as("CartDiscount")
type discount = Types.discount

@genType.as("CartProductFee")
type fee = Types.fee

@genType.as("Tax")
type tax = Types.tax

open! Accounting__Computer
@genType let compute = make

open! Accounting__Serializer
@genType let serialize = serialize
@genType let deserialize = deserialize

open! Accounting__Formatter.Cart
@genType let format = make

open! Accounting__Formatter
@genType let formatAmount = formatAmount
@genType let formatAmountFromString = formatAmountFromString
@genType let formatAmountFromBig = formatAmountFromBig

open! Accounting__Maker.Cart
@genType let make = make

open! Accounting__Utils
@genType let fromRawProductQuantity = fromRawProductQuantity
@genType let toRawProductQuantity = toRawProductQuantity
@genType let isBulk = isBulk

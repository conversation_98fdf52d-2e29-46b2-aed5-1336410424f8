open! Big.Operators
open Accounting__Types

module ComputerDiscount = Accounting__Computer__Discount

// Module responsible of comparing and mapping
// different feeKinds and setting their values
// It helps us group all fees by the feeKind: totalAmountOfFees
module FeeKindMap = Id.MakeComparable({
  type t = feeKind
  let cmp = compare
})

// Module responsible of comparing and mapping
// different taxes and setting their values
// It helps us group all taxes by the tax.rate: totalAmountOfTaxes
module TaxMap = Id.MakeComparable({
  type t = float
  let cmp = compare
})

// Given a cart, loop over all products and
// accumulate their fees's amounts grouping them by the fee's kind
// The fees are grouped by feeKind and are shaped with cart.(type feesTotal)
// So while we are looping through the products fees
//    we accumulate each product.fee's amount
//    and generate a feesTotal with the result
//    adds that result to the map - grouped by feeKind
let computeFees = cart => {
  let fees: array<feesTotal> =
    cart.products
    // Loop over all products and flat them into one array
    ->Array.reduce([], (acc, product) =>
      acc->Array.concat(
        switch product {
        | Unit({fees})
        | Bulk({fees}, _) => fees
        },
      )
    )
    // Then reduce obtained array,
    // a map setted as the initial value
    ->Array.reduce(Map.make(~id=module(FeeKindMap)), (// Initial value of the map
    acc: Belt.Map.t<FeeKindMap.t, Accounting__Types.feesTotal, FeeKindMap.identity>,
    fee) =>
      switch acc->Map.get(fee.kind) {
      // If there is a fee in the map with the kind
      | Some(item) =>
        // Accumulate the present amount in
        // the map with the new one comming up
        let amount = fee.totalAmount->Option.getExn +. item.amount

        // And set the map with the new computed amount
        acc->Map.set(item.kind, {...item, amount})
      // If not generate a new one
      | None =>
        // set the correct amount to it
        let amount = fee.totalAmount->Option.getExn

        // and add it to the map
        acc->Map.set(fee.kind, {kind: fee.kind, amount, formattedAmount: None})
      }
    )
    // Finally, convert the map to an array
    ->Map.valuesToArray

  // Return the final cart with
  // all fees summed up and grouped by kind
  {...cart, fees: Some(fees)}
}

// Computes the total amounts of products excluding the total of the fees
let computeTotalAmountOfGoods = cart => {
  let totalAmountOfGoods =
    cart.products
    ->Array.reduce(0.->Big.fromFloat, (acc, product) =>
      switch product {
      | Unit({totalFees, totalAmountExcludingTaxes})
      | Bulk({totalFees, totalAmountExcludingTaxes}, _) =>
        acc +. totalAmountExcludingTaxes->Option.getExn -. totalFees->Option.getExn
      }
    )
    ->Big.round(cart.decimalPrecision)

  {...cart, totalAmountOfGoods: Some(totalAmountOfGoods)}
}

// Accumulate products taxes amounts and group the results by tax's rate
let computeTaxes = cart =>
  switch cart.taxesFree {
  | false =>
    // Generates a cart tax from data coming from a product tax
    let generateCartTax = (productTax: tax) => {
      rate: productTax.rate,
      amount: productTax.amount,
      formattedAmount: None,
    }

    let taxes =
      cart.products
      // Flat all product's taxes
      // to one array and converting them to cart tax
      ->Array.reduce([], (acc, product) =>
        acc->Array.concat(
          switch product {
          | Unit({taxes})
          | Bulk({taxes}, _) =>
            taxes->Option.getExn->Array.map(generateCartTax)
          },
        )
      )
      // Loop over all generated cart taxes
      // and group them by their rate
      ->Array.reduce(Map.make(~id=module(TaxMap)), (acc, cartTax) =>
        switch acc->Map.get(cartTax.rate) {
        // If there was a tax at this tax.rate in the map
        // Gets it and accumulate its amount with the current cartTax
        | Some(item: tax) =>
          let amount = Some(
            (item.amount->Option.getExn +. cartTax.amount->Option.getExn)
              ->Big.round(cart.decimalPrecision),
          )
          acc->Map.set(
            item.rate,
            {
              ...cartTax,
              amount,
            },
          )
        // If there was not a cart tax in the map at this
        // cartTax.rate, set the map with the current cartTax
        // using its rate of couse
        | None => acc->Map.set(cartTax.rate, {...cartTax, formattedAmount: None})
        }
      )
      // Converts values in map to array so
      ->Map.valuesToArray

    let totalTaxes =
      taxes
      ->Array.reduce(0.->Big.fromFloat, (acc, tax) => acc +. tax.amount->Option.getExn)
      ->Big.round(cart.decimalPrecision)

    // Returns the cart w/ products.taxes processed and map converted to an array
    {...cart, taxes: Some(taxes), totalTaxes: Some(totalTaxes)}
  | true => {
      ...cart,
      taxes: None,
      totalTaxes: None,
    }
  }

// Compute all total amounts of each products
let computeTotalAmounts = cart => {
  // Array.reducing cart.products to accumulate their
  // totalAmounts(Excluding|Including)Taxes, starting from 0.->Big.fromFloat
  let (totalAmountExcludingTaxes, totalAmountIncludingTaxes) = cart.products->Array.reduce(
    (0.->Big.fromFloat, 0.->Big.fromFloat),
    (acc, product) =>
      switch product {
      | Unit({totalAmountExcludingTaxes, totalAmountIncludingTaxes})
      | Bulk({totalAmountExcludingTaxes, totalAmountIncludingTaxes}, _) => (
          // Here we're accumulating the totalAmounts (without ref(😝))
          (acc->fst +. totalAmountExcludingTaxes->Option.getExn)->Big.round(cart.decimalPrecision),
          (acc->snd +. totalAmountIncludingTaxes->Option.getExn)->Big.round(cart.decimalPrecision),
        )
      },
  )

  {
    ...cart,
    // Recalculated fields
    totalAmountExcludingTaxes: Some(totalAmountExcludingTaxes),
    totalAmountIncludingTaxes: Some(totalAmountIncludingTaxes),
  }
}

// Compute global discounts
// --- Just formatting them
let computeGlobalDiscounts = cart => {
  let computeGlobalDiscountWarnings = (discount: discount) =>
    if discount.value > cart.totalAmountExcludingGlobalDiscounts->Option.getExn->Big.toFloat {
      [AmountGreaterThanTotalPrice]
    } else {
      []
    }

  // Computes values of a single global discount
  let computeGlobalDiscount = (discount: discount) => {
    let amount = discount->ComputerDiscount.Global.compute(~cart)
    let warnings = {...discount, amount}->computeGlobalDiscountWarnings
    let newDiscount = {...discount, amount, warnings}

    {...newDiscount, formattedAmount: None, formattedValue: None}
  }

  // Compute each discount contained in the cart
  // -- it's just one in this version
  {...cart, discounts: cart.discounts->Array.map(computeGlobalDiscount)}
}

// Compute total amount of product
// excluding global discounts
let computeTotalAmountExcludingGlobalDiscounts = cart => {
  let totalAmountExcludingGlobalDiscounts = cart.products->Array.reduce(0.->Big.fromFloat, (
    acc,
    product,
  ) =>
    switch product {
    | Unit({totalAmountExcludingGlobalDiscounts})
    | Bulk({totalAmountExcludingGlobalDiscounts}, _) =>
      acc +. totalAmountExcludingGlobalDiscounts->Option.getExn
    }
  )

  {
    ...cart,
    totalAmountExcludingGlobalDiscounts: Some(totalAmountExcludingGlobalDiscounts),
    formattedTotalAmountExcludingGlobalDiscounts: None,
  }
}

// Computes the amount of the global discount based
// on the total amount excluding global discount in cart
let computeGlobalDiscountsAmount = cart => {
  let computeGlobalDiscountAmount = (discount: discount) => {
    let amount = discount->ComputerDiscount.Global.compute(~cart)
    let newDiscount = {...discount, amount}

    {...newDiscount, formattedAmount: None, formattedValue: None}
  }

  {
    ...cart,
    discounts: cart.discounts->Array.map(computeGlobalDiscountAmount),
  }
}

// Computes the total of the units of all products,
// the expected ones and the received.
// The difference with the quantity is that bulk quantity is fully took in account.
let computeTotalProductsUnits = cart => {
  let totalProductsUnits = cart.products->Array.reduce(Big.fromFloat(0.), (acc, product) =>
    switch product {
    | Unit({quantity}) => acc +. Big.fromFloat(quantity->Float.fromInt)
    | Bulk({quantity}, _) => acc +. quantity
    }
  )
  totalProductsUnits
}

// Computes the total of the quantity of all products,
// the expected ones and the received
let computeTotalProductsQuantity = cart => {
  let (totalProductsQuantity, totalProductsExpectedQuantity) = cart.products->Array.reduce((0, 0), (
    acc,
    product,
  ) =>
    switch product {
    | Unit({quantity, expectedQuantity}) => (acc->fst + quantity, acc->snd + expectedQuantity)
    | Bulk(_, _) => (acc->fst + 1, acc->snd + 1)
    }
  )

  {
    ...cart,
    totalProductsExpectedQuantity: Some(totalProductsExpectedQuantity),
    totalProductsQuantity: Some(totalProductsQuantity),
  }
}

// Computes the total of the quantity of all products,
// the expected ones and the received
let computeTotalDiscounts = cart => {
  let totalDiscounts = Some(
    cart.products->Array.reduce(0.->Big.fromFloat, (acc, product) =>
      switch product {
      | Unit({totalDiscounts})
      | Bulk({totalDiscounts}, _) =>
        totalDiscounts->Option.getExn +. acc
      }
    ),
  )

  {...cart, totalDiscounts}
}

// Batch all computes before
// global discounts application
let preCompute = cart =>
  cart->computeFees->computeTotalAmountExcludingGlobalDiscounts->computeGlobalDiscountsAmount

// Batch all computes after
// global discounts application
let postCompute = cart =>
  cart
  ->computeTaxes
  ->computeTotalAmounts
  ->computeGlobalDiscounts
  ->computeTotalAmountOfGoods
  ->computeTotalProductsQuantity
  ->computeTotalDiscounts

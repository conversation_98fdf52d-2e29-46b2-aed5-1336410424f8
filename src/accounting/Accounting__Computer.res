open Accounting__Types

module ComputerCart = Accounting__Computer__Cart
module ComputerProduct = Accounting__Computer__Product

// This function uses all computing functions
// to provide a complete and trustable cart
// Its loops over the products, computes their values
// and finally computes the values in the cart itself
// Calculate everything on the products and stop at the level of the global discount
// since to apply the global discount on the products I need the total of the cart (to calculate the ratio)
// therefore we calculate up to the level of the totals (without global discount and taxes) of each product
// we stop there!
// at this point we jump to calculate the total amount of the cart (without global discount and taxes)
// -> we therefore have an amount of the cart (amount #m1 = accumulate(product.totalAmountExcludingGlobalDiscounts))
// having this amount, we return to the product level to apply the global discount
// we therefore calculate the ratio of each product based on the amount #m1 of the cart
// having the ratio we just apply the global discount to the product and the rest (with taxes ...)
let make = cart => {
  let cartPreComputed = {
    ...cart,
    products: cart.products->Array.map(ComputerProduct.preCompute(~cart)),
  }->ComputerCart.preCompute

  {
    ...cartPreComputed,
    products: cartPreComputed.products->Array.map(
      ComputerProduct.postCompute(~cart=cartPreComputed),
    ),
  }->ComputerCart.postCompute
}

type discountWarning = AmountGreaterThanTotalPrice

type productExpectedQuantityWarning = IsNotMultipleOfPackaging

@genType
type currency =
  | Eur
  | Usd
  | Pound

// Represents the postfix
// when formatting a value
@genType
type postfix =
  | Currency(currency)
  | Percent

@genType
type feeKind =
  | Transport
  | Taxes
  | Other

// Represents an input of a `fee`
@genType
type feeInput = {
  id: option<string>,
  kind: feeKind,
  amount: float,
}

// Used in intern to make computations
// It's shaped and computed from an input(feeInput)
@genType
type fee = {
  // Input fields
  kind: feeKind,
  amount: float,
  // Computed fields
  id: string,
  formattedAmount: option<string>,
  totalAmount: option<Big.t>,
  formattedTotalAmount: option<string>,
}

// All discount kinds
@genType
type discountKind =
  | Currency
  | Percent
  | Free

// Represents an input of discount
@genType
type discountInput = {
  id: option<string>,
  name: string,
  kind: discountKind,
  value: float,
  quantity: int,
}

@genType
type tax = {
  rate: float,
  // Computed fields
  amount: option<Big.t>,
  formattedAmount: option<string>,
}

// Represents the value we get from end-client as an product
// It's composed of fields that should be filled up to shape a computable
// product in intern later
@genType
type productInputStruct<'value> = {
  id: option<string>,
  identifier: option<string>,
  stockKeepingUnit: option<string>,
  name: string,
  description: string,
  capacityUnit: option<string>,
  stock: 'value,
  packaging: option<'value>,
  quantity: 'value,
  expectedQuantity: option<'value>,
  unitPrice: float,
  fees: array<feeInput>,
  discounts: array<discountInput>,
  tax: float,
}

type productInput =
  | Unit({product: productInputStruct<int>})
  | Bulk({product: productInputStruct<float>, precision: int})

// Used to make computations in intern
// It's shaped and computed from an input(discountInput)
@genType
type discount = {
  // Input fields
  name: string,
  kind: discountKind,
  value: float,
  quantity: Big.t,
  // Computed fields
  id: string,
  formattedValue: option<string>,
  amount: option<Big.t>,
  formattedAmount: option<string>,
  warnings: array<discountWarning>,
}

// Here is the shape of the product we manipulate
// in intern and returns to the end-client
@genType
type productStruct<'value> = {
  // Input fields
  identifier: option<string>,
  stockKeepingUnit: option<string>,
  name: string,
  description: string,
  unitPrice: float,
  capacityUnit: option<string>,
  stock: 'value,
  packaging: option<'value>,
  quantity: 'value,
  expectedQuantity: 'value,
  expectedQuantityWarning: array<productExpectedQuantityWarning>,
  fees: array<fee>,
  discounts: array<discount>,
  // Generated fields
  id: string,
  // Recalculated fields without global discounts
  availablesFeeKinds: option<array<feeKind>>,
  totalPrice: option<Big.t>,
  totalLocalDiscounts: option<Big.t>,
  unitFee: option<Big.t>,
  totalFees: option<Big.t>,
  totalAmountExcludingGlobalDiscounts: option<Big.t>,
  // With global discounts
  totalGlobalDiscounts: option<Big.t>,
  totalDiscounts: option<Big.t>,
  totalTaxesExcludingGlobalDiscount: option<Big.t>,
  totalTaxes: option<Big.t>,
  taxes: option<array<tax>>,
  totalAmountExcludingTaxes: option<Big.t>,
  totalAmountIncludingTaxes: option<Big.t>,
  unitCost: option<Big.t>,
  // Formatted fields
  formattedStock: option<string>,
  formattedQuantity: option<string>,
  formattedExpectedQuantity: option<string>,
  formattedUnitPrice: option<string>,
  formattedTotalPrice: option<string>,
  formattedUnitFee: option<string>,
  formattedTotalFees: option<string>,
  formattedTotalLocalDiscounts: option<string>,
  formattedTotalDiscounts: option<string>,
  formattedTotalAmountExcludingGlobalDiscounts: option<string>,
  formattedTotalAmountExcludingTaxes: option<string>,
  formattedTotalAmountIncludingTaxes: option<string>,
}

type product =
  | Unit(productStruct<int>)
  | Bulk(productStruct<Big.t>, int)

// We need an object with this shape to get started...
@genType
type cartInput = {
  products: array<productInput>,
  discounts: array<discountInput>,
  decimalPrecision: int,
  currency: currency,
  taxesFree: bool,
  standardTaxRate: float,
}

// Represents the total of all `fee`s
// applicated on products in the cart
@genType
type feesTotal = {
  kind: feeKind,
  amount: Big.t,
  formattedAmount: option<string>,
}

// From a cartInput we can return an object shaped like bellow
// representing a cart shaped and computed with trustable values
@genType
type cart = {
  // Input fields
  products: array<product>,
  discounts: array<discount>,
  currency: currency,
  decimalPrecision: int,
  taxesFree: bool,
  standardTaxRate: float,
  // Computed fields
  // Total of fees applicated on products, grouped by feeKind
  fees: option<array<feesTotal>>,
  // Total amounts of merchendises (total amounts excluding total fees)
  totalAmountOfGoods: option<Big.t>,
  totalAmountExcludingGlobalDiscounts: option<Big.t>,
  taxes: option<array<tax>>,
  totalTaxes: option<Big.t>,
  totalAmountExcludingTaxes: option<Big.t>,
  totalAmountIncludingTaxes: option<Big.t>,
  totalProductsExpectedQuantity: option<int>,
  totalProductsQuantity: option<int>,
  totalDiscounts: option<Big.t>,
  // Formatted fields
  formattedTotalTaxes: option<string>,
  formattedTotalAmountOfGoods: option<string>,
  formattedTotalAmountExcludingGlobalDiscounts: option<string>,
  formattedTotalAmountExcludingTaxes: option<string>,
  formattedTotalAmountIncludingTaxes: option<string>,
}

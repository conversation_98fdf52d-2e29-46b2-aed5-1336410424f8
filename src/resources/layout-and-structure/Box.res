// NOTE - The layout system is almost complete.
// However, there are two concerns about <Box /> component:
// + how to manage the responsive ?

open Style
open Spaces

let viewStyleFromParams = (
  ~spaceX,
  ~spaceY,
  ~spaceTop,
  ~spaceBottom,
  ~spaceLeft,
  ~spaceRight,
  ~grow,
) =>
  [
    styleFromSpace(~spaceX, ~spaceY),
    styleExceptionFromSpace(~spaceTop, ~spaceBottom, ~spaceLeft, ~spaceRight),
    grow ? style(~flex=1., ()) : style(),
  ]->arrayStyle

@react.component
let make = React.forwardRef((
  ~children=?,
  ~spaceX=?,
  ~spaceY=?,
  ~spaceTop=?,
  ~spaceBottom=?,
  ~spaceLeft=?,
  ~spaceRight=?,
  ~grow=false,
  ~style=?,
  ref,
) => {
  let ref = ref->Js.Nullable.toOption
  let blankContent = switch children {
  | Some(children) =>
    children->React.Children.toArray->Array.every(element => element === React.null)
  | _ => false
  }

  if !blankContent {
    <View
      ?ref
      style={[
        style,
        Some(
          viewStyleFromParams(
            ~spaceX,
            ~spaceY,
            ~spaceTop,
            ~spaceBottom,
            ~spaceLeft,
            ~spaceRight,
            ~grow,
          ),
        ),
      ]->arrayOptionStyle}>
      {switch children {
      | Some(content) => content
      | None => React.null
      }}
    </View>
  } else {
    React.null
  }
})

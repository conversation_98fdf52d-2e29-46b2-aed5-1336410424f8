open Intl
open Style

let styles = StyleSheet.create({
  "view": style(~overflow=#hidden, ()),
  "labelView": style(~overflow=#hidden, ()),
  "contentView": merge([
    style(~overflow=#hidden, ()),
    unsafeCss({
      "transitionDelay": "0s",
      "transitionDuration": ".5s",
      "transitionProperty": "all",
      "transitionTimingFunction": "cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    }),
  ]),
})

let contentViewStyleFromParams = (~opened) =>
  opened
    ? style(~opacity=1., ~paddingTop=Spaces.normal->dp, ())
    : style(~opacity=0., ~height=0.->dp, ~paddingVertical=0.->dp, ())

@react.component
let make = (~text) => {
  let (opened, setOpened) = React.useState(_ => false)

  <View style={styles["view"]}>
    <Inline align=#spaceBetween>
      <View style={styles["labelView"]}>
        <TextStyle size=#large>
          {if !opened {
            text->Js.String.slice(~from=0, ~to_=150) ++ "..."
          } else {
            t("Hide")
          }->React.string}
        </TextStyle>
      </View>
      <IconButton
        name={opened ? #arrow_up_light : #arrow_down_light}
        marginSize=#normal
        onPress={_ => setOpened(is => !is)}
      />
    </Inline>
    <View style={[styles["contentView"], contentViewStyleFromParams(~opened)]->arrayStyle}>
      <TextStyle size=#large> {text->React.string} </TextStyle>
    </View>
  </View>
}

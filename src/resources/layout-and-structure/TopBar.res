open Style

let styles = StyleSheet.create({
  "container": style(
    ~paddingTop=15.->dp,
    ~height=70.->dp,
    ~backgroundColor=Colors.neutralColor00,
    ~shadowColor=Colors.neutralColor50,
    ~shadowOffset=shadowOffset(~width=4.0, ~height=4.0),
    ~shadowOpacity=0.125,
    ~shadowRadius=3.,
    (),
  ),
  "burger": style(~marginLeft=-10.->dp, ()),
})

@react.component
let make = (~title, ~onRequestNavBarToggle) => {
  <View style={styles["container"]}>
    <View style={styles["burger"]}>
      <Burger onPress={_ => onRequestNavBarToggle()} />
    </View>
    <Title level=#1> {title->React.string} </Title>
  </View>
}

let make = React.memo(make)

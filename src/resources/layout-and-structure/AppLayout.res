open Style

let styles = StyleSheet.create({
  "container": merge([
    style(
      ~flex=1.,
      ~alignItems=#center,
      ~backgroundColor=Colors.neutralColor10,
      ~paddingLeft=0.->dp,
      (),
    ),
    unsafeCss({
      "transitionDuration": ".25s",
      "transitionProperty": "padding-left",
      "transitionTimingFunction": "ease-out",
    }),
  ]),
  "wrapper": style(~width=100.->pct, ~flex=1., ()),
  "smallContainer": style(~paddingLeft=Nav.closedSize->dp, ()),
  "largeContainer": style(~paddingLeft=Nav.openedSize->dp, ()),
})

let containerStyleFromParams = (~navOpened) =>
  [
    styles["container"],
    navOpened ? styles["largeContainer"] : styles["smallContainer"],
  ]->Style.arrayStyle

@react.component
let make = (~children, ~navBar, ~alertBar) => {
  let (navState, navDispatch) = Nav.Context.getReducer()

  <Nav.Context.Provider value=(Some(navState), navDispatch)>
    <View style={containerStyleFromParams(~navOpened=navState.opened)}>
      {navBar}
      <View style={styles["wrapper"]}>
        {alertBar}
        children
      </View>
    </View>
  </Nav.Context.Provider>
}

let make = React.memo(make)

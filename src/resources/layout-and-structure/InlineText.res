open Style

let styles = StyleSheet.create({
  "root": style(~display=#flex, ()),
  "textAlignLeft": style(~textAlign=#left, ()),
  "textAlignJustify": style(~textAlign=#justify, ()),
  "textAlignCenter": style(~textAlign=#center, ()),
  "textAlignRight": style(~textAlign=#right, ()),
})

let textStyleFromParams = (~linespace, ~align, ~wrap, ~maxLines) =>
  [
    styles["root"],
    style(~flexWrap=wrap ? #wrap : #nowrap, ()),
    unsafeCss({
      "display": wrap ? "block" : "flex",
      "lineHeight": wrap ? "auto" : `calc(100% + ${linespace->Float.toString}px)`,
    }),
    switch maxLines {
    | Some(qty) =>
      unsafeCss({
        "overflow": "hidden",
        "display": "-webkit-box",
        "WebkitBoxOrient": "vertical",
        "WebkitLineClamp": qty,
      })
    | None => style()
    },
    switch align {
    | #left => styles["textAlignLeft"]
    | #justify => styles["textAlignJustify"]
    | #center => styles["textAlignCenter"]
    | #right => styles["textAlignRight"]
    },
  ]->arrayStyle

type alignment = [#left | #justify | #center | #right]

@react.component
let make = (~children, ~align=#left, ~wrap=true, ~linespace=#xsmall, ~maxLines=?) =>
  <Text style={textStyleFromParams(~linespace=linespace->Spaces.toFloat, ~align, ~wrap, ~maxLines)}>
    children
  </Text>

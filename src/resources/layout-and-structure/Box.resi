let makeProps: (
  ~children: React.element=?,
  ~spaceX: Spaces.t=?,
  ~spaceY: Spaces.t=?,
  ~spaceTop: Spaces.t=?,
  ~spaceBottom: Spaces.t=?,
  ~spaceLeft: Spaces.t=?,
  ~spaceRight: Spaces.t=?,
  ~grow: bool=?,
  ~style: Style.t=?,
  ~key: string=?,
  ~ref: 'ref=?,
  unit,
) => {
  "children": option<React.element>,
  "grow": option<bool>,
  "spaceBottom": option<Spaces.t>,
  "spaceLeft": option<Spaces.t>,
  "spaceRight": option<Spaces.t>,
  "spaceTop": option<Spaces.t>,
  "spaceX": option<Spaces.t>,
  "spaceY": option<Spaces.t>,
  "style": option<Style.t>,
}

@genType
let make: {
  "children": option<React.element>,
  "grow": option<bool>,
  "spaceX": option<Spaces.t>,
  "spaceY": option<Spaces.t>,
  "spaceTop": option<Spaces.t>,
  "spaceBottom": option<Spaces.t>,
  "spaceLeft": option<Spaces.t>,
  "spaceRight": option<Spaces.t>,
  "style": option<Style.t>,
} => React.element

open Style

let styles = StyleSheet.create({
  "container": style(
    ~flex=1.,
    ~paddingHorizontal=Spaces.xxlarge->dp,
    ~paddingTop=Spaces.large->dp,
    (),
  ),
  "header": style(~flexDirection=#row, ~alignItems=#center, ()),
  "title": style(~flex=1., ()),
  "content": style(~flex=1., ()),
})

let containerStyleFromParams = (~variation) =>
  switch variation {
  | #compact => style(~paddingBottom=20.->dp, ())
  | #standard => style(~paddingBottom=90.->dp, ())
  }

type variation = [#standard | #compact]

@react.component
let make = (
  ~children,
  ~variation: variation=#standard,
  ~title=?,
  ~subtitle=?,
  ~renderTitleEnd=() => React.null,
  ~renderActions=?,
  ~renderHeaderActions=?,
) => {
  let (canGoBack, _) = Navigation.useGoBack()

  <View style={[styles["container"], containerStyleFromParams(~variation)]->arrayStyle}>
    {switch variation {
    | #standard if canGoBack =>
      <Box
        spaceBottom={switch subtitle {
        | Some(_) => #medium
        | _ => #small
        }}>
        <GoBackButton />
      </Box>
    | _ => React.null
    }}
    {switch title {
    | Some(title) =>
      <View style={styles["header"]}>
        <View style={styles["title"]}>
          <Inline space=#medium>
            <Stack space=#xsmall>
              <Inline space=#medium alignY=#bottom wrap=true>
                <Title level=#2> {title->React.string} </Title>
                {renderTitleEnd()}
              </Inline>
              {switch subtitle {
              | Some(value) =>
                <TextStyle variation=#normal size=#small> {value->React.string} </TextStyle>
              | _ => React.null
              }}
            </Stack>
          </Inline>
        </View>
        {switch renderActions {
        | Some(renderActions) => renderActions()
        | None => React.null
        }}
      </View>
    | _ => React.null
    }}
    {switch renderHeaderActions {
    | Some(renderHeaderActions) => <Box spaceTop=#medium> {renderHeaderActions()} </Box>
    | None => React.null
    }}
    <View style={styles["content"]}> children </View>
  </View>
}

let make = React.memo(make)

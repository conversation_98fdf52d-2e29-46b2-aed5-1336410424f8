open WebAPI
open Style

type navOpened = option<bool>

let styles = StyleSheet.create({
  "wrapper": merge([
    style(
      ~zIndex=10,
      ~bottom=0.->dp,
      ~left=0.->dp,
      ~right=0.->dp,
      ~height=64.->dp,
      ~backgroundColor=Colors.transparent,
      (),
    ),
    unsafeCss({
      "position": "fixed",
      "transitionProperty": "height width",
      "transitionDuration": ".3s",
      "transitionTimingFunction": "cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    }),
  ]),
  "wrapperHidden": merge([
    style(~height=0.->dp, ~overflow=#hidden, ()),
    unsafeCss({
      "transitionProperty": "height width",
      "transitionDuration": ".35s",
      "transitionTimingFunction": "cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    }),
  ]),
  "inner": merge([
    style(
      ~zIndex=10,
      ~height=100.->pct,
      ~backgroundColor=Colors.neutralColor00,
      ~shadowColor=Colors.neutralColor50,
      ~shadowOffset=shadowOffset(~width=0.0, ~height=0.3),
      ~shadowOpacity=0.2,
      ~shadowRadius=10.,
      ~flexDirection=#row,
      ~alignItems=#center,
      ~justifyContent=#spaceBetween,
      ~paddingHorizontal=15.->dp,
      ~paddingVertical=20.->dp,
      (),
    ),
  ]),
})

let wrapperStyleFromParams = (~navOpened: navOpened, ~show) =>
  arrayOptionStyle([
    !show ? Some(styles["wrapperHidden"]) : None,
    switch navOpened {
    | None => Some(style(~left=0.->dp, ()))
    | Some(false) => Some(style(~left=(Nav.closedSize +. 1.)->dp, ()))
    | Some(true) => Some(style(~left=(Nav.openedSize +. 1.)->dp, ()))
    },
  ])

@react.component
let make = (~displayThreshold=100., ~renderStart=() => React.null, ~renderEnd, ~navOpened=?) => {
  let (show, setShow) = React.useState(_ => false)

  let onScroll = React.useCallback0(_ => {
    let scrolled = window->Window.pageYOffset
    if scrolled >= displayThreshold && !show {
      setShow(_ => true)
    } else if scrolled < displayThreshold {
      setShow(_ => false)
    }
  })

  React.useEffect0(_ => {
    window->Window.addEventListener("scroll", onScroll)
    Some(_ => window->Window.removeEventListener("scroll", onScroll))
  })

  <View style={[styles["wrapper"], wrapperStyleFromParams(~navOpened, ~show)]->arrayStyle}>
    <View style={styles["inner"]}>
      <Box spaceX=#medium>
        <Inline> {renderStart()} </Inline>
      </Box>
      <Box spaceX=#medium> {renderEnd()} </Box>
    </View>
  </View>
}

let make = React.memo(make)

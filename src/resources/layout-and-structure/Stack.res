// NOTE — at some point, this component should stop relying
// on wrapping into a View the children, this has always been
// a pain to manage for styling and JSX comprehension.
// Now we're getting rid of the crossplatform caveat, we can start
// using the gap property that works very well with flexbox.

open Style

let styles = StyleSheet.create({
  "baseContainer": style(~flexDirection=#column, ~width=100.->pct, ~paddingBottom=0.->dp, ()),
  "alignLeft": style(~alignItems=#flexStart, ()),
  "alignRight": style(~alignItems=#flexEnd, ()),
  "alignCenter": style(~alignItems=#center, ()),
  "alignSpaceBetween": style(~alignItems=#baseline, ()),
  "alignStretch": style(~alignItems=#stretch, ()),
  "baseItem": style(~paddingBottom=0.->dp, ()),
  "xxsmallItem": style(~paddingBottom=Spaces.xxsmall->dp, ()),
  "xsmallItem": style(~paddingBottom=Spaces.xsmall->dp, ()),
  "smallItem": style(~paddingBottom=Spaces.small->dp, ()),
  "normalItem": style(~paddingBottom=Spaces.normal->dp, ()),
  "mediumItem": style(~paddingBottom=Spaces.medium->dp, ()),
  "largeItem": style(~paddingBottom=Spaces.large->dp, ()),
  "xlargeItem": style(~paddingBottom=Spaces.xlarge->dp, ()),
  "xxlargeItem": style(~paddingBottom=Spaces.xxlarge->dp, ()),
  "hugeItem": style(~paddingBottom=Spaces.huge->dp, ()),
  "xhugeItem": style(~paddingBottom=Spaces.xhuge->dp, ()),
  "xxhugeItem": style(~paddingBottom=Spaces.xxhuge->dp, ()),
})

let containerStyleFromParams = (~align) =>
  [
    styles["baseContainer"],
    switch align {
    | #start => styles["alignLeft"]
    | #end => styles["alignRight"]
    | #center => styles["alignCenter"]
    | #spaceBetween => styles["alignSpaceBetween"]
    | #stretch => styles["alignStretch"]
    },
  ]->arrayStyle

let itemStyleFromParams = (~space, ~isLastItem) =>
  if isLastItem {
    emptyStyle
  } else {
    switch space {
    | #xxsmall => styles["xxsmallItem"]
    | #xsmall => styles["xsmallItem"]
    | #small => styles["smallItem"]
    | #xnormal | #normal => styles["normalItem"]
    | #xmedium | #medium => styles["mediumItem"]
    | #large => styles["largeItem"]
    | #xlarge => styles["xlargeItem"]
    | #xxlarge => styles["xxlargeItem"]
    | #huge => styles["hugeItem"]
    | #xhuge => styles["xhugeItem"]
    | #xxhuge => styles["xxhugeItem"]
    | #none => styles["baseItem"]
    }
  }

type align = [#center | #end | #start | #spaceBetween | #stretch]

@react.component
let make = (~children, ~space=#xxsmall, ~align=#stretch) => {
  let children = children->React.Children.toArray->Array.keep(child => child !== React.null)
  let childrenLength = children->Array.size

  if childrenLength > 0 {
    <View style={containerStyleFromParams(~align)}>
      {children
      ->Array.mapWithIndex((index, element) =>
        <View
          key={index->Int.toString}
          style={itemStyleFromParams(~isLastItem=index === childrenLength - 1, ~space)}>
          element
        </View>
      )
      ->React.array}
    </View>
  } else {
    React.null
  }
}

open Style

type variation = [#common | #table | #unset]
let variationCommon = #common

module Action = {
  type handler =
    | Callback(unit => unit)
    | OpenLink(Navigation.to)
    | OpenLinkNewTab(Navigation.to)

  type t = {
    icon: Icon.t,
    title: string,
    handler: handler,
  }
}

let actionOpenLinkNewTab = (~icon, ~title, ~to) => {
  Action.icon,
  title,
  handler: OpenLinkNewTab(to),
}

let actionCallback = (~icon, ~title, ~callback) => {
  Action.icon,
  title,
  handler: Callback(callback),
}

let styles = StyleSheet.create({
  "container": style(
    ~paddingVertical=Spaces.normal->dp,
    ~borderRadius=5.,
    ~backgroundColor=Colors.neutralColor00,
    (),
  ),
  "containerShadow": style(
    ~shadowColor=Colors.neutralColor50,
    ~shadowOffset=shadowOffset(~width=3., ~height=0.),
    ~shadowOpacity=0.1,
    ~shadowRadius=10.,
    (),
  ),
  "containerStretch": style(~flex=1., ()),
  "title": style(~display=#flex, ~flexDirection=#row, ~justifyContent=#spaceBetween, ()),
  "centerContent": style(~justifyContent=#center, ()),
})

@react.component
let make = (
  ~children,
  ~title=?,
  ~action: option<Action.t>=?,
  ~variation=#common,
  ~shadowed=false,
  ~stretch=false,
  ~centerContent=false,
) => {
  let actionButton = switch action {
  | Some({icon, title, handler}) =>
    <Tooltip content={<Tooltip.Span text=title />} placement=#top delay=250 closeDelay=0>
      {switch handler {
      | Callback(onPress) =>
        <IconButton
          name=icon
          onPress={_ => onPress()}
          color=Colors.neutralColor100
          hoveredColor=Colors.brandColor60
        />
      | OpenLink(to) =>
        <IconLink to name=icon color=Colors.neutralColor100 hoveredColor=Colors.brandColor60 />
      | OpenLinkNewTab(to) =>
        <IconLink
          to name=icon openNewTab=true color=Colors.neutralColor100 hoveredColor=Colors.brandColor60
        />
      }}
    </Tooltip>
  | _ => React.null
  }

  <View
    style={[
      Some(styles["container"]),
      shadowed ? Some(styles["containerShadow"]) : None,
      stretch ? Some(styles["containerStretch"]) : None,
      centerContent ? Some(styles["centerContent"]) : None,
    ]->arrayOptionStyle}>
    {switch variation {
    | #table =>
      <Box spaceY=#medium spaceTop=#small spaceBottom=#medium>
        {switch title {
        | Some(title) =>
          <Box spaceX=#large spaceBottom=#xnormal>
            <View style={styles["title"]}>
              <Title level=#3 weight=#strong> {title->React.string} </Title>
              {actionButton}
            </View>
          </Box>
        | None => React.null
        }}
        <Stack space=#none> children </Stack>
      </Box>
    | #common =>
      <Box spaceX=#large spaceTop=#small spaceBottom=#medium>
        {switch title {
        | Some(title) =>
          <Box spaceBottom=#large>
            <View style={styles["title"]}>
              <Title level=#3 weight=#strong> {title->React.string} </Title>
              {actionButton}
            </View>
          </Box>
        | None => React.null
        }}
        <Stack space=#xlarge> children </Stack>
      </Box>
    | #unset =>
      <>
        {switch title {
        | Some(title) =>
          <Box spaceBottom=#medium>
            <View style={styles["title"]}>
              <Title level=#3 weight=#strong> {title->React.string} </Title>
              {actionButton}
            </View>
          </Box>
        | None => React.null
        }}
        <Stack space=#xlarge> children </Stack>
      </>
    }}
  </View>
}

let make = React.memo(make)

React.setDisplayName(make, "Card")

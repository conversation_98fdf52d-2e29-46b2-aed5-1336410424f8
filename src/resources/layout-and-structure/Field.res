// TODO - props to trigger a scrollIntoView whenever `errorMessage` become defined
open Style
open Intl

module SubText = {
  let styles = StyleSheet.create({
    "main": merge([
      FontFaces.libreFranklinRegularStyle,
      style(~marginTop=Spaces.xsmall->dp, ~fontSize=FontSizes.xsmall, ()),
    ]),
    "information": style(~color=Colors.neutralColor50, ()),
    "error": style(~color=Colors.dangerColor55, ()),
  })

  @react.component
  let make = React.memo((~children, ~variation: [#error | #information]) =>
    <Text
      style={[
        styles["main"],
        switch variation {
        | #information => styles["information"]
        | #error => styles["error"]
        },
      ]->arrayStyle}>
      children
    </Text>
  )
}

let makeLabel = (label, ~required) => {
  switch (label, required) {
  | ("", true) => t("Required field") ++ " *"
  | (_, true) => label ++ " *"
  | (_, false) => label
  }
}

module Action = {
  type handler =
    | Callback(unit => unit)
    | OpenLink(Navigation.to)
    | OpenLinkNewTab(Navigation.to)

  type t = {
    text: string,
    handler: handler,
  }
}

@react.component
let make = React.forwardRef((
  ~children,
  ~label=?,
  ~labelAriaProps=?,
  ~tooltip=?,
  ~errorMessage=?,
  ~action=?,
  ~required=false,
  ref,
) => {
  let labelRef = React.useRef(Js.Nullable.null)

  <View ref style={style(~flex=1., ())}>
    {switch (label, action) {
    | (Some(label), None) =>
      <View ref=labelRef style={unsafeCss({"width": "fit-content"})}>
        <Inline space=#xsmall alignY=#top>
          <Label text={label->makeLabel(~required)} ariaProps=?labelAriaProps />
          {switch tooltip {
          | Some(elements) =>
            <TooltipIcon
              variation=#info placement=#"bottom start" crossOffset={-1.} altTriggerRef=labelRef>
              {elements}
            </TooltipIcon>
          | _ => React.null
          }}
        </Inline>
      </View>
    | (Some(label), Some({Action.text: text, handler})) =>
      <Inline align=#spaceBetween>
        <Label text={label->makeLabel(~required)} ariaProps=?labelAriaProps />
        {switch tooltip {
        | Some(elements) =>
          <TooltipIcon
            variation=#info placement=#"bottom start" crossOffset={-1.} altTriggerRef=labelRef>
            {elements}
          </TooltipIcon>

        | _ => React.null
        }}
        {switch handler {
        | Callback(action) =>
          <Touchable
            style={ReactDOM.Style.make(~marginBottom="3px", ())}
            excludeFromTabOrder=true
            onPress={_ => action()}>
            <TextStyle size=#xsmall underlined=true> {text->React.string} </TextStyle>
          </Touchable>
        | OpenLink(to) => <TextLink text to />
        | OpenLinkNewTab(to) => <TextLink text to openNewTab=true />
        }}
      </Inline>
    | (None, _) => React.null
    }}
    children
    {switch errorMessage {
    | Some("") | None => React.null
    | Some(errorMessage) => <SubText variation=#error> {errorMessage->React.string} </SubText>
    }}
  </View>
})

let make = React.memo(make)

open Style

let styles = StyleSheet.create({
  "main": style(
    ~borderBottomColor=Colors.neutralColor15,
    ~borderBottomWidth=Style.hairlineWidth,
    (),
  ),
})

// TODO - https://react-spectrum.adobe.com/react-aria/useSeparator.html
// TODO - merge the Separator.res and Divider.res as they share identical logic/purpose
@react.component
let make = (~spaceY=?, ~spaceTop=?, ~spaceBottom=?) =>
  <Box ?spaceY ?spaceTop ?spaceBottom>
    <View style={styles["main"]}> React.null </View>
  </Box>

let make = React.memo(make)

open Style

let styles = StyleSheet.create({
  "icon": style(~paddingHorizontal=3.->dp, ()),
  "contentView": merge([
    style(~overflow=#hidden, ()),
    unsafeCss({
      "transitionDelay": "0s",
      "transitionDuration": ".5s",
      "transitionProperty": "all",
      "transitionTimingFunction": "cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    }),
  ]),
})

let contentViewStyleFromParams = (~opened) =>
  [
    styles["contentView"],
    opened
      ? style(~opacity=1., ~paddingTop=Spaces.xsmall->dp, ())
      : style(~opacity=0., ~height=0.->dp, ~paddingVertical=0.->dp, ()),
  ]->arrayStyle

@react.component
let make = (~children, ~triggerShowView, ~triggerHideView, ~onToggle=?) => {
  let (opened, setOpened) = React.useState(() => false)
  let (ref, hovered) = Hover.use()

  let onPress = _ => {
    setOpened(_ => !opened)
    switch onToggle {
    | Some(onToggle) => onToggle(!opened)
    | _ => ()
    }
  }

  <View>
    <Inline>
      <Touchable ref onPress>
        {if opened {
          <Inline>
            <View style={styles["icon"]}>
              <Icon name=#arrow_up_light />
            </View>
            triggerHideView
          </Inline>
        } else {
          <View
            style={style(
              ~borderBottomColor=hovered ? Colors.neutralColor20 : Colors.transparent,
              ~borderBottomWidth=Style.hairlineWidth,
              (),
            )}>
            <Inline>
              <View style={styles["icon"]}>
                <Icon name=#arrow_down_light />
              </View>
              triggerShowView
            </Inline>
          </View>
        }}
      </Touchable>
    </Inline>
    <View style={contentViewStyleFromParams(~opened)}> children </View>
  </View>
}

let make = React.memo(make)

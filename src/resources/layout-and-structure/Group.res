open StyleX

let styles = StyleX.create({
  "container": style(~display=#flex, ()),
})

let containerStyleProps = (~wrap, ~spaceX, ~spaceY) => {
  let columnGapStr = spaceX->Spaces.toFloat->Float.toString
  let rowGapStr = spaceY->Spaces.toFloat->Float.toString
  StyleX.props([
    styles["container"],
    style(~columnGap=`${columnGapStr}px`, ~rowGap=`${rowGapStr}px`, ()),
    style(~flexWrap=wrap ? #wrap : #nowrap, ()),
  ])
}

let itemStyleProps = (~flexBasisPct, ~spaceX, ~gridLength) => {
  let gapPx = `${spaceX->Spaces.toFloat->Float.toString}px`
  let gridLength = gridLength->Int.toString
  StyleX.props([
    style(
      ~flexGrow="1",
      ~flexShrink="1",
      ~flexBasis=`calc(${flexBasisPct} - ${gapPx} / ${gridLength} * (${gridLength} - 1))`,
      (),
    ),
  ])
}

@react.component
let make = (~children, ~grid=?, ~wrap=true, ~spaceX=#large, ~spaceY=#large) => {
  let childrenLength = children->React.Children.toArray->Array.length
  let defaultFlexBasisPct = Js.Math.max_int(
    100 / childrenLength - childrenLength,
    childrenLength - 2,
  )

  let containerStyleProps = containerStyleProps(~wrap, ~spaceX, ~spaceY)
  let {style: ?containerStyle, className: ?containerClassName} = containerStyleProps

  // TODO — should eventually be a react-aria <Group />
  <div style=?containerStyle className=?containerClassName>
    {children
    ->React.Children.toArray
    ->Array.mapWithIndex((idx, child) => {
      let flexBasisPct =
        grid
        ->Option.flatMap(grid => grid->Array.get(mod(idx, grid->Array.size)))
        ->Option.getWithDefault(`${defaultFlexBasisPct->Int.toString}%`)
      let {style: ?itemStyle, className: ?itemClassName} = itemStyleProps(
        ~flexBasisPct,
        ~spaceX,
        ~gridLength=grid->Option.mapWithDefault(childrenLength, value => value->Array.size),
      )
      let rootKey = grid->Option.getWithDefault([])->Array.joinWith("", a => a)
      <div key={`${rootKey}${Int.toString(idx)}`} style=?itemStyle className=?itemClassName>
        {child}
      </div>
    })
    ->React.array}
  </div>
}

let make = React.memo(make)

// NOTE — at some point, this component should stop relying
// on wrapping into a View the children, this has always been
// a pain to manage for styling and JSX comprehension.
// Now we're getting rid of the crossplatform caveat, we can start
// using the gap property that works very well with flexbox.
// TODO - to be replaced by react-aria Group component?

open Style

let styles = StyleSheet.create({
  "grow": style(~flex=1., ()),
  "wrap": style(~flexWrap=#wrap, ()),
  "alignStart": style(~justifyContent=#flexStart, ()),
  "alignEnd": style(~justifyContent=#flexEnd, ()),
  "alignCenter": style(~justifyContent=#center, ()),
  "alignSpaceBetween": style(~justifyContent=#spaceBetween, ()),
  "alignSpaceAround": style(~justifyContent=#spaceAround, ()),
  "alignYTop": style(~alignItems=#flexStart, ()),
  "alignYBottom": style(~alignItems=#flexEnd, ()),
  "alignYCenter": style(~alignItems=#center, ()),
  "alignYStretch": style(~alignItems=#stretch, ()),
  "alignYBaseline": style(~alignItems=#baseline, ()),
  "baseContainer": style(~flexDirection=#row, ~alignItems=#center, ()),
  "baseItem": Style.unsafeCss({"flexBasis": "fit-content"}),
  "lastItem": style(~paddingRight=0.->dp, ()),
  "xxsmallContainer": style(~marginTop=(Spaces.xxsmall /. -2.)->dp, ()),
  "xsmallContainer": style(~marginTop=(Spaces.xsmall /. -2.)->dp, ()),
  "smallContainer": style(~marginTop=(Spaces.small /. -2.)->dp, ()),
  "xnormalContainer": style(~marginTop=(Spaces.xnormal /. -2.)->dp, ()),
  "normalContainer": style(~marginTop=(Spaces.normal /. -2.)->dp, ()),
  "xmediumContainer": style(~marginTop=(Spaces.xmedium /. -2.)->dp, ()),
  "mediumContainer": style(~marginTop=(Spaces.medium /. -2.)->dp, ()),
  "largeContainer": style(~marginTop=(Spaces.large /. -2.)->dp, ()),
  "xlargeContainer": style(~marginTop=(Spaces.xlarge /. -2.)->dp, ()),
  "xxlargeContainer": style(~marginTop=(Spaces.xxlarge /. -2.)->dp, ()),
  "hugeContainer": style(~marginTop=(Spaces.huge /. -2.)->dp, ()),
  "xhugeContainer": style(~marginTop=(Spaces.xhuge /. -2.)->dp, ()),
  "xxhugeContainer": style(~marginTop=(Spaces.xxhuge /. -2.)->dp, ()),
  "xxsmallItem": style(~marginTop=(Spaces.xxsmall /. 2.)->dp, ~paddingRight=Spaces.xxsmall->dp, ()),
  "xsmallItem": style(~marginTop=(Spaces.xsmall /. 2.)->dp, ~paddingRight=Spaces.xsmall->dp, ()),
  "smallItem": style(~marginTop=(Spaces.small /. 2.)->dp, ~paddingRight=Spaces.small->dp, ()),
  "xnormalItem": style(~marginTop=(Spaces.xnormal /. 2.)->dp, ~paddingRight=Spaces.xnormal->dp, ()),
  "normalItem": style(~marginTop=(Spaces.normal /. 2.)->dp, ~paddingRight=Spaces.normal->dp, ()),
  "xmediumItem": style(~marginTop=(Spaces.xmedium /. 2.)->dp, ~paddingRight=Spaces.xmedium->dp, ()),
  "mediumItem": style(~marginTop=(Spaces.medium /. 2.)->dp, ~paddingRight=Spaces.medium->dp, ()),
  "largeItem": style(~marginTop=(Spaces.large /. 2.)->dp, ~paddingRight=Spaces.large->dp, ()),
  "xlargeItem": style(~marginTop=(Spaces.xlarge /. 2.)->dp, ~paddingRight=Spaces.xlarge->dp, ()),
  "xxlargeItem": style(~marginTop=(Spaces.xxlarge /. 2.)->dp, ~paddingRight=Spaces.xxlarge->dp, ()),
  "hugeItem": style(~marginTop=(Spaces.huge /. 2.)->dp, ~paddingRight=Spaces.huge->dp, ()),
  "xhugeItem": style(~marginTop=(Spaces.xhuge /. 2.)->dp, ~paddingRight=Spaces.xhuge->dp, ()),
  "xxhugeItem": style(~marginTop=(Spaces.xhuge /. 2.)->dp, ~paddingRight=Spaces.xxhuge->dp, ()),
})

let growStyleFromParams = (~grow) => [grow ? Some(styles["grow"]) : None]->arrayOptionStyle
let wrapStyleFromParams = (~wrap) => [wrap ? Some(styles["wrap"]) : None]->arrayOptionStyle

type align = [#center | #start | #end | #spaceAround | #spaceBetween]
type alignY = [#center | #top | #bottom | #stretch | #baseline]

let wrapperViewStyleFromParams = (~grow, ~wrap, ~space, ~align: align, ~alignY: alignY) =>
  [
    styles["baseContainer"],
    growStyleFromParams(~grow),
    wrapStyleFromParams(~wrap),
    switch align {
    | #start => styles["alignStart"]
    | #end => styles["alignEnd"]
    | #center => styles["alignCenter"]
    | #spaceBetween => styles["alignSpaceBetween"]
    | #spaceAround => styles["alignSpaceAround"]
    },
    switch alignY {
    | #top => styles["alignYTop"]
    | #bottom => styles["alignYBottom"]
    | #center => styles["alignYCenter"]
    | #stretch => styles["alignYStretch"]
    | #baseline => styles["alignYBaseline"]
    },
    switch space {
    | #xxsmall => styles["xxsmallContainer"]
    | #xsmall => styles["xsmallContainer"]
    | #small => styles["smallContainer"]
    | #xnormal => styles["xnormalContainer"]
    | #normal => styles["normalContainer"]
    | #xmedium => styles["xmediumContainer"]
    | #medium => styles["mediumContainer"]
    | #large => styles["largeContainer"]
    | #xlarge => styles["xlargeContainer"]
    | #xxlarge => styles["xxlargeContainer"]
    | #huge => styles["hugeContainer"]
    | #xhuge => styles["xhugeContainer"]
    | #xxhuge => styles["xxhugeContainer"]
    | #none => styles["baseContainer"]
    },
  ]->arrayStyle

let itemViewStyleFromParams = (~wrap, ~index, ~childrenLength, ~space) =>
  [
    styles["baseItem"],
    wrapStyleFromParams(~wrap),
    switch space {
    | #xxsmall => styles["xxsmallItem"]
    | #xsmall => styles["xsmallItem"]
    | #small => styles["smallItem"]
    | #xnormal => styles["xnormalItem"]
    | #normal => styles["normalItem"]
    | #xmedium => styles["xmediumItem"]
    | #medium => styles["mediumItem"]
    | #large => styles["largeItem"]
    | #xlarge => styles["xlargeItem"]
    | #xxlarge => styles["xxlargeItem"]
    | #huge => styles["hugeItem"]
    | #xhuge => styles["xhugeItem"]
    | #xxhuge => styles["xxhugeItem"]
    | #none => styles["baseItem"]
    },
    if index === childrenLength - 1 {
      styles["lastItem"]
    } else {
      emptyStyle
    },
  ]->arrayStyle

@react.component
let make = (
  ~children,
  ~space=#xxsmall,
  ~align=#start,
  ~alignY=#center,
  ~grow=false,
  ~wrap=false,
) => {
  let children = children->React.Children.toArray
  let childrenLength = children->Array.size

  <View style={wrapperViewStyleFromParams(~grow, ~wrap, ~align, ~alignY, ~space)}>
    {children
    ->Array.mapWithIndex((index, child) =>
      <View
        key={index->Int.toString}
        style={itemViewStyleFromParams(~wrap, ~index, ~childrenLength, ~space)}>
        child
      </View>
    )
    ->React.array}
  </View>
}

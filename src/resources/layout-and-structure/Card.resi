@genType type variation = [#common | #table | #unset]

@genType let variationCommon: variation

module Action: {
  type handler =
    | Callback(unit => unit)
    | OpenLink(Navigation.to)
    | OpenLinkNewTab(Navigation.to)

  type t = {
    icon: Icon.t,
    title: string,
    handler: handler,
  }
}

@genType
let actionCallback: (~icon: Icon.t, ~title: string, ~callback: unit => unit) => Action.t

@genType
let actionOpenLinkNewTab: (~icon: Icon.t, ~title: string, ~to: Navigation.to) => Action.t

@genType @react.component
let make: (
  ~children: React.element,
  ~title: string=?,
  ~action: Action.t=?,
  ~variation: variation=?,
  ~shadowed: bool=?,
  ~stretch: bool=?,
  ~centerContent: bool=?,
) => React.element

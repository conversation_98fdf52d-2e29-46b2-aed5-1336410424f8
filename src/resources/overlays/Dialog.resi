module Section: {
  @react.component
  let make: (
    ~children: React.element,
    ~title: string=?,
    ~tooltip: React.element=?,
    ~collapsable: bool=?,
    ~defaultCollapsed: bool=?,
  ) => React.element
}

@react.component
let make: (
  ~ariaProps: ReactAria.Overlay.overlayProps,
  ~children: React.element,
  ~width: float=?,
  ~title: string,
  ~titleStartElement: React.element=?,
  ~commitDisabled: bool=?,
  ~commitLoading: bool=?,
  ~commitButtonText: string=?,
  ~onCommit: unit => unit,
  ~onRequestClose: unit => unit,
) => React.element

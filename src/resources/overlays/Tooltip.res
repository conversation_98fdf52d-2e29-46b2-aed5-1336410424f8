open StyleX

type alignment = [#auto | #stretch | #start | #center | #end]

module Span = {
  @inline let font = `normal 400 12px "Libre Franklin"`

  type tone = [#neutral | #faded | #success]

  let styles = StyleX.create({
    "root": style(~font, ~lineHeight=Spaces.xmediumPx, ~whiteSpace=#"pre-line", ()),
  })

  let styleProps = (~tone, ~italic, ~bold, ~size) =>
    StyleX.props([
      styles["root"],
      style(
        ~color=switch tone {
        | #neutral => Colors.neutralColor00
        | #faded => Colors.neutralColor25
        | #success => Colors.successColor50
        },
        ~fontWeight=bold ? #700 : #400,
        ~fontStyle=italic ? #italic : #normal,
        ~fontSize=FontSizes.toPx(size),
        (),
      ),
    ])

  @react.component
  let make = (~text, ~tone=#neutral, ~italic=false, ~bold=false, ~size=#xxsmall) => {
    let {?style, ?className} = styleProps(~tone, ~italic, ~bold, ~size)
    <span ?style ?className> {text->React.string} </span>
  }

  let make = React.memo(make)
}

module Br = {
  let styles = StyleX.create({
    "root": style(~display=#block, ~content="", ~lineHeight="0px", ()),
  })

  let styleProps = (~space) =>
    StyleX.props([
      styles["root"],
      style(
        ~paddingBottom=space->Spaces.toFloat->Float.toString ++ "px",
        ~lineHeight=space->Spaces.toFloat->Float.toString ++ "px",
        (),
      ),
    ])

  @react.component
  let make = (~space=#none) => {
    let {?style, ?className} = styleProps(~space)
    <br ?style ?className />
  }

  let make = React.memo(make)
}

module Overlay = {
  let styles = StyleX.create({
    "root": style(
      ~display=#"inline-block",
      ~maxWidth="350px",
      ~padding=`${Spaces.smallPx} ${Spaces.normalPx}`,
      ~paddingBottom="9px",
      ~lineHeight=Spaces.xmediumPx,
      ~backgroundColor=Colors.neutralColor80,
      ~borderRadius="5px",
      ~color=Colors.transparent, // NOTE - help the user knows the Tooltip.Span should be used
      (),
    ),
    "arrow": style(
      ~position=#absolute,
      ~borderStyle=#solid,
      ~borderTopWidth="6px",
      ~borderRightWidth="5px",
      ~borderBottomWidth="0px",
      ~borderLeftWidth="5px",
      ~borderTopColor=Colors.neutralColor80,
      ~borderRightColor=Colors.transparent,
      ~borderBottomColor=Colors.neutralColor80,
      ~borderLeftColor=Colors.transparent,
      (),
    ),
  })

  let styleProps = () => StyleX.props([styles["root"]])

  open! ReactAria.Overlay.Position
  let arrowStyleProps = (~placement, ~positionStyleProps, ~crossOffset) =>
    StyleX.props([
      styles["arrow"],
      style(
        ~transform=switch placement {
        | #top => "rotate(0deg)"
        | #right => "rotate(90deg)"
        | #bottom => "rotate(180deg)"
        | #left => "rotate(-90deg)"
        },
        (),
      ),
      switch (placement, positionStyleProps) {
      | (#top, {style: {left: Some(left)}}) =>
        style(~bottom="-4px", ~left=Float.toString(left +. crossOffset -. 4.) ++ "px", ())
      | (#right, {style: {top: Some(top)}}) =>
        style(~top=Float.toString(top +. crossOffset -. 3.) ++ "px", ~left="-7px", ())
      | (#bottom, {style: {left: Some(left)}}) =>
        style(~top="-4px", ~left=Float.toString(left +. crossOffset -. 4.) ++ "px", ())
      | (#left, {style: {top: Some(top)}}) =>
        style(~top=Float.toString(top +. crossOffset -. 3.) ++ "px", ~right="-7px", ())
      | _ => style()
      },
    ])

  @react.component
  let make = React.memo((
    ~arrowed=true,
    ~placement,
    ~offset=?,
    ~crossOffset=0.,
    ~content,
    ~state,
    ~ariaProps,
    ~triggerDomRef,
    ~immediateClose,
  ) => {
    let domRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef

    let props = ReactAria.mergeProps2(
      ariaProps,
      {"isOpen": state.ReactStately.Tooltip.Trigger.opened},
    )
    let {tooltipProps} = ReactAria.Tooltip.use(~props, ~state, ())

    let position = ReactAria.Overlay.Position.use(
      ~props={
        targetRef: triggerDomRef,
        overlayRef: domRef,
        opened: state.opened,
        placement,
        crossOffset,
        offset: switch (arrowed, offset) {
        | (true, _) => Spaces.small +. offset->Option.getWithDefault(0.)
        | (false, None) => Spaces.xxsmall
        | (_, Some(offset)) => offset
        },
      },
    )

    let {?style, ?className} = styleProps()
    let {style: ?arrowStyle, className: ?arrowClassName} = arrowStyleProps(
      ~placement=position.placement,
      ~positionStyleProps=position.arrowProps,
      ~crossOffset,
    )

    let tooltipProps = immediateClose
      ? ReactAria.mergeProps2(
          tooltipProps,
          {
            "tabIndex": 0,
            "onMouseEnter": Js.Nullable.null,
            "onTouchStart": Js.Nullable.null,
            "onClick": event => event->WebAPI.DomEvent.stopPropagation,
          },
        )
      : ReactAria.mergeProps2(
          tooltipProps,
          {
            "tabIndex": 0,
            "onClick": event => event->WebAPI.DomEvent.stopPropagation,
          },
        )
    let props = ReactAria.mergeProps2(tooltipProps, position.overlayProps)

    <ReactAria.Overlay>
      <ReactAria.Spread props>
        <span ref=domRef>
          <AnimatedRender displayed=state.opened duration=50>
            {<>
              {if arrowed {
                <div style=?arrowStyle className=?arrowClassName />
              } else {
                React.null
              }}
              <div ?style ?className> {content} </div>
            </>}
          </AnimatedRender>
        </span>
      </ReactAria.Spread>
    </ReactAria.Overlay>
  })
}

@react.component
let make = (
  ~children=React.null,
  ~arrowed=true,
  ~placement=#bottom,
  ~offset=?,
  ~crossOffset=?,
  ~content,
  ~disabled=false,
  ~delay=500,
  ~closeDelay=50,
  ~triggerAlign: alignment=#auto,
  ~opened as derivedOpened=?,
  ~onOpenChange=?,
) => {
  let domRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef

  let props = {
    ReactStately.Tooltip.Trigger.delay,
    closeDelay,
    disabled,
    opened: ?derivedOpened,
    ?onOpenChange,
  }
  let state = ReactStately.Tooltip.Trigger.useState(~props)
  let {triggerProps, tooltipProps} = ReactAria.Tooltip.Trigger.use(~state, ~props, ~ref=domRef)

  let opened = state.opened && !disabled
  let triggerStyle = ReactDOMStyle.make(
    ~display="flex",
    ~flexDirection="column",
    ~alignSelf=(triggerAlign :> string),
    ~cursor="inherit",
    (),
  )

  // NOTE - this allows to select the text of the trigger with disabled tooltip as well
  let triggerProps = ReactAria.mergeProps2(
    triggerProps,
    {
      "onPointerDown": Obj.magic(%raw(`null`)),
      "onMouseDown": Obj.magic(%raw(`null`)),
    },
  )

  <>
    <ReactAria.Spread props=triggerProps>
      <div ref=domRef style=triggerStyle> children </div>
    </ReactAria.Spread>
    {if opened {
      <Overlay
        arrowed
        placement
        ?offset
        ?crossOffset
        content
        state
        ariaProps=tooltipProps
        triggerDomRef=domRef
        immediateClose={closeDelay < 50}
      />
    } else {
      React.null
    }}
  </>
}

let make = React.memo(make)

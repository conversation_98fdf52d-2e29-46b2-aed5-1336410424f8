import {
  make,
  Span_make,
  Br_make,
  type Br_make_Props as TooltipBrProps,
  type Props as TooltipSpanProps,
  type make_Props as TooltipProps,
} from './Tooltip.gen'

const Tooltip = make as React.NamedExoticComponent<
  React.PropsWithoutRef<TooltipProps>
>
const TooltipSpan = Span_make as React.NamedExoticComponent<
  React.PropsWithoutRef<TooltipSpanProps>
>
const TooltipBr = Br_make as React.NamedExoticComponent<
  React.PropsWithoutRef<TooltipBrProps>
>

Tooltip.displayName = 'Tooltip'

export { Tooltip, TooltipSpan, TooltipBr, type TooltipProps }

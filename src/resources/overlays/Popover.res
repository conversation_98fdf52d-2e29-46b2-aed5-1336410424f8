// NOTE - unmount animation doesn't work because:
// - portal instantly being removed (not so clean but can be delayed)
// - Popover rendering condition may be handled by consumer

open Style

type t = ReactStately.Overlay.Trigger.state

type trigger = {
  state: t,
  ariaProps: ReactAria.Overlay.Trigger.t,
  ref: React.ref<Js.Nullable.t<Dom.element>>,
}

// NOTE - when Popover is consumed as standalone (ie. not inside a react aria based component)
// this hook must be called to be bound to a custom trigger element and handle the popover state.
let useTrigger = (~defaultOpened=false, ()) => {
  let ref = React.useRef(Js.Nullable.null)
  let state = ReactStately.Overlay.Trigger.useState(~props={defaultOpen: defaultOpened})
  let ariaProps = ReactAria.Overlay.Trigger.use(
    ~props={\"type": #dialog},
    ~state,
    ~ref=ref->ReactDOM.Ref.domRef,
    (),
  )
  {ref, state, ariaProps}
}

// NOTE - to be used within a <Popover> in combination of Popover.useTrigger
module Dialog = {
  @react.component
  let make = React.memo((
    ~children,
    ~ariaLabel="popover-dialog",
    ~ariaProps as overlayProps: ReactAria.Overlay.overlayProps,
    ~style=?,
    ~className=?,
  ) => {
    let domRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {dialogProps} = ReactAria.Dialog.use(~props={\"aria-label": ariaLabel}, ~ref=domRef)

    <ReactAria.Spread props={ReactAria.mergeProps2(dialogProps, overlayProps)}>
      <div ref=domRef ?style ?className> children </div>
    </ReactAria.Spread>
  })
}

// REVIEW - to be removed: once Menu will be revamped with useMenu, the Menu items will be builtin
// allowing its items to manage the popover state such as closing it when clicking on an item.
module Context = {
  let context = React.createContext(None)

  module Provider = {
    let make = React.Context.provider(context)
    let makeProps = (~value, ~children, ()) =>
      {
        "value": value,
        "children": children,
      }
  }

  exception PopoverContextNotFound
  let use = () =>
    switch React.useContext(context) {
    | Some(state) => state
    | None => raise(PopoverContextNotFound)
    }
}

// NOTE - the Popover state can be consumed within its children: eg. inside items
// being children of a component using Popover requiring a popover toggle/close action.
let useState = Context.use

module ArrowOverlay = {
  open! Svg
  open ReactAria.Overlay.Position

  let styles = StyleSheet.create({
    "root": style(~position=#absolute, ~zIndex=1, ()),
  })

  @react.component
  let make = React.memo((~arrowProps, ~placement, ~crossOffset) => {
    let style = arrayStyle([
      styles["root"],
      unsafeCss({
        "transform": switch placement {
        | #top => "rotate(180deg)"
        | #right => "rotate(-90deg)"
        | #bottom => "rotate(0deg)"
        | #left => "rotate(90deg)"
        },
      }),
      switch (placement, arrowProps) {
      | (#top, {style: {left: Some(left)}}) =>
        style(~bottom=-10.->dp, ~left=(left +. crossOffset -. 8.)->dp, ())
      | (#right, {style: {top: Some(top)}}) =>
        style(~top=(top +. crossOffset -. 8.)->dp, ~left=-10.->dp, ())
      | (#bottom, {style: {left: Some(left)}}) =>
        style(~top=-10.->dp, ~left=(left +. crossOffset -. 8.)->dp, ())
      | (#left, {style: {top: Some(top)}}) =>
        style(~top=(top +. crossOffset -. 8.)->dp, ~right=-10.->dp, ())
      | _ => style()
      },
    ])

    <View style>
      <Svg width="16" height="16" viewBox="0 0 20 20">
        <Path
          d="M 9.278 6.281 Q 9.989 5.718 10.7 6.281 L 19.741 13.437 Q 20.452 14 19.029 14 L 0.949 14 Q -0.474 14 0.237 13.437 Z"
          fill=Colors.neutralColor00
          filter="drop-shadow(rgba(0, 0, 0, 0.05) 0px -1px 1px)"
          stroke=Colors.neutralColor20
          strokeDasharray="13, 21"
        />
      </Svg>
    </View>
  })
}

let styles = StyleSheet.create({
  "root": style(
    ~backgroundColor=Colors.neutralColor00,
    ~overflow=#hidden,
    ~shadowColor=Colors.neutralColor100,
    ~shadowOffset=shadowOffset(~width=0., ~height=8.),
    ~shadowOpacity=0.1,
    ~shadowRadius=14.,
    ~borderWidth=1.,
    ~borderRadius=5.,
    (),
  ),
})

let viewStyleFromParams = (~layout, ~size, ~triggerWidth, ~borderColor) =>
  [
    Some(styles["root"]),
    Some(style(~borderColor, ())),
    switch layout {
    | #auto => None
    | #triggerMinWidth => Some(style(~minWidth=triggerWidth->dp, ()))
    | #triggerStrictWidth => Some(style(~minWidth=triggerWidth->dp, ~maxWidth=triggerWidth->dp, ()))
    },
    switch size {
    | #compact => Some(style(~borderRadius=3., ()))
    | #normal => None
    },
  ]->arrayOptionStyle

type layout = [#auto | #triggerMinWidth | #triggerStrictWidth]
type variation = [#normal | #arrowed]
type size = [#normal | #compact]

@react.component
let make = (
  ~children,
  ~popoverRef=?,
  ~triggerRef,
  ~state,
  ~variation=#normal,
  ~borderColor=Colors.neutralColor20,
  ~size=#normal,
  ~placement=#bottom,
  ~layout=#auto,
  ~animation=?,
  ~overlayPriority=true,
  ~dismissable=true,
  ~shouldUpdatePosition=true,
  ~modal=true,
  ~offset=3.,
  ~crossOffset=0.,
) => {
  let ref = popoverRef->Option.getWithDefault(React.useRef(Js.Nullable.null))

  // NOTE - the position is always updated until the popover is defined
  let shouldUpdatePosition =
    shouldUpdatePosition || (!shouldUpdatePosition && ref.React.current->Js.isNullable)

  let props = {
    ReactAria.Popover.popoverRef: ref->ReactDOM.Ref.domRef,
    triggerRef: triggerRef->ReactDOM.Ref.domRef,
    shouldUpdatePosition,
    placement,
    offset: size === #compact ? offset -. 1. : offset,
    crossOffset,
    isNonModal: !modal,
  }
  let (_, {Dimensions.width: triggerWidth}) = Dimensions.use(~ref=triggerRef, ())
  let {popoverProps, arrowProps, placement} = ReactAria.Popover.use(~props, ~state)

  // NOTE — With Rescript v11, we'll be able to rely on JsxDom.domProps
  // which exposes the whole record type.
  let popoverProps = Obj.magic(popoverProps)

  if !overlayPriority {
    popoverProps
    ->Js.Dict.get("style")
    ->Option.map(style => Obj.magic(style)->Js.Dict.set("zIndex", 1))
    ->ignore
  }
  if !dismissable {
    popoverProps->Js.Dict.set("onBlur", Obj.magic(() => ()))->ignore
  }
  let animation =
    animation->Option.getWithDefault(
      triggerWidth > 550. ? #fadeTranslation : #fadeBubbleTranslation,
    )

  <Context.Provider value=Some(state)>
    <ReactAria.Overlay>
      <ReactAria.Spread props=popoverProps>
        <div ref={ref->ReactDOM.Ref.domRef}>
          <AnimatedRender displayed=state.opened duration=75 animation>
            {switch variation {
            | #arrowed => <ArrowOverlay arrowProps placement crossOffset />
            | #normal => React.null
            }}
            <ReactAria.DismissButton onDismiss=state.onRequestClose />
            <View style={viewStyleFromParams(~layout, ~size, ~triggerWidth, ~borderColor)}>
              children
            </View>
            <ReactAria.DismissButton onDismiss=state.onRequestClose />
          </AnimatedRender>
        </div>
      </ReactAria.Spread>
    </ReactAria.Overlay>
  </Context.Provider>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["state"].opened === newProps["state"].opened && !newProps["state"].opened
)

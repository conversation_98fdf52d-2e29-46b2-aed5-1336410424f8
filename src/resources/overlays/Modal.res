// TODO - should rely on <Popover> OR implements `useModalOverlay`
// TODO - add story (once reworked with React Aria)

open Style
open WebAPI

module ModalFooter = {
  open Intl

  let styles = StyleSheet.create({
    "footerSecondary": style(
      ~zIndex=2,
      ~backgroundColor=Colors.neutralColor00,
      ~shadowColor=Colors.neutralColor50,
      ~shadowOffset=shadowOffset(~width=2., ~height=0.),
      ~shadowOpacity=0.2,
      ~shadowRadius=10.,
      (),
    ),
  })

  let footerViewStyleFromParams = (~variation) =>
    switch variation {
    | #primary => style()
    | #secondary => styles["footerSecondary"]
    }

  type variation = [#primary | #secondary]

  @react.component
  let make = (
    ~variation: variation,
    ~renderStartText=() => React.null,
    ~criticalButtonText,
    ~criticalButtonCallback,
    ~abortButtonText,
    ~allowCommit=true,
    ~commitButtonVariation,
    ~commitButtonText,
    ~commitButtonCallback,
    ~onRequestClose=() => (),
  ) =>
    <View style={footerViewStyleFromParams(~variation)}>
      {switch variation {
      | #primary =>
        <Box spaceX=#large spaceTop=#xsmall spaceBottom=#xlarge>
          <Inline space=#normal align=#end>
            <Button variation=#neutral onPress={_ => onRequestClose()}>
              {switch (commitButtonText, commitButtonCallback) {
              | (Some(_), Some(_)) => t("Cancel")
              | _ => t("Close")
              }->React.string}
            </Button>
            {switch (commitButtonText, commitButtonCallback) {
            | (Some(text), Some(callback)) =>
              <Button
                disabled={!allowCommit}
                variation=commitButtonVariation
                onPress={_ => {
                  onRequestClose()
                  callback()
                }}>
                {text->React.string}
              </Button>
            | _ => React.null
            }}
          </Inline>
        </Box>
      | #secondary =>
        <Box spaceX=#large spaceY=#small>
          <Inline align=#spaceBetween>
            {renderStartText()}
            <Inline space=#small>
              {switch (criticalButtonText, criticalButtonCallback) {
              | (Some(text), Some(callback)) =>
                <Inline>
                  <Button
                    variation=#danger
                    onPress={_ => {
                      onRequestClose()
                      callback()
                    }}>
                    {text->React.string}
                  </Button>
                </Inline>
              | _ => React.null
              }}
              {switch abortButtonText {
              | Some(text) =>
                <Button variation=#neutral onPress={_ => onRequestClose()}>
                  {text->React.string}
                </Button>
              | _ => React.null
              }}
              {switch (commitButtonText, commitButtonCallback) {
              | (Some(text), Some(callback)) =>
                <Button
                  disabled={!allowCommit}
                  variation=commitButtonVariation
                  onPress={_ => {
                    onRequestClose()
                    callback()
                  }}>
                  {text->React.string}
                </Button>
              | _ => React.null
              }}
            </Inline>
          </Inline>
        </Box>
      }}
    </View>

  let make = React.memo(make)
}

let styles = StyleSheet.create({
  "header": style(~borderBottomWidth=2., ~borderBottomColor=Colors.neutralColor15, ()),
  "wrapper": merge([
    style(
      ~left=0.->dp,
      ~top=0.->dp,
      ~right=0.->dp,
      ~bottom=0.->dp,
      ~zIndex=100,
      ~justifyContent=#center,
      ~alignItems=#center,
      (),
    ),
    unsafeCss({
      "position": "fixed",
      "backgroundColor": "transparent",
    }),
  ]),
  "backdrop": merge([
    style(~zIndex=0, ~opacity=0., ()),
    unsafeCss({
      "width": "100vw",
      "height": "100vh",
      "cursor": "auto",
      "background-color": "rgb(7, 6, 30, 0.8)",
      "backdrop-filter": "blur(2.5px) saturate(0)",
    }),
  ]),
  "inner": style(
    ~position=#absolute,
    ~zIndex=1,
    ~minWidth=580.->dp,
    ~maxWidth=60.->pct,
    ~minHeight=225.->dp,
    ~maxHeight=90.->pct,
    ~backgroundColor=Colors.neutralColor00,
    ~borderRadius=5.,
    ~overflow=#hidden,
    ~shadowColor=Colors.neutralColor100,
    ~shadowOffset=shadowOffset(~width=0.0, ~height=0.0),
    ~shadowOpacity=0.1,
    ~shadowRadius=20.,
    (),
  ),
  "body": style(~flex=1., ()),
  "wrapperViewOpened": merge([
    style(~zIndex=90, ()),
    unsafeCss({
      "visibility": "visible",
      "transitionDelay": "0s",
      "transitionDuration": ".1s",
      "transitionProperty": "all",
      "transitionTimingFunction": "ease-out",
    }),
  ]),
  "wrapperViewClosed": merge([
    style(~zIndex=-1, ()),
    unsafeCss({
      "visibility": "hidden",
      "transitionDelay": "0s",
      "transitionDuration": ".1s",
      "transitionProperty": "all",
      "transitionTimingFunction": "ease-out",
    }),
  ]),
  "backdropViewOpened": merge([
    style(~opacity=0.75, ()),
    unsafeCss({
      "visibility": "visible",
      "transitionDelay": "0s",
      "transitionDuration": ".1s",
      "transitionProperty": "all",
      "transitionTimingFunction": "ease-out",
    }),
  ]),
  "backdropViewClosed": merge([
    style(~opacity=0., ()),
    unsafeCss({
      "visibility": "hidden",
      "transitionDelay": "0s",
      "transitionDuration": ".1s",
      "transitionProperty": "all",
      "transitionTimingFunction": "ease-out",
    }),
  ]),
  "innerViewOpened": merge([
    style(
      ~opacity=1.,
      ~transform=[ReactNative.Style.translateY(~translateY=0.), ReactNative.Style.scale(~scale=1.)],
      (),
    ),
    unsafeCss({
      "visibility": "visible",
      "transitionDelay": "0s",
      "transitionDuration": "0.2s",
      "transitionProperty": "all",
      "transitionTimingFunction": "cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    }),
  ]),
  "innerViewClosed": merge([
    style(
      ~opacity=0.,
      ~transform=[
        ReactNative.Style.translateY(~translateY=-5.),
        ReactNative.Style.scale(~scale=0.55),
      ],
      (),
    ),
    unsafeCss({
      "visibility": "hidden",
      "transitionDelay": "0s",
      "transitionDuration": ".1s",
      "transitionProperty": "all",
      "transitionTimingFunction": "linear",
    }),
  ]),
})

let wrapperViewStyleFromParams = (~opened) =>
  [opened ? styles["wrapperViewOpened"] : styles["wrapperViewClosed"]]->arrayStyle

let backdropViewStyleFromParams = (~opened) =>
  [opened ? styles["backdropViewOpened"] : styles["backdropViewClosed"]]->arrayStyle

let innerViewStyleFromParams = (~opened, ~compact) =>
  [
    opened ? styles["innerViewOpened"] : styles["innerViewClosed"],
    !compact ? style(~width=60.->pct, ()) : style(),
  ]->arrayStyle

let bodyViewStyleFromParams = (~backgroundColor) => style(~backgroundColor, ())

@react.component
let make = (
  ~children,
  ~opened,
  ~title,
  ~variation=#primary,
  ~compact=true,
  ~backgroundColor=Colors.neutralColor05,
  ~hideFooter=false,
  ~renderStartText=() => React.null,
  ~criticalButtonText=?,
  ~criticalButtonCallback=?,
  ~abortButtonText=?,
  ~allowCommit=true,
  ~commitButtonVariation=#success,
  ~commitButtonText=?,
  ~commitButtonCallback=?,
  ~onRequestClose=?,
) => {
  let (modalRoot, setModalRoute) = React.useState(_ =>
    document->Document.querySelector("#portals-modal")
  )

  React.useEffect2(() => {
    switch (modalRoot, document->Document.querySelector("body")) {
    | (None, Some(body)) =>
      let element = document->Document.createElement("div")
      element->DomElement.setAttribute("id", "portals-modal")
      setModalRoute(_ => Some(element))
      body->DomElement.asNode->DomNode.appendChild(~child=element)
    | (_, _) => ()
    }

    let handler = evt => {
      let ctrlKeyPressed = KeyboardEvent.ctrlKey(evt)
      switch KeyboardEvent.key(evt) {
      | "Enter" if ctrlKeyPressed =>
        commitButtonCallback->Option.forEach(commit => commit())
        onRequestClose->Option.forEach(close => close())
      | "Escape" => onRequestClose->Option.forEach(fn => fn())
      | _ => ()
      }
    }

    let domDocument = document->Document.asDomElement
    if opened {
      domDocument->DomElement.addKeyDownEventListener(handler)
    } else {
      domDocument->DomElement.removeKeyDownEventListener(handler)
    }

    Some(() => domDocument->DomElement.removeKeyDownEventListener(handler))
  }, (modalRoot, commitButtonCallback))

  switch (modalRoot, opened) {
  | (Some(portal), true) =>
    ReactDOM.createPortal(
      <View style={[styles["wrapper"], wrapperViewStyleFromParams(~opened)]->arrayStyle}>
        <Touchable onPress={_ => onRequestClose->Option.forEach(fn => fn())}>
          <View style={[styles["backdrop"], backdropViewStyleFromParams(~opened)]->arrayStyle} />
        </Touchable>
        <View style={[styles["inner"], innerViewStyleFromParams(~opened, ~compact)]->arrayStyle}>
          <View style={styles["header"]}>
            <Box spaceX=#large spaceY=#large>
              <Columns align=#center>
                <Column width=#fluid>
                  <Title level=#3 weight=#medium> {title->React.string} </Title>
                </Column>
                <Column width=#content>
                  {switch onRequestClose {
                  | Some(onRequestClose) =>
                    <IconButton
                      name=#close_bold
                      color=Colors.neutralColor30
                      hoveredColor=Colors.neutralColor50
                      onPress={_ => onRequestClose()}
                    />
                  | None => React.null
                  }}
                </Column>
              </Columns>
            </Box>
          </View>
          <View style={[styles["body"], bodyViewStyleFromParams(~backgroundColor)]->arrayStyle}>
            <Box grow=true> children </Box>
            {if !hideFooter {
              <ModalFooter
                variation
                allowCommit
                renderStartText
                commitButtonText
                commitButtonVariation
                abortButtonText
                criticalButtonText
                commitButtonCallback
                criticalButtonCallback
                ?onRequestClose
              />
            } else {
              React.null
            }}
          </View>
        </View>
      </View>,
      portal,
    )
  | (_, _) => React.null
  }
}

let make = React.memo(make)

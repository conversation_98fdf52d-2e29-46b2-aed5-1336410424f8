// TODO - to rename as <InputView> / <TriggerView> / <TextTrigger> ?
open Intl
open Style

let styles = StyleSheet.create({
  "root": style(
    ~overflow=#hidden,
    ~width=100.->pct,
    ~justifyContent=#center,
    ~borderWidth=1.,
    ~borderRadius=5.,
    ~borderColor=Colors.transparent,
    (),
  ),
  "filter": style(
    ~height=42.->dp,
    ~paddingHorizontal=Spaces.xmedium->dp,
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  // TODO - should use TextFieldStyle
  "field": style(
    ~height=40.->dp,
    ~paddingHorizontal=Spaces.normal->dp,
    ~backgroundColor=Colors.neutralColor00,
    ~borderColor=Colors.neutralColor20,
    (),
  ),
  "text": merge([
    FontFaces.libreFranklinRegularStyle,
    style(
      ~flex=1.,
      ~color=Colors.neutralColor90,
      ~fontSize=FontSizes.normal,
      ~letterSpacing=0.125,
      (),
    ),
  ]),
  "textLabel": style(~color=Colors.neutralColor50, ~letterSpacing=-0.2, ~paddingRight=0.->dp, ()),
  "textDisabled": style(~color=Colors.neutralColor35, ()),
  "filterActive": style(~backgroundColor=Colors.neutralColor20, ()),
  "fieldActive": style(~borderColor=Colors.neutralColor25, ()),
  "filterDisabled": style(~backgroundColor=Colors.neutralColor20, ()),
  "fieldDisabled": style(
    ~backgroundColor=Colors.neutralColor05,
    ~borderColor=Colors.neutralColor15,
    (),
  ),
  "filterFocused": style(~borderColor=Colors.neutralColor25, ()),
  "filterHighlighted": style(~borderColor=Colors.neutralColor30, ()),
  "fieldFocused": style(~borderColor=Colors.neutralColor30, ()),
  "fieldHighlighted": style(~borderColor=Colors.neutralColor35, ()),
  "fieldFocusedAndErrored": style(~borderColor=Colors.dangerColor55, ()),
  "fieldErrored": style(~borderColor=Colors.dangerColor30, ()),
})

let filterStyleFromParams = (~hovered, ~focused, ~active, ~highlighted, ~disabled, ~size) =>
  [
    styles["root"],
    styles["filter"],
    switch (hovered, active, disabled) {
    | (true, _, false) | (_, true, false) => styles["filterActive"]
    | (_, _, true) => styles["filterDisabled"]
    | (false, false, false) => style()
    },
    highlighted ? styles["filterHighlighted"] : style(),
    focused ? styles["filterFocused"] : style(),
    switch size {
    | #normal => style(~minWidth=160.->dp, ())
    | #compact =>
      style(
        ~minWidth=130.->dp,
        ~height=30.->dp,
        ~paddingHorizontal=Spaces.normal->dp,
        ~paddingVertical=Spaces.small->dp,
        ~borderRadius=3.,
        (),
      )
    },
  ]->arrayStyle

let fieldStyleFromParams = (
  ~hovered,
  ~focused,
  ~active,
  ~highlighted,
  ~disabled,
  ~errored,
  ~size,
) =>
  [
    styles["root"],
    styles["field"],
    switch (hovered, active, disabled) {
    | (true, _, false)
    | (_, true, false) =>
      styles["fieldActive"]
    | (_, _, true) => styles["fieldDisabled"]
    | (false, false, false) => style()
    },
    switch (focused, errored) {
    | (true, true) => styles["fieldFocusedAndErrored"]
    | (true, false) => styles["fieldFocused"]
    | (false, true) => styles["fieldErrored"]
    | (false, false) => style()
    },
    highlighted ? styles["fieldHighlighted"] : style(),
    switch size {
    | #normal => style(~minWidth=160.->dp, ())
    | #compact =>
      style(
        ~minWidth=130.->dp,
        ~height=30.->dp,
        ~paddingHorizontal=Spaces.normal->dp,
        ~paddingVertical=Spaces.small->dp,
        ~borderRadius=3.,
        (),
      )
    },
  ]->arrayStyle

let textStyleFromParams = (~disabled, ~size) =>
  [
    styles["text"],
    disabled ? styles["textDisabled"] : style(),
    switch size {
    | #compact =>
      merge([FontFaces.libreFranklinRegularStyle, style(~fontSize=FontSizes.xsmall, ())])
    | #normal => style()
    },
  ]->arrayStyle

type size = [#normal | #compact]
type inputFieldPresetProps = {
  required: bool,
  tooltip?: React.element,
  errorMessage?: string,
}
type preset = [
  | #filter
  | #inputField(inputFieldPresetProps)
]

@react.component
let make = (
  ~children,
  ~preset: preset,
  ~size=#normal,
  ~growContent=false,
  ~label=?,
  ~icon=?,
  ~hideIcon=false,
  ~hovered,
  ~active=false,
  ~focused=false,
  ~highlighted=false,
  ~disabled=false,
) => {
  let iconComponent = {
    let name = switch icon {
    | Some(name) => name
    | None => active ? #arrow_up_light : #arrow_down_light
    }
    let size = switch size {
    | #compact => 16.
    | #normal => 20.
    }
    let fill = if highlighted {
      Colors.neutralColor90
    } else if hovered || focused {
      Colors.neutralColor70
    } else {
      Colors.neutralColor50
    }

    <View style={style(~marginRight=-2.->dp, ~paddingLeft=Spaces.xxsmall->dp, ())}>
      <Icon name size fill />
    </View>
  }

  let style = switch preset {
  | #filter => filterStyleFromParams(~hovered, ~focused, ~highlighted, ~active, ~size, ~disabled)
  | #inputField({?errorMessage}) =>
    fieldStyleFromParams(
      ~hovered,
      ~active,
      ~focused,
      ~highlighted,
      ~disabled,
      ~errored=errorMessage->Option.isSome,
      ~size,
    )
  }

  switch preset {
  | #filter =>
    <View style>
      <Inline space=#xsmall align=#spaceBetween grow=growContent>
        <Inline grow=growContent>
          {switch label {
          | Some(label) =>
            <Text style={[textStyleFromParams(~disabled, ~size), styles["textLabel"]]->arrayStyle}>
              {(label ++ (t(":") ++ " "))->React.string}
            </Text>
          | _ => React.null
          }}
          <Text style={textStyleFromParams(~disabled, ~size)}> children </Text>
        </Inline>
        {if !hideIcon {
          iconComponent
        } else {
          React.null
        }}
      </Inline>
    </View>
  | #inputField({required, ?errorMessage, ?tooltip}) =>
    <Field ?label required ?errorMessage ?tooltip>
      <View style>
        <Inline space=#xsmall align=#spaceBetween grow=growContent>
          <Text style={textStyleFromParams(~disabled, ~size)}> children </Text>
          {if !hideIcon {
            iconComponent
          } else {
            React.null
          }}
        </Inline>
      </View>
    </Field>
  }
}

let make = React.memo(make)

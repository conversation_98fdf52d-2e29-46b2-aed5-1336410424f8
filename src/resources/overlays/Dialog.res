open StyleX
open Intl
open WebAPI

module Section = {
  module HeaderTooltipIcon = {
    @react.component
    let make = React.memo((~content, ~titleWrapperRef) =>
      <div
        style={ReactDOMStyle.make(
          ~marginRight="13px",
          ~transform="scale(0.9)",
          ~opacity="0.75",
          (),
        )}>
        <Offset top={-0.5}>
          <TooltipIcon altTriggerRef=titleWrapperRef offset=3.> {content} </TooltipIcon>
        </Offset>
      </div>
    )
  }

  let styles = StyleX.create({
    "Section_root": style(
      ~display=#flex,
      ~flexDirection=#column,
      ~padding=`${Spaces.xsmallPx} ${Spaces.largePx} 6px ${Spaces.largePx}`,
      ~gap=Spaces.normalPx,
      (),
    ),
    "Section_header": style(~display=#flex, ~gap=Spaces.xsmallPx, ()),
    "Section_headerHoverable": style(
      ~\":hover"=style(~filter="drop-shadow(0.5px 0 0 #00000080)", ()),
      (),
    ),
    "Section_headerDivider": style(
      ~flex="1",
      ~alignSelf=#center,
      ~height="0.5px",
      ~backgroundColor=Colors.neutralColor20,
      (),
    ),
    "Section_titleWrapper": style(~display=#contents, ()),
    "Section_title": style(
      ~font=`normal 400 12px "Libre Franklin"`,
      ~color=Colors.neutralColor90,
      (),
    ),
    "Section_content": style(
      ~display=#flex,
      ~flexDirection=#inherit,
      ~gap="inherit",
      ~overflow=#hidden,
      ~transition="all .15s cubic-bezier(.4, 0, .2, 1)",
      (),
    ),
  })

  let styleProps = () => StyleX.props([styles["Section_root"]])
  let headerStyleProps = (~hoverable) =>
    StyleX.props([
      styles["Section_header"],
      hoverable ? styles["Section_headerHoverable"] : styles["none"],
    ])
  let headerDividerStyleProps = () => StyleX.props([styles["Section_headerDivider"]])
  let titleStyleProps = () => StyleX.props([styles["Section_title"]])
  let titleWrapperStyleProps = () => StyleX.props([styles["Section_titleWrapper"]])
  let styleContentProps = (~hidden) =>
    StyleX.props([
      styles["Section_content"],
      style(~maxHeight=hidden ? "0" : "300px", ~opacity=hidden ? 0. : 1., ()),
      style(~marginBottom=hidden ? `-${Spaces.xlargePx}` : "0", ()),
    ])

  @react.component
  let make = React.memo((
    ~children,
    ~title=?,
    ~tooltip=?,
    ~collapsable=false,
    ~defaultCollapsed=false,
  ) => {
    let titleWrapperRef = React.useRef(Js.Nullable.null)
    let (opened, setOpened) = React.useState(() => !defaultCollapsed || !collapsable)

    let {?style, ?className} = styleProps()
    let {style: ?headerStyle, className: ?headerClassName} = headerStyleProps(
      ~hoverable=collapsable,
    )
    let {className: ?headerDividerClassName} = headerDividerStyleProps()
    let {style: ?titleStyle, className: ?titleClassName} = titleStyleProps()
    let {style: ?titleWrapperStyle, className: ?titleWrapperClassName} = titleWrapperStyleProps()
    let {style: ?contentStyle, className: ?contentClassName} = styleContentProps(~hidden=!opened)

    <div ?style ?className>
      {switch title {
      | Some(title) =>
        <Touchable disabled={!collapsable} onPress={_ => setOpened(v => !v)}>
          <div style=?headerStyle className=?headerClassName>
            {if collapsable {
              <Icon
                name={opened ? #arrow_down_light : #arrow_up_light}
                fill=Colors.neutralColor90
                size=13.
              />
            } else {
              React.null
            }}
            <div
              ref={titleWrapperRef->ReactDOM.Ref.domRef}
              style=?titleWrapperStyle
              className=?titleWrapperClassName>
              <span style=?titleStyle className=?titleClassName> {title->React.string} </span>
              {switch tooltip {
              | Some(content) => <HeaderTooltipIcon content titleWrapperRef />
              | None => React.null
              }}
              <div className=?headerDividerClassName />
            </div>
          </div>
        </Touchable>
      | None => React.null
      }}
      <div style=?contentStyle className=?contentClassName> children </div>
    </div>
  })
}

let styles = StyleX.create({
  "root": style(~maxWidth="550px", ()),
  "header": style(
    ~display=#flex,
    ~gap="6px",
    ~padding=`${Spaces.normalPx} ${Spaces.largePx}`,
    ~paddingBottom=Spaces.xnormalPx,
    ~borderBottom="1px solid " ++ Colors.neutralColor20,
    (),
  ),
  "headerTitle": style(~font=`normal 700 16px "Archivo"`, ~color=Colors.neutralColor90, ()),
  "content": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~overflow=#auto,
    ~maxHeight="60vh",
    ~gap=Spaces.normalPx,
    ~paddingTop=Spaces.normalPx,
    ~paddingBottom=Spaces.largePx,
    (),
  ),
  "footer": style(
    ~display=#flex,
    ~justifyContent=#"flex-end",
    ~borderTop="1px solid " ++ Colors.neutralColor20,
    ~gap="6px",
    ~padding=`${Spaces.xnormalPx} ${Spaces.mediumPx}`,
    (),
  ),
})

let styleProps = (~width) =>
  StyleX.props([
    styles["root"],
    switch width {
    | Some(width) => style(~width=Float.toString(width) ++ "px", ())
    | None => style()
    },
  ])
let headerStyleProps = () => StyleX.props([styles["header"]])
let headerTitleStyleProps = () => StyleX.props([styles["headerTitle"]])
let contentStyleProps = () => StyleX.props([styles["content"]])
let footerStyleProps = () => StyleX.props([styles["footer"]])

let bottomGradientStyle = ReactDOM.Style.make(
  ~position="absolute",
  ~bottom="0",
  ~left="0",
  ~right="0",
  ~zIndex="1",
  ~pointerEvents="none",
  ~height="13px",
  ~backgroundImage="radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 75%)",
  ~backgroundSize="100% 200%",
  ~backgroundPosition="center top",
  (),
)

@react.component
let make = (
  ~ariaProps,
  ~children,
  ~width=?,
  ~title,
  ~titleStartElement=React.null,
  ~commitDisabled=false,
  ~commitLoading=false,
  ~commitButtonText=t("Save"),
  ~onCommit,
  ~onRequestClose,
) => {
  let contentRef = React.useRef(Js.Nullable.null)
  let (contentScrollable, setContentScrollable) = React.useState(() => false)

  let handleScrollable = React.useCallback1(() =>
    switch contentRef.current->Js.Nullable.toOption {
    | Some(domElement) =>
      setContentScrollable(_ =>
        domElement->DomElement.scrollHeight > domElement->DomElement.clientHeight
      )
    | None => ()
    }
  , [contentRef])

  React.useLayoutEffect1(() => {
    handleScrollable()
    None
  }, [contentRef])

  ReactAria.Resize.useObserver({
    ref: contentRef->ReactDOM.Ref.domRef,
    onResize: handleScrollable,
  })

  let onScroll = React.useCallback0(event => {
    let domElement =
      event
      ->ReactEvent.UI.currentTarget
      ->ReactDomEventTarget.toUnsafeDomEventTarget
      ->EventTarget.unsafeAsDomElement

    let hasReachedBottomScroll =
      domElement->DomElement.scrollTop->Float.toInt + domElement->DomElement.clientHeight >=
        domElement->DomElement.scrollHeight - 10

    setContentScrollable(_ => !hasReachedBottomScroll)
  })

  let onKeyDown = React.useCallback2(event =>
    switch WebAPI.KeyboardEvent.key(event) {
    | "Enter" if !commitDisabled =>
      onCommit()
      event->WebAPI.KeyboardEvent.preventDefault
    | _ => ()
    }
  , (onCommit, commitDisabled))

  React.useEffect1(() => {
    let document = WebAPI.document->WebAPI.Document.asDomElement
    document->WebAPI.DomElement.addKeyDownEventListener(onKeyDown)
    Some(_ => document->WebAPI.DomElement.removeKeyDownEventListener(onKeyDown))
  }, [onKeyDown])

  let handleOnCommit = () => onCommit()

  let {?style, ?className} = styleProps(~width)
  let {style: ?headerStyle, className: ?headerClassName} = headerStyleProps()
  let {style: ?headerTitleStyle, className: ?headerTitleClassName} = headerTitleStyleProps()
  let {style: ?contentStyle, className: ?contentClassName} = contentStyleProps()
  let {style: ?footerStyle, className: ?footerClassName} = footerStyleProps()

  <Popover.Dialog ariaLabel=title ariaProps ?style ?className>
    <div style=?headerStyle className=?headerClassName>
      <span style=?headerTitleStyle className=?headerTitleClassName> {title->React.string} </span>
      {titleStartElement}
    </div>
    <div style={ReactDOM.Style.make(~position="relative", ())}>
      <div
        onScroll
        ref={contentRef->ReactDOM.Ref.domRef}
        style=?contentStyle
        className=?contentClassName>
        {children}
      </div>
      <AnimatedRender displayed={contentScrollable} animation=#fade duration=450>
        <div style=bottomGradientStyle />
      </AnimatedRender>
    </div>
    <div style=?footerStyle className=?footerClassName>
      <Button size=#tiny variation=#neutral onPress={_ => onRequestClose()}>
        {t("Discard")->React.string}
      </Button>
      <Button
        size=#tiny
        variation=#success
        loading=commitLoading
        disabled=commitDisabled
        onPress={_ => handleOnCommit()}>
        {commitButtonText->React.string}
      </Button>
    </div>
  </Popover.Dialog>
}

let make = React.memo(make)

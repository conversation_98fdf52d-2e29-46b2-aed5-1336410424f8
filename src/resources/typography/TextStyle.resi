type direction = [#ltr | #rtl]

@genType @react.component
let make: (
  ~children: React.element,
  ~align: [#start | #center | #end]=?,
  ~variation: [
    | #neutral
    | #success
    | #normal
    | #negative
    | #subdued
    | #primary
    | #secondary
    | #placeholder
  ]=?,
  ~weight: [#regular | #medium | #semibold | #strong]=?,
  ~size: FontSizes.t=?,
  ~lineHeight: Spaces.t=?,
  ~underlined: bool=?,
  ~maxLines: int=?,
  ~direction: direction=?,
  ~opacity: float=?,
) => React.element

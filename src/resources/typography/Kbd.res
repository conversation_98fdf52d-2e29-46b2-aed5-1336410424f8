open Style

let styles = merge([
  FontFaces.libreFranklinRegularStyle,
  style(
    ~height=20.->dp,
    ~lineHeight=15.,
    ~backgroundColor=Colors.neutralColor10,
    ~color=Colors.neutralColor80,
    ~fontSize=FontSizes.xsmall,
    ~paddingVertical=2.->dp,
    ~paddingHorizontal=6.->dp,
    ~borderWidth=1.,
    ~borderRadius=3.,
    ~borderColor=Colors.neutralColor15,
    (),
  ),
])

let styleFromParams = (~hovered) =>
  hovered ? style(~borderColor=Colors.neutralColor20, ()) : style()

@react.component
let make = (~children, ~hovered=false) =>
  <Text style={[styles, styleFromParams(~hovered)]->arrayStyle}> children </Text>

let make = React.memo(make)

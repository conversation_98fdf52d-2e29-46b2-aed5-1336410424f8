open Style

type direction = [#ltr | #rtl]

let styles = StyleSheet.create({
  "main": merge([
    FontFaces.libreFranklinRegularStyle,
    style(~letterSpacing=0.125, ()),
    unsafeCss({"overflowWrap": "anywhere"}),
  ]),
  "neutralView": style(~color=Colors.neutralColor90, ()),
  "normalView": style(~color=Colors.neutralColor50, ()),
  "successView": style(~color=Colors.successColor50, ()),
  "negativeView": style(~color=Colors.dangerColor50, ()),
  "primaryView": style(~color=Colors.brandColor50, ()),
  "secondaryView": style(~color=Colors.secondaryColor50, ()),
  "placeholderView": style(~color=Colors.placeholderTextColor, ()),
  "subduedView": style(~color=Colors.neutralColor50, ~fontStyle=#italic, ()),
  "alignLeft": style(~textAlign=#left, ()),
  "alignCenter": style(~textAlign=#center, ()),
  "alignRight": style(~textAlign=#right, ()),
})

let weightFromParams = (~weight) =>
  switch weight {
  | #regular => FontFaces.libreFranklinRegularStyle
  | #medium => FontFaces.libreFranklinMediumStyle
  | #semibold => FontFaces.libreFranklinSemiBoldStyle
  | #strong => FontFaces.libreFranklinBoldStyle
  }

let lineHeightFromParams = (~lineHeight, ~fontSize) =>
  switch lineHeight {
  | Some(lineHeight) => style(~lineHeight=lineHeight->Spaces.toFloat, ())
  | None =>
    switch fontSize {
    | #tiny | #xxsmall | #xsmall => style(~lineHeight=Spaces.medium, ())
    | #small | #normal => style(~lineHeight=18., ())
    | #large => style(~lineHeight=Spaces.large, ())
    | #xlarge | #huge => style()
    }
  }

let styleFromParams = (~variation, ~underlined, ~maxLines, ~direction: direction) =>
  [
    switch variation {
    | #normal => styles["normalView"]
    | #success => styles["successView"]
    | #negative => styles["negativeView"]
    | #subdued => styles["subduedView"]
    | #neutral => styles["neutralView"]
    | #primary => styles["primaryView"]
    | #secondary => styles["secondaryView"]
    | #placeholder => styles["placeholderView"]
    },
    underlined ? style(~textDecorationLine=#underline, ()) : style(),
    switch maxLines {
    | Some(0 | 1) =>
      unsafeCss({
        "overflow": "hidden",
        "whiteSpace": "nowrap",
        "textOverflow": "ellipsis",
        "direction": (direction :> string),
      })
    | Some(qty) =>
      // NOTE - multi lines clamping doesn't work on Firefox
      unsafeCss({
        "overflow": "hidden",
        "display": "-webkit-box",
        "WebkitBoxOrient": "vertical",
        "WebkitLineClamp": qty,
      })
    | None => style()
    },
  ]->arrayStyle

let alignFromParams = (~align) =>
  switch align {
  | #start => styles["alignLeft"]
  | #center => styles["alignCenter"]
  | #end => styles["alignRight"]
  }

@react.component
let make = (
  ~children,
  ~align=#start,
  ~variation=#neutral,
  ~weight=#regular,
  ~size=#normal,
  ~lineHeight=?,
  ~underlined=false,
  ~maxLines=?,
  ~direction: direction=#ltr,
  ~opacity=1.,
) =>
  // TODO - add a native HTML `title` props when `maxLines`
  <Text
    style={[
      styles["main"],
      alignFromParams(~align),
      weightFromParams(~weight),
      lineHeightFromParams(~lineHeight, ~fontSize=size),
      styleFromParams(~variation, ~underlined, ~maxLines, ~direction),
      size->FontSizes.styleFromSize,
      Style.style(~opacity, ()),
    ]->arrayStyle}>
    children
  </Text>

let make = React.memo(make)

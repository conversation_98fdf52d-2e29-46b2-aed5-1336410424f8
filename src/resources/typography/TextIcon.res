open Style

let styles = StyleSheet.create({
  "wrapper": style(~flex=1., ()),
  "view": style(~flexDirection=#row, ~justifyContent=#flexStart, ~alignItems=#center, ()),
  "hoveredView": style(~backgroundColor=Colors.neutralColor05, ()),
  "iconView": style(~marginRight=8.->dp, ()),
  "xxsmallView": style(~height=36.0->dp, ()),
  "xsmallView": style(~height=40.0->dp, ()),
  "smallView": style(~height=44.0->dp, ()),
  "normalView": style(~height=48.0->dp, ()),
  "mediumView": style(~height=52.0->dp, ()),
  "largeView": style(~height=60.0->dp, ()),
  "xlargeView": style(~height=68.0->dp, ()),
  "text": merge([FontFaces.archivoRegularStyle, style(~fontWeight=#_600, ())]),
  "lightText": style(~color=Colors.neutralColor20, ()),
  "grayText": style(~color=Colors.neutralColor100, ()),
  "darkText": style(~color=Colors.brandColor60, ()),
})

let viewStyleFromParams = (~size) =>
  [
    switch size {
    | #xsmall => styles["xsmallView"]
    | #small => styles["smallView"]
    | #large => styles["largeView"]
    | #normal
    | _ =>
      styles["normalView"]
    },
  ]->arrayStyle

let textStyleFromParams = (~size, ~disabled, ~hovered) =>
  [
    FontSizes.styleFromSize(size),
    switch (disabled, hovered) {
    | (true, _) => styles["lightText"]
    | (false, true) => styles["darkText"]
    | (false, _) => styles["grayText"]
    },
  ]->arrayStyle

let iconColorFromParams = (~disabled, ~hovered) =>
  switch (disabled, hovered) {
  | (true, _) => Colors.neutralColor20
  | (false, true) => Colors.brandColor60
  | (false, _) => Colors.neutralColor100
  }

@react.component
let make = (~children, ~size=#normal, ~noMargin=false, ~disabled=false, ~icon) => {
  let (ref, hovered) = Hover.use()

  <View ref style={styles["wrapper"]}>
    <View style={[styles["view"], noMargin ? style() : viewStyleFromParams(~size)]->arrayStyle}>
      <View style={styles["iconView"]}>
        <Icon fill={iconColorFromParams(~disabled, ~hovered)} name=icon />
      </View>
      <Text style={[styles["text"], textStyleFromParams(~size, ~disabled, ~hovered)]->arrayStyle}>
        children
      </Text>
    </View>
  </View>
}

let make = React.memo(make)

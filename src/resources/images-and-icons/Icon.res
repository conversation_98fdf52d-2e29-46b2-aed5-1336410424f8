open! Svg

type t = [
  | #toggle_list_arrows
  | #printer
  | #printer_bold
  | #printer_locked
  | #burger
  | #search
  | #back
  | #alert_tip
  | #info_tip
  | #settings
  | #settings_bold
  | #ticket_bold
  | #switch_bold
  | #arrow_up_overlay
  | #arrow_up_light
  | #arrow_down_light
  | #arrow_left_light
  | #arrow_right_light
  | #queue_arrow_left_light
  | #queue_arrow_right_light
  | #queue_arrow_up_light
  | #queue_arrow_down_light
  | #double_arrow_left_light
  | #double_arrow_right_light
  | #export_light
  | #import_light
  | #edit_light
  | #help_bold
  | #notification_bold
  | #plus_light
  | #bordered_plus_light
  | #analytics_bold
  | #close_light
  | #close_medium
  | #close_bold
  | #oval
  | #sales_bold
  | #products_bold
  | #contacts_bold
  | #filter_light
  | #delete_light
  | #more_x_bold
  | #more_y_light
  | #inner_oval
  | #calendar
  | #clipboard
  | #tick_light
  | #tick_medium
  | #tick_bold
  | #book_bold
  | #download
  | #export_download
  | #external_link
  | #cloud_save
  | #customer_support
  | #filter
  | #reset
  | #danger
  | #warning
  | #printing_label
]

@react.component
let make = (
  ~name,
  ~fill=Colors.neutralColor90,
  ~size=20.,
  ~filter=?,
  ~stroke=?,
  ~strokeDasharray=?,
) =>
  <Svg width={size->Float.toString} height={size->Float.toString} viewBox="0 0 20 20">
    {switch name {
    | #toggle_list_arrows =>
      <Path
        d="M5.64714612,13.3542515 L10.0158438,17.7057133 L14.3528539,13.385814 C14.5485016,13.1909382 14.5491273,12.8743563 14.3542515,12.6787086 C14.1593757,12.4830609 13.8427938,12.4824352 13.6471461,12.677311 L10.015,16.294 L6.35285388,12.6457485 C6.17894481,12.4725255 5.90948281,12.453773 5.71488188,12.5891539 L5.64574848,12.6471461 C5.45087266,12.8427938 5.45149841,13.1593757 5.64714612,13.3542515 Z M5.64714612,6.64574848 L10.0158438,2.29428672 L14.3528539,6.61418597 C14.5485016,6.80906179 14.5491273,7.12564366 14.3542515,7.32129137 C14.1593757,7.51693908 13.8427938,7.51756482 13.6471461,7.322689 L10.015,3.706 L6.35285388,7.35425152 C6.17894481,7.52747447 5.90948281,7.54622705 5.71488188,7.41084609 L5.64574848,7.35285388 C5.45087266,7.15720617 5.45149841,6.8406243 5.64714612,6.64574848 Z"
        fill
        ?stroke
      />
    | #printer =>
      <Path
        d="M14.1515152,2.5 C14.9799423,2.5 15.6515152,3.17157288 15.6515152,4 L15.651,6.318 L15.6969697,6.31818182 C17.0776816,6.31818182 18.1969697,7.43746994 18.1969697,8.81818182 L18.1969697,13.530303 C18.1969697,14.0825878 17.7492544,14.530303 17.1969697,14.530303 L15.651,14.53 L15.6515152,16 C15.6515152,16.8284271 14.9799423,17.5 14.1515152,17.5 L5.54545455,17.5 C4.71702742,17.5 4.04545455,16.8284271 4.04545455,16 L4.045,14.53 L2.5,14.530303 C1.94771525,14.530303 1.5,14.0825878 1.5,13.530303 L1.5,8.81818182 C1.5,7.49269842 2.53153594,6.40814315 3.83562431,6.3234995 L4.045,6.318 L4.04545455,4 C4.04545455,3.17157288 4.71702742,2.5 5.54545455,2.5 L14.1515152,2.5 Z M14.1515152,12.4090909 L5.54545455,12.4090909 C5.26931217,12.4090909 5.04545455,12.6329485 5.04545455,12.9090909 L5.04545455,16 C5.04545455,16.2761424 5.26931217,16.5 5.54545455,16.5 L14.1515152,16.5 C14.4276575,16.5 14.6515152,16.2761424 14.6515152,16 L14.6515152,12.9090909 C14.6515152,12.6329485 14.4276575,12.4090909 14.1515152,12.4090909 Z M15.6969697,7.31818182 L4,7.31818182 C3.17157288,7.31818182 2.5,7.98975469 2.5,8.81818182 L2.5,13.530303 L4.045,13.53 L4.04545455,12.9090909 C4.04545455,12.0806638 4.71702742,11.4090909 5.54545455,11.4090909 L14.1515152,11.4090909 C14.9799423,11.4090909 15.6515152,12.0806638 15.6515152,12.9090909 L15.651,13.53 L17.1969697,13.530303 L17.1969697,8.81818182 C17.1969697,7.98975469 16.5253968,7.31818182 15.6969697,7.31818182 Z M15.1515152,8.72727273 C15.5029691,8.72727273 15.7878788,9.01218243 15.7878788,9.36363636 C15.7878788,9.7150903 15.5029691,10 15.1515152,10 C14.8000612,10 14.5151515,9.7150903 14.5151515,9.36363636 C14.5151515,9.01218243 14.8000612,8.72727273 15.1515152,8.72727273 Z M14.1515152,3.5 L5.54545455,3.5 C5.26931217,3.5 5.04545455,3.72385763 5.04545455,4 L5.04545455,6.318 L14.6515152,6.318 L14.6515152,4 C14.6515152,3.75454011 14.47464,3.55039163 14.2413908,3.50805567 L14.1515152,3.5 Z"
        fill
        ?stroke
      />
    | #printer_bold =>
      <Path
        d="M14.152 2a2 2 0 0 1 2 2v1.852a3 3 0 0 1 2.545 2.966v4.712a1.5 1.5 0 0 1-1.5 1.5h-1.046V16a2 2 0 0 1-2 2H5.546a2 2 0 0 1-2-2v-.97H2.5a1.5 1.5 0 0 1-1.5-1.5V8.818a3 3 0 0 1 2.545-2.966V4a2 2 0 0 1 2-2h8.607zm0 10.91H5.545V16h8.607v-3.09zm1.545-5.092H4a1 1 0 0 0-1 1v4.212h.545v-.12a2 2 0 0 1 2-2h8.607a2 2 0 0 1 2 2l-.001.12h.545l.001-4.212a1 1 0 0 0-.883-.993l-.117-.007zm-.545.91a.636.636 0 1 1 0 1.272.636.636 0 0 1 0-1.273zm-1-4.728H5.544l.001 1.818h8.607V4z"
        fill
        ?stroke
      />
    | #printer_locked =>
      <Path
        d="M10.799294,1.99859892 C10.732969,2.24673452 10.686771,2.50310845 10.6625578,2.76600888 L10.661,2.788 L10.5896088,2.85117247 C10.2438219,3.17221367 9.96580087,3.56259677 9.77715204,3.99900291 L5.54445455,4 L5.54545455,5.818 L9.5,5.81759892 L9.5,7.81759892 L4,7.81818182 C3.44771525,7.81818182 3,8.26589707 3,8.81818182 L3,13.0301818 L3.545,13.03 L3.54545455,12.9090909 C3.54545455,11.8045214 4.44088505,10.9090909 5.54545455,10.9090909 L11.0097801,10.9103273 C11.5630352,11.2829858 12.2304055,11.5 12.9458128,11.5 L15.5705591,11.4997242 C15.9296482,11.8612635 16.1515152,12.3592673 16.1515152,12.9090909 L16.151,13.03 L16.696,13.0301818 L16.695,11.4995989 L17.91133,11.5 C18.1812205,11.5 18.4442742,11.4691144 18.6969435,11.4106382 L18.6969697,13.530303 C18.6969697,14.3587302 18.0253968,15.030303 17.1969697,15.030303 L16.151,15.03 L16.1515152,16 C16.1515152,17.1045695 15.2560847,18 14.1515152,18 L5.54545455,18 C4.44088505,18 3.54545455,17.1045695 3.54545455,16 L3.545,15.03 L2.5,15.030303 C1.67157288,15.030303 1,14.3587302 1,13.530303 L1,8.81818182 C1,7.31600882 2.10406446,6.0717039 3.54499653,5.8524639 L3.54545455,4 C3.54545455,2.8954305 4.44088505,2 5.54545455,2 L10.799294,1.99859892 Z M14.1515152,12.9090909 L5.54545455,12.9090909 L5.54545455,16 L14.1515152,16 L14.1515152,12.9090909 Z M15.4285714,0.5 C16.9629227,0.5 18.2142857,1.70228991 18.2142857,3.19607843 L18.214619,3.959798 C18.865246,4.09582743 19.3571429,4.66055221 19.3571429,5.34285714 L19.3571429,8.08571429 C19.3571429,8.87066686 18.7061086,9.5 17.91133,9.5 L12.9458128,9.5 C12.1510343,9.5 11.5,8.87066686 11.5,8.08571429 L11.5,5.34285714 C11.5,4.66082606 11.9915021,4.09628076 12.6417405,3.959962 L12.6428571,3.19607843 C12.6428571,1.70228991 13.8942201,0.5 15.4285714,0.5 Z M15.4285714,1.5 C14.4377526,1.5 13.6428571,2.26372308 13.6428571,3.19607843 L13.642,3.928 L17.214,3.928 L17.2142857,3.19607843 C17.2142857,2.26372308 16.4193903,1.5 15.4285714,1.5 Z"
        fill
        ?stroke
      />
    | #inner_oval => <Circle fill cx="10" cy="10" r="4" />
    | #book_bold =>
      <Path
        d="M10.023 4.468c2.77-2.379 5.89-3.05 9.242-1.961a1 1 0 0 1 .69.907l.005.134.03 2.738.01 4.706-.018 3.412-.026 1.66a1 1 0 0 1-1.202.955c-3.017-.623-5.574-.082-7.752 1.62l-.161.103a1.858 1.858 0 0 1-1.776-.01 1 1 0 0 1-.21-.152c-2.027-1.906-4.51-2.433-7.584-1.577a1 1 0 0 1-1.268-.963L0 4.9l.002-1.444a1 1 0 0 1 .706-.954c3.364-1.033 6.46-.364 9.18 1.96l.07.061Zm7.783-.29c-2.484-.545-4.738.156-6.861 2.15a.632.632 0 0 1 .008.053l.007.117v9.75c2.005-1.193 4.263-1.664 6.743-1.413l.274.03.01-.925.01-2.098.002-2.86-.008-2.5-.019-2.264Zm-8.83 2.15c-2.08-1.952-4.325-2.65-6.83-2.133l-.145.032v10.559l.106-.019c2.575-.435 4.875.012 6.852 1.342v-9.61l.008-.118Zm-4.825.566c1.358 0 2.166.186 3.126.972a1 1 0 0 1-1.266 1.547c-.523-.427-.921-.52-1.86-.52a1 1 0 1 1 0-2Z"
        fill
        ?stroke
      />
    | #burger =>
      <Path
        d="M1.90909091,4 C1.35680616,4 0.909090909,3.55228475 0.909090909,3 C0.909090909,2.44771525 1.35680616,2 1.90909091,2 L18.2727273,2 C18.825012,2 19.2727273,2.44771525 19.2727273,3 C19.2727273,3.55228475 18.825012,4 18.2727273,4 L1.90909091,4 Z M1.90909091,11 C1.35680616,11 0.909090909,10.5522847 0.909090909,10 C0.909090909,9.44771525 1.35680616,9 1.90909091,9 L18.2727273,9 C18.825012,9 19.2727273,9.44771525 19.2727273,10 C19.2727273,10.5522847 18.825012,11 18.2727273,11 L1.90909091,11 Z M1.90909091,18 C1.35680616,18 0.909090909,17.5522847 0.909090909,17 C0.909090909,16.4477153 1.35680616,16 1.90909091,16 L18.2727273,16 C18.825012,16 19.2727273,16.4477153 19.2727273,17 C19.2727273,17.5522847 18.825012,18 18.2727273,18 L1.90909091,18 Z"
        fill
        ?stroke
      />
    | #search =>
      <Path
        d="M9.5 2.5a7 7 0 015.292 11.583l3.062 3.064a.5.5 0 01-.708.706l-3.061-3.063A7 7 0 119.5 2.5zm0 1a6 6 0 100 12 6 6 0 000-12z"
        fill
        ?stroke
      />
    | #back =>
      <Path
        d="M13.684 15.346a1 1 0 11-1.424 1.406l-5.972-6.05a1 1 0 01.003-1.407l5.972-6a1 1 0 111.418 1.41l-5.273 5.298 5.276 5.343z"
        fill
        ?stroke
      />
    | #alert_tip =>
      <Path
        d="M 9.997 0.004 C 17.693 0.004 22.502 8.334 18.654 14.998 C 16.869 18.091 13.568 19.996 9.997 19.996 L 1.539 19.996 C 0.69 19.996 0.001 19.307 0.001 18.458 L 0.001 10 C 0.001 4.479 4.476 0.004 9.997 0.004 Z M 9.997 12.307 C 9.573 12.307 9.228 12.651 9.228 13.076 L 9.228 14.614 L 9.241 14.752 C 9.347 15.335 10.042 15.584 10.494 15.201 C 10.668 15.055 10.766 14.84 10.766 14.614 L 10.766 13.076 L 10.754 12.937 C 10.688 12.571 10.368 12.307 9.997 12.307 Z M 9.997 4.617 C 9.573 4.617 9.228 4.962 9.228 5.386 L 9.228 10 L 9.241 10.138 C 9.347 10.721 10.042 10.97 10.494 10.587 C 10.668 10.441 10.766 10.226 10.766 10 L 10.766 5.386 L 10.754 5.248 C 10.688 4.882 10.368 4.617 9.997 4.617 Z"
        fill
        ?stroke
      />
    | #info_tip =>
      <Path
        d="M 9.992 0.001 C 17.688 0.001 22.5 8.334 18.651 14.999 C 16.865 18.094 13.564 19.999 9.992 19.999 L 1.532 19.999 C 0.681 19.999 -0.006 19.311 -0.006 18.461 L -0.006 10 C -0.006 4.479 4.47 0.001 9.992 0.001 Z M 9.992 8.462 L 8.454 8.462 L 8.316 8.474 C 7.733 8.58 7.482 9.277 7.865 9.729 C 8.011 9.902 8.226 10.002 8.454 10 L 9.223 10 L 9.223 13.846 L 8.454 13.846 L 8.316 13.858 C 7.733 13.964 7.482 14.661 7.865 15.113 C 8.011 15.286 8.226 15.386 8.454 15.384 L 11.531 15.384 L 11.669 15.372 C 12.251 15.267 12.501 14.57 12.118 14.118 C 11.972 13.946 11.757 13.846 11.531 13.846 L 10.762 13.846 L 10.762 9.231 L 10.749 9.092 C 10.682 8.728 10.363 8.462 9.992 8.462 Z M 9.992 4.616 C 9.566 4.616 9.223 4.961 9.223 5.385 L 9.223 6.923 L 9.236 7.062 C 9.34 7.645 10.037 7.896 10.489 7.513 C 10.662 7.366 10.762 7.151 10.762 6.923 L 10.762 5.385 L 10.749 5.247 C 10.682 4.882 10.363 4.616 9.992 4.616 Z"
        fill
        ?stroke
      />
    | #settings =>
      <Path
        fillRule="evenodd"
        d="M16.1142 10.51L16.7442 10.27V10.24C17.2542 10.08 17.5742 9.42 17.4642 8.79C17.3542 8.16 16.8242 7.64 16.3142 7.68C15.7542 7.71 15.3142 7.51 15.0942 7.14C14.8742 6.77 14.9242 6.29 15.2242 5.82C15.4942 5.38 15.3242 4.67 14.8342 4.26C14.3542 3.86 13.6342 3.81 13.2542 4.16C12.8442 4.54 12.3842 4.68 11.9742 4.53C11.5742 4.38 11.3042 3.98 11.2342 3.43C11.1742 2.92 10.5842 2.49 9.95423 2.49C9.32423 2.49 8.73423 2.93 8.67423 3.45C8.61423 4 8.36423 4.29 8.17423 4.43C7.95423 4.6 7.68423 4.65 7.40423 4.6C7.14423 4.55 6.90423 4.41 6.66423 4.19C6.57423 4.11 6.46423 4.05 6.34423 4.01C6.15423 3.95 5.93423 3.94 5.71423 3.99C5.48423 4.04 5.27423 4.15 5.09423 4.3C4.98423 4.39 4.89423 4.49 4.81423 4.61C4.72423 4.76 4.65423 4.92 4.61423 5.09C4.54423 5.38 4.58423 5.67 4.71423 5.87C5.32423 6.83 4.77423 7.36 4.59423 7.5C4.35423 7.68 4.02423 7.77 3.64423 7.75C3.45423 7.73 3.22423 7.82 3.03423 7.97C2.76423 8.18 2.57423 8.51 2.51423 8.86C2.41423 9.49 2.73423 10.16 3.22423 10.31C3.75423 10.47 4.10423 10.81 4.18423 11.24C4.26423 11.67 4.05423 12.1 3.61423 12.44C3.36423 12.63 3.22423 13.02 3.26423 13.43C3.28423 13.64 3.35423 13.85 3.45423 14.03C3.54423 14.19 3.67423 14.34 3.81423 14.45C3.93423 14.55 4.07423 14.62 4.20423 14.67C4.33423 14.72 4.45423 14.74 4.59423 14.74C4.70423 14.74 4.81423 14.72 4.90423 14.68C5.10423 14.6 5.29423 14.55 5.47423 14.54C5.84423 14.52 6.17423 14.65 6.38423 14.92C6.54423 15.12 6.70423 15.48 6.57423 16.07C6.46423 16.57 6.87423 17.18 7.46423 17.4C8.05423 17.62 8.75423 17.41 8.98423 16.95C9.23423 16.45 9.63422 16.16 10.0642 16.16C10.4942 16.16 10.8842 16.44 11.1342 16.94C11.3642 17.4 12.0542 17.6 12.6542 17.38C13.2442 17.17 13.6542 16.55 13.5342 16.04C13.4142 15.48 13.5342 15.01 13.8642 14.73C14.1842 14.46 14.6642 14.43 15.1742 14.64C15.6342 14.84 16.2942 14.54 16.6142 13.99C16.7442 13.74 17.0542 13.66 17.2942 13.8C17.5342 13.94 17.6242 14.24 17.4842 14.48C16.9042 15.49 15.7242 15.96 14.7842 15.56C14.6252 15.4893 14.5365 15.4967 14.5041 15.4994C14.4998 15.4997 14.4966 15.5 14.4942 15.5C14.4842 15.52 14.4542 15.62 14.5042 15.82C14.7342 16.81 14.0542 17.93 12.9942 18.32C11.9342 18.72 10.7042 18.3 10.2442 17.4C10.1542 17.23 10.0742 17.17 10.0542 17.17C10.0542 17.1725 10.0486 17.1781 10.0388 17.188C10.0092 17.2175 9.94173 17.285 9.87423 17.42C9.52423 18.09 8.74423 18.5 7.93423 18.5C7.65423 18.5 7.37423 18.45 7.10423 18.35C6.04423 17.96 5.36423 16.84 5.58423 15.86C5.62423 15.67 5.59423 15.58 5.58423 15.56C5.47423 15.57 5.39423 15.58 5.28423 15.63C5.07423 15.72 4.84423 15.77 4.59423 15.77C4.35423 15.79 4.09423 15.73 3.85423 15.64C3.61423 15.56 3.39423 15.43 3.18423 15.27C2.93423 15.07 2.73423 14.83 2.57423 14.56C2.39423 14.25 2.28423 13.9 2.25423 13.55C2.18423 12.79 2.46423 12.07 2.99423 11.67C3.15423 11.55 3.19423 11.46 3.19423 11.44C3.19423 11.43 3.12423 11.35 2.92423 11.29C1.96423 10.98 1.34423 9.83 1.53423 8.71C1.63423 8.11 1.96423 7.57 2.42423 7.2C2.80423 6.89 3.25423 6.74 3.70423 6.76C3.87423 6.77 3.96423 6.74 3.99423 6.72C4.00423 6.72 4.00423 6.63 3.87423 6.42C3.60423 5.99 3.51423 5.44 3.64423 4.88C3.70423 4.6 3.82423 4.33 3.98423 4.08C4.11423 3.88 4.27423 3.7 4.45423 3.55C4.75423 3.3 5.11423 3.12 5.50423 3.03C5.89423 2.95 6.28423 2.96 6.64423 3.07C6.92423 3.16 7.15423 3.29 7.35423 3.48C7.48423 3.6 7.57423 3.63 7.59423 3.63C7.60423 3.62 7.67423 3.53 7.69423 3.33C7.81423 2.32 8.83422 1.5 9.96422 1.5C11.0942 1.5 12.1142 2.32 12.2342 3.32C12.2642 3.52 12.3142 3.6 12.3342 3.61C12.3364 3.60784 12.3423 3.60522 12.3517 3.60102C12.3859 3.58577 12.4666 3.54978 12.5842 3.44C13.3242 2.74 14.6242 2.78 15.4942 3.51C16.3542 4.24 16.6142 5.52 16.0742 6.37C15.9642 6.54 15.9642 6.64 15.9642 6.66C15.9742 6.67 16.0542 6.71 16.2642 6.7C17.2542 6.64 18.2442 7.52 18.4442 8.64C18.6442 9.78 18.0342 10.91 17.0642 11.21L16.4642 11.44C16.2142 11.54 15.9242 11.41 15.8242 11.15C15.7242 10.9 15.8542 10.61 16.1142 10.51ZM7.71425 7.35001C8.34425 6.81001 9.15425 6.51001 9.98425 6.51001C11.9143 6.51001 13.4843 8.08001 13.4843 10.01C13.4843 11.94 11.9143 13.51 9.98425 13.51C9.15425 13.51 8.34425 13.21 7.71425 12.67C6.93425 12.01 6.48425 11.03 6.48425 10.01C6.48425 8.99001 6.93425 8.02001 7.71425 7.35001ZM8.36425 11.91C8.81425 12.3 9.38425 12.51 9.98425 12.51C11.3643 12.51 12.4843 11.39 12.4843 10.01C12.4843 8.63001 11.3643 7.51001 9.98425 7.51001C9.39425 7.51001 8.81425 7.72001 8.36425 8.11001C7.80425 8.58001 7.48425 9.28001 7.48425 10.01C7.48425 10.74 7.80425 11.43 8.36425 11.91Z"
        fill
        ?stroke
      />
    | #settings_bold =>
      <Path
        d="M14.6626029,10.0354071 C14.6626029,12.4116918 12.7364876,14.3378071 10.3602029,14.3378071 C7.98391814,14.3378071 6.05780289,12.4116918 6.05780289,10.0354071 C6.05780289,7.65912231 7.98391814,5.73300706 10.3602029,5.73300706 C12.7364876,5.73300706 14.6626029,7.65912231 14.6626029,10.0354071 Z M12.6626029,10.0354071 C12.6626029,8.76369181 11.6319181,7.73300706 10.3602029,7.73300706 C9.08848764,7.73300706 8.05780289,8.76369181 8.05780289,10.0354071 C8.05780289,11.3071223 9.08848764,12.3378071 10.3602029,12.3378071 C11.6319181,12.3378071 12.6626029,11.3071223 12.6626029,10.0354071 Z M16.7585849,2.30868426 C17.7742363,3.16091506 18.1679475,4.59803275 17.6703126,5.71877136 C18.9059173,5.83916564 19.9712347,6.90054852 20.2032493,8.21581229 C20.4552213,9.65205277 19.6775904,11.1451041 18.4169887,11.5316849 L17.7144946,11.7928254 C17.1968204,11.9852622 16.6211618,11.7216052 16.428725,11.203931 C16.2362883,10.6862568 16.4999453,10.1105982 17.0176194,9.91816144 L17.7741819,9.63866116 C18.0644424,9.54925605 18.320418,9.05778282 18.2334972,8.56233147 C18.1462477,8.06772507 17.7317802,7.69195049 17.4153304,7.7079618 C15.7607845,7.78958606 14.913455,6.35607122 15.8139724,4.95949033 C15.9784325,4.70547277 15.8584826,4.16422254 15.4735783,3.84124989 C15.0881209,3.51829909 14.52919,3.4966878 14.297531,3.71194932 C13.0825394,4.83873817 11.5106302,4.28601962 11.3049378,2.63824492 C11.2674367,2.33742082 10.8274358,2 10.3230452,2 C9.82168276,2 9.37937469,2.3428934 9.34002281,2.65518965 C9.13488124,4.30073391 7.57404655,4.88755575 6.35831631,3.75674726 C6.13558235,3.54995207 5.58205556,3.57406371 5.1951004,3.89884989 C4.81104736,4.22062405 4.69285453,4.76748381 4.86445616,5.03287798 C5.76378039,6.42594884 4.94558111,7.87867001 3.28789699,7.79377414 C2.98355801,7.77861863 2.57469074,8.15295085 2.48729638,8.64837595 C2.40055155,9.14282148 2.66165659,9.63832763 2.96169787,9.7310995 C4.54750064,10.2185298 4.85353129,11.8581123 3.52884085,12.861124 C3.28754719,13.0437246 3.21515377,13.5933591 3.46736588,14.0293681 C3.71766405,14.4630645 4.23633352,14.6745811 4.52712693,14.5518765 C6.05512512,13.9080447 7.34298047,14.965959 6.97217317,16.5854487 C6.90446626,16.8811566 7.20233576,17.3487909 7.67551273,17.5210134 C8.14707972,17.6926499 8.68054447,17.521634 8.82361831,17.242581 C9.57795955,15.764778 11.2461377,15.7462117 12.0037168,17.2256138 C12.1421593,17.4959648 12.6703882,17.6626735 13.1437475,17.4903848 C13.615682,17.3188247 13.9138193,16.8455689 13.8430889,16.536962 C13.4749105,14.9209452 14.7392894,13.8363974 16.2691177,14.4818287 C16.5501452,14.6002208 17.0621603,14.3883704 17.3147528,13.9517049 C17.591292,13.4736416 18.2030182,13.3102742 18.6810815,13.5868134 C19.1591448,13.8633527 19.3225123,14.4750789 19.045973,14.9531421 C18.3823789,16.1003209 17.0379839,16.7363332 15.8463107,16.4421817 C15.9425836,17.6802142 15.0818292,18.9138946 13.8274186,19.3699043 C12.582475,19.8230271 11.1432257,19.4463711 10.4198047,18.4557453 C9.69737811,19.4651608 8.24510076,19.8566824 6.99147186,19.4003984 C5.74600154,18.9470838 4.88486068,17.7324237 4.96822828,16.508059 C3.7664243,16.8170003 2.40181585,16.1842266 1.73564689,15.0299421 C1.07186207,13.8824337 1.19312482,12.3975512 2.04372337,11.5134951 C0.924730718,10.9773395 0.28696084,9.61617971 0.517544218,8.30185677 C0.747783098,6.99666152 1.79376539,5.93874213 3.01462023,5.8071631 C2.5011345,4.67683987 2.88766951,3.22290968 3.90998519,2.36637538 C4.92524638,1.51422672 6.4063556,1.37551764 7.42569503,2.05901757 C7.75998687,0.863996048 8.98967572,-2.13162821e-14 10.3230452,-2.13162821e-14 C11.648592,-2.13162821e-14 12.8732381,0.84597023 13.214155,2.02434844 C14.2378147,1.32266092 15.7352793,1.45131973 16.7585849,2.30868426 Z"
        fill
        ?stroke
      />
    | #ticket_bold =>
      <Path
        d="M6.20171517,18.6701377 C5.80199647,19.1239435 5.09407688,19.1218662 4.69702834,18.6657224 L3.38338138,17.1565569 C3.27452579,17.0314996 3.19847231,16.8813389 3.16206515,16.7195879 C3.13620572,16.6046988 3.093026,16.3962975 3.03835997,16.1045038 C2.94833079,15.6239504 2.85855274,15.0804624 2.77488306,14.4840995 C2.37094614,11.6050044 2.24902858,8.62210851 2.58217057,5.81406631 C2.71070733,4.73063441 2.9059347,3.7009572 3.17392194,2.73314224 C3.29387734,2.29993271 3.68814725,2 4.13765787,2 C6.70343695,2 6.70343695,2 11.4530106,2 L17.2995916,2 C17.9313061,2 18.4047533,2.57847789 18.2798499,3.1977213 C17.4616179,7.25433395 17.4616179,11.143879 17.9520906,14.5333346 C18.0231084,15.02411 18.0989113,15.4579868 18.1742805,15.8297477 C18.2184355,16.0475436 18.2517253,16.1929321 18.2689116,16.2607065 C18.3495266,16.5786115 18.2691331,16.9158393 18.0537655,17.1631834 L16.745371,18.6658403 C16.3483349,19.1218258 15.6406009,19.1239043 15.2408934,18.6702587 L14.6713791,18.023892 L14.1117572,18.6661207 C13.7144213,19.1221081 13.0063978,19.1237494 12.606952,18.6696092 L12.0373294,18.0219905 L11.470533,18.6684333 C11.0722876,19.1226402 10.3653405,19.1227603 9.96694067,18.6686888 L9.40441639,18.0275583 L8.85177208,18.6645134 C8.4548367,19.1220048 7.74554696,19.1243904 7.3455432,18.6695794 L6.77430377,18.0200705 L6.20171517,18.6701377 Z M11.4530106,4 C7.23799914,4 6.76360166,4 4.91691377,4 C4.76866564,4.65156658 4.65291853,5.33595822 4.56824233,6.04969094 C4.25819556,8.66306435 4.37346716,11.4833569 4.75548489,14.2062211 C4.83477253,14.7713506 4.91959162,15.2848187 5.00415958,15.7362214 C5.02614979,15.8535997 5.04586724,15.9550721 5.06282075,16.0397636 L5.45575918,16.4911856 L6.02445633,15.8455364 C6.42281438,15.3932753 7.12774689,15.3935375 7.52576843,15.8460947 L8.09133266,16.4891508 L8.64487498,15.8511607 C9.04212278,15.3933092 9.75210739,15.3913421 10.1518862,15.8469853 L10.718367,16.4926252 L11.2842354,15.8472408 C11.6821564,15.3934039 12.3883885,15.3928513 12.7870192,15.8460649 L13.3543082,16.4910306 L13.9132753,15.8495534 C14.3103827,15.3938283 15.0178999,15.3918819 15.4175086,15.8454154 L15.9867398,16.4914608 L16.2146742,16.2296842 C16.1314673,15.8192644 16.049211,15.3484498 15.972707,14.8197622 C15.5001695,11.5542494 15.4561279,7.86677158 16.0990417,4 L11.4530106,4 Z M7.99906525,8.55555556 C7.4467805,8.55555556 6.99906525,8.10784031 6.99906525,7.55555556 C6.99906525,7.00327081 7.4467805,6.55555556 7.99906525,6.55555556 L12.3291712,6.55555556 C12.881456,6.55555556 13.3291712,7.00327081 13.3291712,7.55555556 C13.3291712,8.10784031 12.881456,8.55555556 12.3291712,8.55555556 L7.99906525,8.55555556 Z M7.99906525,12.5555556 C7.4467805,12.5555556 6.99906525,12.1078403 6.99906525,11.5555556 C6.99906525,11.0032708 7.4467805,10.5555556 7.99906525,10.5555556 L12.3291712,10.5555556 C12.881456,10.5555556 13.3291712,11.0032708 13.3291712,11.5555556 C13.3291712,12.1078403 12.881456,12.5555556 12.3291712,12.5555556 L7.99906525,12.5555556 Z"
        fill
        ?stroke
      />
    | #switch_bold =>
      <Path
        d="M2.96761427,14.7494526 C2.53688385,14.6380474 2.21875,14.2468008 2.21875,13.78125 C2.21875,13.2932753 2.56826872,12.886935 3.03061531,12.7989201 C3.11514044,12.7683469 3.23961776,12.7185137 3.39280967,12.6484353 C3.75181257,12.4842075 4.111155,12.2765522 4.44196098,12.0256337 C5.14546181,11.4920238 5.61490243,10.8455171 5.77981604,10.0725855 C5.89505828,9.53245801 6.42634039,9.18802049 6.96646785,9.30326272 C7.5065953,9.41850496 7.85103283,9.94978707 7.73579059,10.4899145 C7.5480086,11.3700277 7.13872898,12.1315408 6.56224011,12.78125 L17.28125,12.78125 C17.8335347,12.78125 18.28125,13.2289653 18.28125,13.78125 C18.28125,14.3335347 17.8335347,14.78125 17.28125,14.78125 L6.45707599,14.78125 C7.0891192,15.4473607 7.53579818,16.2024433 7.7323613,17.0571145 C7.85614793,17.595348 7.52017234,18.1320214 6.98193883,18.255808 C6.44370531,18.3795946 5.90703195,18.043619 5.78324533,17.5053855 C5.62974618,16.8379597 5.15034112,16.2031798 4.42428686,15.6325827 C4.08237278,15.3638767 3.71117339,15.1294152 3.3400621,14.9331733 C3.18539576,14.8513864 3.0578543,14.7899617 2.96761427,14.7494526 Z M17.674582,5.36157435 C18.0313205,5.514345 18.28125,5.86861042 18.28125,6.28125 C18.28125,6.72685943 17.9897863,7.10439385 17.5870994,7.23361269 C17.4927793,7.28173289 17.3604111,7.35315148 17.2003848,7.44620239 C16.8213511,7.66660017 16.4423885,7.92112714 16.0930427,8.20301231 C15.350833,8.80189726 14.8621712,9.43003616 14.7177643,10.0192367 C14.5862961,10.5556456 14.0448747,10.883915 13.5084658,10.7524468 C12.9720568,10.6209786 12.6437875,10.0795572 12.7752557,9.54314827 C12.9785617,8.71363082 13.4542542,7.96472797 14.1306798,7.28125 L3.21875,7.28125 C2.66646525,7.28125 2.21875,6.83353475 2.21875,6.28125 C2.21875,5.72896525 2.66646525,5.28125 3.21875,5.28125 L14.4335921,5.28125 C13.8054968,4.64951906 13.2755908,3.98028942 12.8759537,3.27326139 C12.6041916,2.79246645 12.7736461,2.18239838 13.2544411,1.91063622 C13.735236,1.63887406 14.3453041,1.80832861 14.6170662,2.28912355 C14.9260644,2.83579557 15.3568754,3.37704247 15.8793152,3.89861395 C16.2976467,4.31625018 16.7491251,4.69650589 17.200274,5.02953677 C17.3977595,5.17531743 17.5603076,5.28707645 17.674582,5.36157435 Z"
        fill
        ?stroke
      />
    | #arrow_up_overlay =>
      <Path
        d="M 9.278 6.281 Q 9.989 5.718 10.7 6.281 L 19.741 13.437 Q 20.452 14 19.029 14 L 0.949 14 Q -0.474 14 0.237 13.437 Z"
        fill
        ?filter
        ?stroke
        ?strokeDasharray
      />
    | #arrow_up_light =>
      <Path
        transform="rotate(270 10 10.5)"
        d="M6.66741951,4.33258049 C6.47215737,4.13731834 6.47215737,3.82073585 6.66741951,3.62547371 C6.86268166,3.43021156 7.17926415,3.43021156 7.37452629,3.62547371 L13.3745263,9.62547371 C13.5693077,9.82025508 13.5698574,10.1358881 13.3757557,10.3313468 L7.37575568,16.3732926 C7.18117487,16.5692337 6.8645943,16.5703365 6.66865318,16.3757557 C6.47271207,16.1811749 6.47160932,15.8645943 6.66619013,15.6686532 L12.3150955,9.98025649 L6.66741951,4.33258049 Z"
        fill
        ?stroke
      />
    | #arrow_down_light =>
      <Path
        transform="rotate(90 10 10.5)"
        d="M6.66741951,4.33258049 C6.47215737,4.13731834 6.47215737,3.82073585 6.66741951,3.62547371 C6.86268166,3.43021156 7.17926415,3.43021156 7.37452629,3.62547371 L13.3745263,9.62547371 C13.5693077,9.82025508 13.5698574,10.1358881 13.3757557,10.3313468 L7.37575568,16.3732926 C7.18117487,16.5692337 6.8645943,16.5703365 6.66865318,16.3757557 C6.47271207,16.1811749 6.47160932,15.8645943 6.66619013,15.6686532 L12.3150955,9.98025649 L6.66741951,4.33258049 Z"
        fill
        ?stroke
      />
    | #arrow_left_light =>
      <Path
        transform="rotate(180 10 10)"
        d="M6.66741951,4.33258049 C6.47215737,4.13731834 6.47215737,3.82073585 6.66741951,3.62547371 C6.86268166,3.43021156 7.17926415,3.43021156 7.37452629,3.62547371 L13.3745263,9.62547371 C13.5693077,9.82025508 13.5698574,10.1358881 13.3757557,10.3313468 L7.37575568,16.3732926 C7.18117487,16.5692337 6.8645943,16.5703365 6.66865318,16.3757557 C6.47271207,16.1811749 6.47160932,15.8645943 6.66619013,15.6686532 L12.3150955,9.98025649 L6.66741951,4.33258049 Z"
        fill
        ?stroke
      />
    | #arrow_right_light =>
      <Path
        d="M6.66741951,4.33258049 C6.47215737,4.13731834 6.47215737,3.82073585 6.66741951,3.62547371 C6.86268166,3.43021156 7.17926415,3.43021156 7.37452629,3.62547371 L13.3745263,9.62547371 C13.5693077,9.82025508 13.5698574,10.1358881 13.3757557,10.3313468 L7.37575568,16.3732926 C7.18117487,16.5692337 6.8645943,16.5703365 6.66865318,16.3757557 C6.47271207,16.1811749 6.47160932,15.8645943 6.66619013,15.6686532 L12.3150955,9.98025649 L6.66741951,4.33258049 Z"
        fill
        ?stroke
      />
    | #queue_arrow_left_light =>
      <Path
        transform="rotate(180 10 10)"
        d="M 11.236 4.018 L 16.683 9.464 L 1.97 9.464 C 1.559 9.464 1.301 9.91 1.508 10.267 C 1.603 10.433 1.779 10.535 1.97 10.535 L 16.799 10.535 L 11.352 15.981 C 11.06 16.272 11.194 16.768 11.592 16.876 C 11.775 16.926 11.973 16.873 12.108 16.738 L 18.408 10.436 C 18.618 10.227 18.618 9.887 18.408 9.679 L 11.991 3.263 C 11.784 3.053 11.444 3.053 11.236 3.263 C 11.027 3.47 11.027 3.81 11.236 4.018 Z"
        fill
        ?stroke
      />
    | #queue_arrow_right_light =>
      <Path
        d="M 11.236 4.018 L 16.683 9.464 L 1.97 9.464 C 1.559 9.464 1.301 9.91 1.508 10.267 C 1.603 10.433 1.779 10.535 1.97 10.535 L 16.799 10.535 L 11.352 15.981 C 11.06 16.272 11.194 16.768 11.592 16.876 C 11.775 16.926 11.973 16.873 12.108 16.738 L 18.408 10.436 C 18.618 10.227 18.618 9.887 18.408 9.679 L 11.991 3.263 C 11.784 3.053 11.444 3.053 11.236 3.263 C 11.027 3.47 11.027 3.81 11.236 4.018 Z"
        fill
        ?stroke
      />
    | #queue_arrow_up_light =>
      <Path
        transform="rotate(270 10.5 11.5)"
        d="M 11.236 4.018 L 16.683 9.464 L 1.97 9.464 C 1.559 9.464 1.301 9.91 1.508 10.267 C 1.603 10.433 1.779 10.535 1.97 10.535 L 16.799 10.535 L 11.352 15.981 C 11.06 16.272 11.194 16.768 11.592 16.876 C 11.775 16.926 11.973 16.873 12.108 16.738 L 18.408 10.436 C 18.618 10.227 18.618 9.887 18.408 9.679 L 11.991 3.263 C 11.784 3.053 11.444 3.053 11.236 3.263 C 11.027 3.47 11.027 3.81 11.236 4.018 Z"
        fill
        ?stroke
      />
    | #queue_arrow_down_light =>
      <Path
        transform="rotate(90 9.5 10.5)"
        d="M 11.236 4.018 L 16.683 9.464 L 1.97 9.464 C 1.559 9.464 1.301 9.91 1.508 10.267 C 1.603 10.433 1.779 10.535 1.97 10.535 L 16.799 10.535 L 11.352 15.981 C 11.06 16.272 11.194 16.768 11.592 16.876 C 11.775 16.926 11.973 16.873 12.108 16.738 L 18.408 10.436 C 18.618 10.227 18.618 9.887 18.408 9.679 L 11.991 3.263 C 11.784 3.053 11.444 3.053 11.236 3.263 C 11.027 3.47 11.027 3.81 11.236 4.018 Z"
        fill
        ?stroke
      />
    | #double_arrow_left_light =>
      <>
        <Path
          transform="rotate(180 10 10)"
          d="M10.268 10.048L4.644 4.35a.5.5 0 11.712-.702l5.972 6.049a.5.5 0 01-.002.704l-5.972 6a.5.5 0 01-.708-.706l5.622-5.648z"
          fill
          ?stroke
        />
        <Path
          transform="rotate(180 10 10)"
          d="M14.268 10.048L8.644 4.35a.5.5 0 11.712-.702l5.972 6.049a.5.5 0 01-.002.704l-5.972 6a.5.5 0 01-.708-.706l5.622-5.648z"
          fill
          ?stroke
        />
      </>
    | #double_arrow_right_light =>
      <>
        <Path
          d="M10.268 10.048L4.644 4.35a.5.5 0 11.712-.702l5.972 6.049a.5.5 0 01-.002.704l-5.972 6a.5.5 0 01-.708-.706l5.622-5.648z"
          fill
          ?stroke
        />
        <Path
          d="M14.268 10.048L8.644 4.35a.5.5 0 11.712-.702l5.972 6.049a.5.5 0 01-.002.704l-5.972 6a.5.5 0 01-.708-.706l5.622-5.648z"
          fill
          ?stroke
        />
      </>
    | #export_light =>
      <Path
        d="M9.73 1a.5.5 0 01.09.992L9.73 2H2v15.98h15.99v-7.353a.5.5 0 01.41-.492l.09-.008a.5.5 0 01.492.41l.008.09v7.854a.5.5 0 01-.41.492l-.09.008H1.5a.5.5 0 01-.492-.41L1 18.481V1.5a.5.5 0 01.41-.492L1.5 1h8.23zm3.43.99a.5.5 0 01.606.366c.26 1.046 1.37 2.156 3.362 3.285l-.07-.042a.5.5 0 01-.005.956c-1.946 1.063-3.028 2.154-3.285 3.237a.5.5 0 01-.973-.232l.052-.194c.298-.99 1.062-1.916 2.278-2.79h-1.244l-.246.004a7.9 7.9 0 00-7.654 7.896.5.5 0 11-1 0 8.9 8.9 0 018.9-8.9h1.286c-1.32-.959-2.115-1.946-2.371-2.98a.5.5 0 01.365-.605z"
        fill
        ?stroke
      />
    | #import_light =>
      <Path
        d="M9.75 1.099a.5.5 0 110 1H2.099V17.4H17.4V9.75a.5.5 0 111 0v8.15a.5.5 0 01-.5.5H1.599a.5.5 0 01-.5-.5V1.6a.5.5 0 01.5-.5zM18 2a.5.5 0 110 1 7.9 7.9 0 00-7.9 7.9v1.244c.93-1.295 1.92-2.078 2.984-2.33a.5.5 0 11.232.972c-1.096.26-2.2 1.365-3.276 3.352l.039-.067a.5.5 0 01-.956.005c-1.114-1.948-2.21-3.034-3.243-3.29a.5.5 0 11.24-.971c1.034.256 2.022 1.051 2.98 2.372V10.9A8.9 8.9 0 0118 2z"
        fill
        ?stroke
      />
    | #edit_light =>
      <Path
        d="M17.1 5.247l-.857.864-2.834-2.833.854-.861c.567-.567 1.555-.566 2.12 0l.714.713c.583.582.585 1.532.004 2.117zM2.25 17.329l1.236-4.058.25-.252 2.834 2.833-.251.253-4.07 1.224zM12.704 3.987l2.835 2.834-8.264 8.322-2.834-2.834 8.263-8.322zm5.1-1.564l-.713-.713c-.942-.943-2.582-.951-3.541.006L2.688 12.654a.508.508 0 00-.123.207l-1.543 5.07a.499.499 0 00.622.624l5.084-1.528a.497.497 0 00.21-.127L17.81 5.952a2.503 2.503 0 00-.006-3.53z"
        fill
        ?stroke
      />
    | #help_bold =>
      <Path
        d="M8.14113175,14.8363735 C8.46160332,14.5387529 8.92508553,14.4914883 9.29074733,14.6856762 C9.82467086,13.9732086 10.6136956,13.5140187 11.5436319,13.5140187 L15.5,13.5140187 C16.3182412,13.5140187 17,12.800402 17,11.8971963 L17,5.61682243 C17,4.7136167 16.3182412,4 15.5,4 L4.5,4 C3.68175881,4 3,4.7136167 3,5.61682243 L3,11.8971963 C3,12.800402 3.68175881,13.5140187 4.5,13.5140187 L5.785,13.5140187 C6.33728475,13.5140187 6.785,13.9617339 6.785,14.5140187 C6.785,15.0663034 6.33728475,15.5140187 5.785,15.5140187 L4.5,15.5140187 C2.55624119,15.5140187 1,13.8850559 1,11.8971963 L1,5.61682243 C1,3.62896274 2.55624119,2 4.5,2 L15.5,2 C17.4437588,2 19,3.62896274 19,5.61682243 L19,11.8971963 C19,13.8850559 17.4437588,15.5140187 15.5,15.5140187 L11.5436319,15.5140187 C11.0332862,15.5140187 10.5516319,16.1369851 10.5516319,17 C10.5516319,18.1423355 8.94497881,18.3917264 8.59868701,17.3031437 C8.54738035,17.141859 8.44660649,16.8757683 8.31935227,16.6212265 C8.23136009,16.445219 8.1467436,16.3119237 8.0888839,16.2496216 C7.71305406,15.8449361 7.73644622,15.2122034 8.14113175,14.8363735 Z M7.14215553,10 C6.58987078,10 6.14215553,9.55228475 6.14215553,9 C6.14215553,8.44771525 6.58987078,8 7.14215553,8 L13.4264666,8 C13.9787513,8 14.4264666,8.44771525 14.4264666,9 C14.4264666,9.55228475 13.9787513,10 13.4264666,10 L7.14215553,10 Z"
        fill
        ?stroke
      />
    | #notification_bold =>
      <Path
        d="M7.25,16.437 L3,16.437 C2.44771525,16.437 2,15.9892847 2,15.437 L2,14.563 C2,14.2979381 2.10523437,14.0437195 2.29257776,13.8562088 C3.22556419,12.9223894 3.75,11.6564718 3.75,10.338 L3.75,9.438 C3.75,6.50867723 5.22888686,4.1012774 7.69140516,3.16882775 C7.76567773,1.95713804 8.76988974,1 10,1 C11.2307843,1 12.2353742,1.95817872 12.3078199,3.1694774 C14.7653236,4.10314029 16.25,6.51781236 16.25,9.438 L16.25,10.338 C16.25,11.6573975 16.7743702,12.9231566 17.7071068,13.8558932 C17.8946432,14.0434296 18,14.2977835 18,14.563 L18,15.437 C18,15.9892847 17.5522847,16.437 17,16.437 L12.75,16.437 L12.75,16.875 C12.75,18.3932847 11.5182847,19.625 10,19.625 C8.48171525,19.625 7.25,18.3932847 7.25,16.875 L7.25,16.437 Z M9.25,16.437 L9.25,16.875 C9.25,17.2887153 9.58628475,17.625 10,17.625 C10.4137153,17.625 10.75,17.2887153 10.75,16.875 L10.75,16.437 L9.25,16.437 Z M15.581461,14.437 C14.7216468,13.2531024 14.25,11.8197431 14.25,10.338 L14.25,9.438 C14.25,7.10442146 13.0344354,5.34453372 11.0810978,4.88097695 C10.6302823,4.77399158 10.312,4.37133624 10.312,3.908 L10.3120009,3.31162449 C10.3122394,3.13825358 10.1739858,3 10,3 C9.82582123,3 9.687,3.13874816 9.687,3.313 L9.687,3.908 C9.687,4.37142942 9.36859239,4.77414008 8.91765958,4.88103451 C6.95785783,5.34560903 5.75,7.09502131 5.75,9.438 L5.75,10.338 C5.75,11.8191897 5.27819985,13.2525629 4.41819141,14.437 L15.581461,14.437 Z"
        fill
        ?stroke
      />
    | #plus_light =>
      <Path
        d="M10 2.5a.5.5 0 01.5.5v7h7a.5.5 0 110 1h-7v7a.5.5 0 11-1 0v-7h-7a.5.5 0 110-1h7V3a.5.5 0 01.5-.5z"
        fill
        ?stroke
      />
    | #bordered_plus_light =>
      <Path
        d="M17.5 0A2.5 2.5 0 0120 2.5v15a2.5 2.5 0 01-2.5 2.5h-15A2.5 2.5 0 010 17.5v-15A2.5 2.5 0 012.5 0zm0 1h-15A1.5 1.5 0 001 2.5v15A1.5 1.5 0 002.5 19h15a1.5 1.5 0 001.5-1.5v-15A1.5 1.5 0 0017.5 1zM10 3.5a.5.5 0 01.492.41L10.5 4v5.5H16a.5.5 0 01.09.992L16 10.5h-5.5V16a.5.5 0 01-.992.09L9.5 16v-5.5H4a.5.5 0 01-.09-.992L4 9.5h5.5V4a.5.5 0 01.5-.5z"
        fill
        ?stroke
      />
    | #analytics_bold =>
      <Path
        d="M6.7549408,12.8243523 L8.18908225,17.7790928 C8.47774073,18.7763639 9.90698869,18.7275628 10.1269513,17.712925 L12.3525926,7.44654644 L13.5934853,13.7344871 C13.6859618,14.2030909 14.0969222,14.5408763 14.5745637,14.5408763 L18,14.5408763 C18.5522847,14.5408763 19,14.0931611 19,13.5408763 C19,12.9885916 18.5522847,12.5408763 18,12.5408763 L15.3965054,12.5408763 L13.37965,2.32092168 C13.1694762,1.25591362 11.6512661,1.24176002 11.4212733,2.3026646 L9.01042213,13.4233742 L7.79971657,9.2405708 C7.52840545,8.30323217 6.21265667,8.269717 5.89397598,9.19202717 L4.73686682,12.5408763 L2,12.5408763 C1.44771525,12.5408763 1,12.9885916 1,13.5408763 C1,14.0931611 1.44771525,14.5408763 2,14.5408763 L5.44935333,14.5408763 C5.87576368,14.5408763 6.25526638,14.2704858 6.39452316,13.8674556 L6.7549408,12.8243523 Z"
        fill
        ?stroke
      />
    | #close_medium =>
      <Path
        d="M4.22 4.22a.75.75 0 011.06 0l4.775 4.774L14.83 4.22a.75.75 0 01.977-.073l.084.073a.75.75 0 010 1.06l-4.776 4.775 4.776 4.775a.75.75 0 01.072.977l-.072.084a.75.75 0 01-1.061 0l-4.775-4.776-4.775 4.776a.75.75 0 01-.976.072l-.084-.072a.75.75 0 010-1.061l4.774-4.775L4.22 5.28a.75.75 0 01-.073-.976z"
        fill
        ?stroke
      />
    | #close_light =>
      <Path
        d="M9.096 9.804l-4.949-4.95a.499.499 0 010-.707.499.499 0 01.707 0l4.949 4.95 4.95-4.95a.499.499 0 01.707 0 .499.499 0 010 .707l-4.95 4.95 4.95 4.95a.499.499 0 010 .707.499.499 0 01-.707 0l-4.95-4.95-4.949 4.95a.499.499 0 01-.707 0 .499.499 0 010-.707l4.949-4.95z"
        fill
        ?stroke
      />
    | #close_bold =>
      <Path
        d="M4.293 4.293a1 1 0 011.414 0l4.598 4.597 4.598-4.597a1 1 0 011.32-.083l.094.083a1 1 0 010 1.414l-4.598 4.598 4.598 4.598a1 1 0 01.084 1.32l-.084.094a1 1 0 01-1.414 0l-4.598-4.598-4.598 4.598a1 1 0 01-1.32.084l-.094-.084a1 1 0 010-1.414l4.597-4.598-4.597-4.598a1 1 0 01-.083-1.32z"
        fill
        ?stroke
      />
    | #oval =>
      <Path
        d="M0 4c0 2.264 1.755 4 4.03 4C6.307 8 8 6.275 8 4S6.198 0 4 0C1.804 0 0 1.736 0 4z"
        fill
        ?stroke
      />
    | #sales_bold =>
      <Path
        d="M11.7 0c2.2 0 3.985 1.785 3.985 3.985l-.001 1.129h2.147a1 1 0 01.995.896l1.164 11.097c.114 1.088-.765 2.008-1.858 2.008H4.868l-.02-.002-.011.002H1.879c-1.097 0-1.984-.917-1.869-2.01L1.19 6.01a1 1 0 01.994-.894l2.531-.001V3.985A3.986 3.986 0 018.502.005l.2-.005c.53 0 1.037.104 1.5.292A3.963 3.963 0 0111.7 0zM4.058 7.114h-.975l-1.063 10h.99v-.007l1.048-9.993zm12.873 0H6.07l-1.05 10h12.96l-1.05-10zM8.209 2.063l-.082.022a1.987 1.987 0 00-1.412 1.901l-.001 1.129h1V3.985c0-.697.18-1.353.495-1.923zm1.99.623l-.084.106c-.251.332-.4.746-.4 1.194l-.001 1.129h.97V3.985c0-.497-.182-.952-.484-1.3zm1.99-.624l.042.076c.29.553.454 1.181.454 1.848l-.001 1.129h1V3.985c0-.927-.635-1.706-1.494-1.924z"
        fill
        ?stroke
      />
    | #products_bold =>
      <Path
        d="M19 1a1 1 0 011 1v16a1 1 0 01-1 1H1a1 1 0 01-1-1V2a1 1 0 011-1h18zm-1 2h-2v4a1 1 0 01-1.335.942l-3.11-1.104-3.23 1.108A1 1 0 017 7V3H5v14h13V3zM3 3H2v14h1V3zm11 0H9v2.6l2.237-.767a1 1 0 01.66.004L14 5.584V3z"
        fill
        ?stroke
      />
    | #contacts_bold =>
      <Path
        d="M11.3 1c1.049 0 2.042.37 2.825 1.035a4.36 4.36 0 011.544 3.333c0 .892-.27 1.73-.736 2.428A7.732 7.732 0 0119 14.607a1 1 0 01-1 1h-1.897c.241.75.37 1.547.37 2.369a1 1 0 01-1 1H2a1 1 0 01-1-1 7.74 7.74 0 014.123-6.843 4.369 4.369 0 011.852-6.378A4.37 4.37 0 0111.3 1zM8.533 13.099l.242.006a4.421 4.421 0 01-.8-.073l-.03-.005a4.344 4.344 0 01-.215-.048l-.053-.013a4.037 4.037 0 01-.19-.054l-.061-.02a3.974 3.974 0 01-.172-.059l-.058-.022a4.297 4.297 0 01-.44-.2L6.73 12.6a5.743 5.743 0 00-3.644 4.377h11.3a5.743 5.743 0 00-3.601-4.36l-.04.02c-.133.067-.27.128-.41.182l-.057.021a4.022 4.022 0 01-.195.066l-.024.008a4.097 4.097 0 01-.23.064l-.017.004c-.078.02-.158.036-.238.05l-.05.01a4.112 4.112 0 01-.54.06l-.21.004c-.082 0-.163-.002-.243-.006zm4.78-3.852l-.144.072-.067.03a4.36 4.36 0 01-.693 1.814 7.749 7.749 0 012.714 2.444h1.79a5.74 5.74 0 00-3.6-4.36zM8.773 6.368a2.368 2.368 0 00-1.338 4.324l-.011-.008c.095.066.194.125.297.176l.052.025-.052-.025c.05.025.101.048.153.07l-.1-.045c.056.026.114.05.173.073l-.073-.029c.046.02.093.037.141.053l-.068-.024c.058.021.117.04.176.057l-.108-.033c.05.017.102.032.154.046l-.046-.013c.049.014.098.026.148.037l-.102-.024c.057.015.114.028.172.038l-.07-.014c.057.012.114.022.173.03l-.103-.016c.071.013.143.023.216.03l-.113-.013c.049.006.099.012.149.016l.18.006.197-.008a2.378 2.378 0 00.176-.02l-.14.017c.068-.007.135-.016.2-.028l-.06.01c.064-.01.126-.022.189-.037l-.128.027c.064-.012.127-.027.19-.044l-.062.017c.074-.018.147-.04.218-.065l-.156.048a2.35 2.35 0 00.224-.073l-.068.025c.063-.022.124-.046.184-.073l-.116.048c.052-.02.102-.041.152-.065l-.036.017c.063-.028.125-.059.185-.092l-.149.075c.058-.026.114-.056.169-.087l-.02.012c.055-.031.11-.064.162-.1l-.142.088a2.368 2.368 0 001.2-2.041.901.901 0 01-.007-.186 2.368 2.368 0 00-2.362-2.202zM11.301 3a2.37 2.37 0 00-2.155 1.385 4.373 4.373 0 013.703 2.775 2.366 2.366 0 00-.018-3.6c-.424-.36-.96-.56-1.53-.56z"
        fill
        ?stroke
      />
    | #filter_light =>
      <Path
        d="M10 1.5a8.5 8.5 0 110 17 8.5 8.5 0 010-17zm0 1a7.5 7.5 0 100 15 7.5 7.5 0 000-15zm1.5 10a.5.5 0 01.09.992l-.09.008h-3a.5.5 0 01-.09-.992l.09-.008h3zm1-3a.5.5 0 01.09.992l-.09.008h-5a.5.5 0 01-.09-.992L7.5 9.5h5zm1-3a.5.5 0 01.09.992l-.09.008h-7a.5.5 0 01-.09-.992L6.5 6.5h7z"
        fill
        ?stroke
      />
    | #delete_light =>
      <Path
        d="M12 .5a.5.5 0 01.5.5v1.5H18a.5.5 0 01.492.41L18.5 3a.5.5 0 01-.5.5h-1.117l-1.457 15.547a.5.5 0 01-.497.453H5.07a.5.5 0 01-.497-.453L3.116 3.5H2a.5.5 0 01-.492-.41L1.5 3a.5.5 0 01.5-.5h5.5V1A.5.5 0 018 .5h4zm-7.88 3l1.407 15h8.946l1.406-15H4.12zm7.38 3a.5.5 0 01.5.5v8a.5.5 0 11-1 0V7a.5.5 0 01.5-.5zm-3 0A.5.5 0 019 7v8a.5.5 0 11-1 0V7a.5.5 0 01.5-.5zm3-5h-3v1h3v-1z"
        fill
        ?stroke
      />
    | #more_x_bold =>
      <Path
        d="M3 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zm7 0a1.5 1.5 0 110 3 1.5 1.5 0 010-3zm7 0a1.5 1.5 0 110 3 1.5 1.5 0 010-3zm-14 1a.5.5 0 100 1 .5.5 0 000-1zm7 0a.5.5 0 100 1 .5.5 0 000-1zm7 0a.5.5 0 100 1 .5.5 0 000-1z"
        fill
        ?stroke
      />
    | #more_y_light =>
      <Path
        d="M 5 10 C 5 10.552 4.552 11 4 11 C 3.448 11 3 10.552 3 10 C 3 9.448 3.448 9 4 9 C 4.552 9 5 9.448 5 10 Z M 11 10 C 11 10.552 10.552 11 10 11 C 9.448 11 9 10.552 9 10 C 9 9.448 9.448 9 10 9 C 10.552 9 11 9.448 11 10 Z M 17 10 C 17 10.552 16.552 11 16 11 C 15.448 11 15 10.552 15 10 C 15 9.448 15.448 9 16 9 C 16.552 9 17 9.448 17 10 Z"
        transform="rotate(90 10 10)"
        fill
        ?stroke
      />
    | #calendar =>
      <Path
        d="M13.5 1a.5.5 0 0 1 .492.41L14 1.5v2.505h2a2.5 2.5 0 0 1 2.495 2.336l.005.164v9.002a2.5 2.5 0 0 1-2.5 2.5H4a2.5 2.5 0 0 1-2.5-2.5V6.505a2.5 2.5 0 0 1 2.5-2.5h2V1.5a.5.5 0 0 1 .992-.09L7 1.5v2.505h6V1.5a.5.5 0 0 1 .5-.5ZM16 5.005H4a1.5 1.5 0 0 0-1.5 1.5v9.002a1.5 1.5 0 0 0 1.5 1.5h12a1.5 1.5 0 0 0 1.5-1.5V6.505a1.5 1.5 0 0 0-1.5-1.5ZM15.5 8a.5.5 0 0 1 .09.992L15.5 9h-11a.5.5 0 0 1-.09-.992L4.5 8h11Z"
        fill
        ?stroke
      />
    | #clipboard =>
      <>
        <Path
          d="M5.83332 8.95837C5.25803 8.95837 4.79166 9.42473 4.79166 10C4.79166 10.5754 5.25803 11.0417 5.83332 11.0417H14.1245C14.6998 11.0417 15.1661 10.5754 15.1661 10C15.1661 9.42473 14.6998 8.95837 14.1245 8.95837H5.83332Z"
          fill
          ?stroke
        />
        <Path
          d="M5.87556 13.1942C5.30026 13.1942 4.83389 13.6606 4.83389 14.2359C4.83389 14.8112 5.30026 15.2775 5.87556 15.2775H14.1667C14.742 15.2775 15.2083 14.8112 15.2083 14.2359C15.2083 13.6606 14.742 13.1942 14.1667 13.1942H5.87556Z"
          fill
          ?stroke
        />
        <Path
          d="M2.70833 0.625C1.55774 0.625 0.625 1.55774 0.625 2.70833V17.2917C0.625 18.4423 1.55774 19.375 2.70833 19.375H17.2917C18.4423 19.375 19.375 18.4423 19.375 17.2917V2.70833C19.375 1.55774 18.4423 0.625 17.2917 0.625H2.70833ZM4.79167 2.70833H2.70833V17.2917H17.2917V2.70833H15.2083V3.75C15.2083 5.47589 13.8093 6.875 12.0833 6.875H7.91667C6.19078 6.875 4.79167 5.47589 4.79167 3.75V2.70833ZM6.875 2.70833V3.75C6.875 4.32529 7.34137 4.79167 7.91667 4.79167H12.0833C12.6586 4.79167 13.125 4.32529 13.125 3.75V2.70833H6.875Z"
          fill
          fillRule="evenodd"
        />
      </>
    | #tick_light =>
      <Path
        d="M15.991 5.642a.5.5 0 01.765.638l-.057.07-8.035 8.034a.5.5 0 01-.64.056l-.07-.058-3.965-4.018a.5.5 0 01.643-.76l.069.058 3.611 3.659 7.68-7.679z"
        fill
        ?stroke
      />
    | #tick_medium =>
      <Path
        d="M15.991 5.642a.5.5 0 01.765.638l-.057.07-8.035 8.034a.5.5 0 01-.64.056l-.07-.058-3.965-4.018a.5.5 0 01.643-.76l.069.058 3.611 3.659 7.68-7.679z"
        fill
        stroke=fill
        strokeWidth="1"
      />
    | #tick_bold =>
      <Path
        d="M7.643 10.357L2.92892 5.643L0.571899 8L7.643 15.0712L19.428 3.286L17.071 0.928986L7.643 10.357Z"
        fill
        ?stroke
      />
    | #download =>
      <Path
        transform="translate(3.5,1.5)"
        d="M8.5,0 C8.52896443,0 8.55766975,0.00251316923 8.58582442,0.00741874956 L8.5,0 C8.53717995,0 8.57341209,0.00405810679 8.60827763,0.0117555499 C8.62775202,0.0161752938 8.64709994,0.0217060499 8.66603209,0.0283713194 C8.67677972,0.032003496 8.68758225,0.0362330462 8.69819437,0.04081971 C8.71222744,0.0470222758 8.72585435,0.0536592396 8.7391668,0.06090986 C8.75136271,0.0674500504 8.76347123,0.0747354237 8.77523219,0.0825044328 C8.78539198,0.0892659827 8.79534927,0.0963652006 8.80505507,0.103839244 C8.81761754,0.113497606 8.82970924,0.123771313 8.84128643,0.134588748 C8.84536408,0.13839719 8.8494901,0.142383321 8.85355339,0.146446609 L12.8535534,4.14644661 L12.8654113,4.15871357 C12.8762287,4.17029076 12.8865024,4.18238246 12.8961917,4.19494796 L12.8535534,4.14644661 C12.8776252,4.17051844 12.8989891,4.19679203 12.917449,4.22479402 C12.9252646,4.23652877 12.9325499,4.24863729 12.9393234,4.26106504 C12.9463408,4.27414565 12.9529777,4.28777256 12.9589828,4.30166987 C12.963767,4.31241775 12.9679965,4.32322028 12.9718538,4.33419807 C12.978294,4.35290006 12.9838247,4.37224798 12.9881784,4.39190902 C12.9895896,4.39781538 12.9908237,4.40395012 12.9919443,4.41012437 C12.9974868,4.44233025 13,4.47103557 13,4.5 L13,14.75 C13,15.9926407 11.9926407,17 10.75,17 L2.25,17 C1.00735931,17 0,15.9926407 0,14.75 L0,2.25 C0,1.00735931 1.00735931,0 2.25,0 L8.5,0 Z M8,1 L2.25,1 C1.55964406,1 1,1.55964406 1,2.25 L1,14.75 C1,15.4403559 1.55964406,16 2.25,16 L10.75,16 C11.4403559,16 12,15.4403559 12,14.75 L12,5 L8.5,5 C8.22385763,5 8,4.77614237 8,4.5 L8,1 Z M6.5,5 C6.77614237,5 7,5.22385763 7,5.5 L6.99880929,12.0382784 C7.30577341,11.7224246 7.63869188,11.4492156 7.99741536,11.2192889 L8.25396554,11.0647219 C8.49436301,10.9288408 8.79939704,11.0135681 8.93527812,11.2539655 C9.0711592,11.494363 8.98643193,11.799397 8.74603446,11.9352781 C8.03109573,12.3393866 7.42546263,12.9470082 6.92721091,13.7663569 C6.71994275,14.1071984 6.21658907,14.0799876 6.04727259,13.7187882 C5.57029518,12.7012621 5.01087921,12.1400093 4.3809779,11.9856272 C4.17237442,11.9345006 4.02672674,11.7593759 4.00322423,11.5578753 L4.00080257,11.4701876 L4.01437284,11.3809779 C4.08010693,11.1127734 4.35081763,10.9486387 4.6190221,11.0143728 C5.13307796,11.1403625 5.59274319,11.419195 5.99981922,11.843425 L6,5.5 C6,5.22385763 6.22385763,5 6.5,5 Z M9,1.707 L9,4 L11.293,4 L9,1.707 Z"
        fill
        ?stroke
      />
    | #export_download =>
      <Path
        d="M10 2.5C10.2761 2.5 10.5 2.72386 10.5 3V12.0303C11.1405 11.0351 11.8722 10.2218 12.6997 9.60022C12.9205 9.43437 13.2339 9.47891 13.3998 9.6997C13.5656 9.92048 13.5211 10.2339 13.3003 10.3998C12.1963 11.2291 11.2423 12.4868 10.4547 14.208C10.3699 14.3934 10.1811 14.5087 9.9775 14.4995C9.77391 14.4903 9.59626 14.3585 9.52847 14.1663C8.73325 11.9118 7.80269 10.7873 6.84515 10.4754C6.58258 10.3899 6.43906 10.1077 6.52458 9.84515C6.6101 9.58258 6.89228 9.43906 7.15485 9.52458C8.07874 9.82551 8.85084 10.571 9.5 11.6765V3C9.5 2.72386 9.72386 2.5 10 2.5ZM4 12.5C4.27614 12.5 4.5 12.7239 4.5 13V16C4.5 16.2761 4.72386 16.5 5 16.5H15C15.2761 16.5 15.5 16.2761 15.5 16V13C15.5 12.7239 15.7239 12.5 16 12.5C16.2761 12.5 16.5 12.7239 16.5 13V16C16.5 16.8284 15.8284 17.5 15 17.5H5C4.17157 17.5 3.5 16.8284 3.5 16V13C3.5 12.7239 3.72386 12.5 4 12.5Z"
        fill
        ?stroke
      />
    | #external_link =>
      <Path
        d="M8.993 2a1 1 0 1 1 0 2H4v12h12v-4.933a1 1 0 1 1 2 0V16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h4.993zm8.68.286a1 1 0 0 1 .193 1.142l.026-.081c-.51 1.412-.512 2.44-.091 3.134a1 1 0 0 1-1.71 1.037 4.088 4.088 0 0 1-.563-1.674L9.7 11.672a1 1 0 1 1-1.414-1.414l6.01-6.009a8.048 8.048 0 0 1-1.278-.19l-.326-.083a1 1 0 1 1 .535-1.927c1.038.289 2.165.294 3.394.008a.992.992 0 0 1 1.051.229z"
        fill
        ?stroke
      />
    | #cloud_save =>
      <Path
        d="M10.516 3a5.642 5.642 0 0 1 4.892 2.824l.207-.209A1 1 0 0 1 17.03 7.03l-2.202 2.2-.667.668v-.001l-3.244 3.245a1.385 1.385 0 0 1-1.96 0l-1.854-1.854a1 1 0 1 1 1.414-1.414l1.42 1.419 3.976-3.975A3.645 3.645 0 0 0 10.516 5a3.644 3.644 0 0 0-3.645 3.645v1H5.677A2.676 2.676 0 0 0 3 12.323 2.676 2.676 0 0 0 5.677 15h9.097a2.096 2.096 0 0 0 1.44-3.62l1.413-1.416a4.085 4.085 0 0 1 1.239 2.733l.005.206A4.096 4.096 0 0 1 14.774 17H5.677A4.676 4.676 0 0 1 1 12.323 4.677 4.677 0 0 1 4.836 7.72l.114-.018.025-.142a5.646 5.646 0 0 1 5.32-4.556L10.515 3z"
        fill
        ?stroke
      />
    | #customer_support =>
      <Path
        transform="translate(2,3.59)"
        d="M8.97393555,0.403598755 L11.499,2.92800001 L13.6456902,0.782079478 C14.4835303,-0.0557606533 15.8946837,0.488590374 15.9944117,1.62880487 L16,1.75790754 L16,12.8217083 L0,12.8217083 L0,1.75790754 C0,0.530809089 1.48543718,-0.0867920584 2.35430869,0.782079455 L4.5,2.92800001 L7.02606439,0.403598813 C7.56419613,-0.134532928 8.43580387,-0.134532928 8.97393555,0.403598755 Z M14,10 L2,10 L2,10.821 L14,10.821 L14,10 Z M8,2.25800001 L5.20805277,5.05003703 L4.50094601,5.75714367 L3.79383928,5.05003699 L2,3.25600001 L2,8 L14,8.00000001 L14,3.25500001 L12.2061609,5.05003686 L11.4990541,5.75714381 L10.7919472,5.05003703 L8,2.25800001 Z"
        fill
        ?stroke
      />
    | #filter =>
      <Path
        d="M 12.143 14.999 C 12.692 14.998 13.037 15.593 12.763 16.069 C 12.658 16.252 12.478 16.379 12.271 16.416 L 12.143 16.428 L 7.857 16.428 C 7.308 16.429 6.963 15.834 7.237 15.358 C 7.342 15.175 7.522 15.048 7.729 15.011 L 7.857 14.999 L 12.143 14.999 Z M 14.999 9.286 C 15.549 9.285 15.894 9.879 15.62 10.356 C 15.515 10.538 15.335 10.665 15.128 10.703 L 14.999 10.714 L 5.001 10.714 C 4.451 10.715 4.106 10.121 4.38 9.644 C 4.485 9.462 4.665 9.335 4.872 9.297 L 5.001 9.286 L 14.999 9.286 Z M 19.284 3.572 C 19.834 3.571 20.179 4.166 19.905 4.642 C 19.8 4.825 19.62 4.952 19.413 4.989 L 19.284 5.001 L 0.716 5.001 C 0.166 5.002 -0.179 4.407 0.095 3.931 C 0.2 3.748 0.38 3.621 0.587 3.584 L 0.716 3.572 L 19.284 3.572 Z"
        fill
        ?stroke
      />
    | #reset =>
      <Path
        d="M 15.792 9.999 C 15.792 6.8 13.198 4.207 9.999 4.207 C 7.59 4.207 5.524 5.678 4.651 7.772 L 8.217 7.772 C 8.464 7.772 8.662 7.97 8.662 8.217 C 8.662 8.464 8.464 8.662 8.217 8.662 L 4.118 8.662 C 4.098 8.662 4.08 8.662 4.063 8.661 C 4.006 8.665 3.95 8.661 3.892 8.645 C 3.842 8.628 3.795 8.606 3.756 8.576 C 3.496 8.443 3.316 8.172 3.316 7.86 L 3.316 3.761 C 3.316 3.515 3.515 3.316 3.761 3.316 C 4.009 3.316 4.207 3.515 4.207 3.761 L 4.207 6.663 C 5.361 4.664 7.524 3.316 9.999 3.316 C 13.69 3.316 16.683 6.309 16.683 9.999 C 16.683 13.69 13.69 16.683 9.999 16.683 C 7.332 16.683 5.032 15.119 3.958 12.865 C 3.854 12.641 3.949 12.375 4.17 12.27 C 4.393 12.164 4.659 12.26 4.765 12.481 C 5.694 14.44 7.689 15.792 9.999 15.792 C 13.198 15.792 15.792 13.198 15.792 9.999 Z"
        fill
        ?stroke
      />
    | #danger =>
      <>
        <Path
          transform="translate(2, 2)"
          d="M5.83432 5.83436C6.14674 5.52194 6.65327 5.52194 6.96569 5.83436L8.00003 6.8687L9.03435 5.83438C9.34677 5.52196 9.8533 5.52196 10.1657 5.83438C10.4781 6.1468 10.4781 6.65333 10.1657 6.96575L9.1314 8.00007L10.1657 9.03437C10.4781 9.34679 10.4781 9.85332 10.1657 10.1657C9.85328 10.4782 9.34674 10.4782 9.03432 10.1657L8.00003 9.13144L6.96571 10.1658C6.65329 10.4782 6.14676 10.4782 5.83434 10.1658C5.52192 9.85334 5.52192 9.34681 5.83434 9.03439L6.86866 8.00007L5.83432 6.96573C5.5219 6.65331 5.5219 6.14678 5.83432 5.83436Z"
          fill
        />
        <Path
          transform="translate(0.7, 0)"
          d="M2 10C2 6.0236 5.22355 2.8 9.2 2.8C13.1765 2.8 16.4 6.0236 16.4 10C16.4 13.9765 13.1765 17.2 9.2 17.2C5.22355 17.2 2 13.9765 2 10ZM9.2 4.4C6.10721 4.4 3.6 6.90725 3.6 10C3.6 13.0928 6.10721 15.6 9.2 15.6C12.2928 15.6 14.8 13.0928 14.8 10C14.8 6.90725 12.2928 4.4 9.2 4.4Z"
          fill
        />
      </>
    | #warning =>
      <>
        <Path
          d="M10 6C10.5523 6 11 6.44772 11 7V10C11 10.5523 10.5523 11 10 11C9.44771 11 9 10.5523 9 10V7C9 6.44772 9.44771 6 10 6Z"
          fill
        />
        <Path
          d="M9 13C9 12.4477 9.44771 12 10 12H10.0083C10.5606 12 11.0083 12.4477 11.0083 13C11.0083 13.5523 10.5606 14 10.0083 14H10C9.44771 14 9 13.5523 9 13Z"
          fill
        />
        <Path
          fillRule="evenodd"
          d="M10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1ZM3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10Z"
          fill
        />
      </>
    | #printing_label =>
      <>
        <Path
          fillRule="evenodd"
          d="M0.909973 3.90997C1.17247 3.64747 1.5285 3.5 1.89973 3.5H6.55316C7.16295 3.50013 7.74781 3.74246 8.17894 4.17369V4.17369L13.3122 9.3069C13.8116 9.80952 14.0919 10.4893 14.0919 11.1979C14.0919 11.9064 13.8116 12.5862 13.3121 13.0889L13.311 13.09L10.09 16.311L10.0889 16.3122C9.58624 16.8116 8.90645 17.0919 8.19788 17.0919C7.48931 17.0919 6.80952 16.8116 6.3069 16.3122L6.30578 16.311L1.17374 11.179V11.179C0.742514 10.7479 0.50013 10.1631 0.5 9.55327V4.89973C0.5 4.5285 0.647472 4.17247 0.909973 3.90997ZM1.89973 4.5C1.79372 4.5 1.69204 4.54211 1.61708 4.61708C1.54211 4.69204 1.5 4.79372 1.5 4.89973V9.55306V9.55306C1.5001 9.89763 1.63706 10.2282 1.88074 10.4718L7.01176 15.6028V15.6028C7.32698 15.9157 7.75369 16.0919 8.19788 16.0919C8.64206 16.0919 9.06822 15.9163 9.38343 15.6034V15.6034L12.6028 12.384V12.384C12.9157 12.0688 13.0919 11.6421 13.0919 11.1979C13.0919 10.7537 12.9163 10.3275 12.6034 10.0123V10.0123L7.47184 4.8808C7.22822 4.63711 6.89774 4.5001 6.55316 4.5V4.5H1.89973ZM5.33243 6.34645C5.52769 6.15118 5.84427 6.15119 6.03953 6.34645L8.26035 8.56728C8.45562 8.76254 8.45562 9.07913 8.26035 9.27439C8.06509 9.46965 7.74851 9.46965 7.55325 9.27439L5.33242 7.05355C5.13716 6.85829 5.13716 6.54171 5.33243 6.34645ZM3.34645 8.33243C3.54171 8.13717 3.85829 8.13717 4.05355 8.33243L8.26035 12.5393C8.45562 12.7345 8.45562 13.0511 8.26035 13.2464C8.06509 13.4416 7.74851 13.4416 7.55325 13.2464L3.34645 9.03954C3.15118 8.84428 3.15118 8.52769 3.34645 8.33243Z"
          fill
        />
        <Path
          fillRule="evenodd"
          d="M12.343 3.64645C12.5383 3.45118 12.8549 3.45118 13.0501 3.64645L18.7178 9.31416V9.31416C18.9656 9.56117 19.1625 9.85493 19.2967 10.1781C19.4309 10.5013 19.5 10.8479 19.5 11.1979C19.5 11.5479 19.4309 11.8945 19.2967 12.2177C19.1625 12.5408 18.9659 12.8343 18.7181 13.0813V13.0813L14.8496 16.9498C14.6543 17.1451 14.3377 17.1451 14.1425 16.9498C13.9472 16.7546 13.9472 16.438 14.1425 16.2427L18.0113 12.3739C18.166 12.2197 18.2894 12.0359 18.3731 11.8342C18.4569 11.6325 18.5 11.4163 18.5 11.1979C18.5 10.9795 18.4569 10.7632 18.3731 10.5615C18.2894 10.3598 18.1667 10.1767 18.012 10.0225L12.343 4.35355C12.1477 4.15829 12.1477 3.84171 12.343 3.64645Z"
          fill
        />
      </>
    }}
  </Svg>

let make = React.memo(make)

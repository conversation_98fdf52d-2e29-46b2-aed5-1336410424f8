@genType
type t = [
  | #toggle_list_arrows
  | #printer
  | #printer_bold
  | #printer_locked
  | #burger
  | #search
  | #back
  | #alert_tip
  | #info_tip
  | #settings
  | #settings_bold
  | #ticket_bold
  | #switch_bold
  | #arrow_up_overlay
  | #arrow_up_light
  | #arrow_down_light
  | #arrow_left_light
  | #arrow_right_light
  | #queue_arrow_left_light
  | #queue_arrow_right_light
  | #queue_arrow_up_light
  | #queue_arrow_down_light
  | #double_arrow_left_light
  | #double_arrow_right_light
  | #export_light
  | #import_light
  | #edit_light
  | #help_bold
  | #notification_bold
  | #plus_light
  | #bordered_plus_light
  | #analytics_bold
  | #close_light
  | #close_medium
  | #close_bold
  | #oval
  | #sales_bold
  | #products_bold
  | #contacts_bold
  | #filter_light
  | #delete_light
  | #more_x_bold
  | #more_y_light
  | #inner_oval
  | #calendar
  | #clipboard
  | #tick_light
  | #tick_medium
  | #tick_bold
  | #book_bold
  | #download
  | #export_download
  | #external_link
  | #cloud_save
  | #customer_support
  | #filter
  | #reset
  | #danger
  | #warning
  | #printing_label
]

@genType @react.component
let make: (
  ~name: t,
  ~fill: Style.Color.t=?,
  ~size: float=?,
  ~filter: string=?,
  ~stroke: string=?,
  ~strokeDasharray: string=?,
) => React.element

open Style

let styles = StyleSheet.create({
  "view": style(
    ~alignSelf=#flexStart,
    ~paddingVertical=4.0->dp,
    ~paddingHorizontal=8.0->dp,
    ~borderRadius=3.,
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  "text": merge([
    style(
      ~fontSize=FontSizes.xsmall,
      ~textAlign=#center,
      ~marginTop=1.5->dp,
      ~color=Colors.neutralColor100,
      (),
    ),
    FontFaces.libreFranklinRegularStyle,
  ]),
})
@react.component
let make = (~children) =>
  <View style={[styles["view"]]->arrayStyle}>
    <Text style={[styles["text"]]->arrayStyle}> children </Text>
  </View>

let make = React.memo(make)

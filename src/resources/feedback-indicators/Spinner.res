open Animated
open Style

@module("./spinner_primary.png") external primaryImageUri: string = "default"
@module("./spinner_neutral.png") external neutralImageUri: string = "default"
@module("./spinner_success.png") external successImageUri: string = "default"

type variation = [#primary | #neutral | #success]

@react.component
let make = (~variation=#primary, ~size: float=24.) => {
  let animatedValue = React.useRef(Value.create(0.))
  let loopAnimation = React.useRef(
    loop(
      timing(
        animatedValue.current,
        Value.Timing.config(
          ~toValue=1.->Value.Timing.fromRawValue,
          ~duration=1200.,
          ~easing=Easing.bezier(0.17, 0.67, 0.83, 0.67),
          ~useNativeDriver=false,
          (),
        ),
      ),
    ),
  )

  React.useLayoutEffect1(() => {
    loopAnimation.current->Animation.start()
    Some(() => loopAnimation.current->Animation.stop)
  }, [])

  let rotateInterpolate = Value.interpolate(
    animatedValue.current,
    Interpolation.config(
      ~inputRange=[0., 1.],
      ~outputRange=["0deg", "360deg"]->Interpolation.fromStringArray,
      (),
    ),
  )

  let imageSource = ReactNative.Image.Source.fromUriSource(
    ReactNative.Image.uriSource(
      ~uri=switch variation {
      | #primary => primaryImageUri
      | #neutral => neutralImageUri
      | #success => successImageUri
      },
      (),
    ),
  )

  <Animated.Image
    accessibilityLabel="spinner"
    style={style(
      ~height=size->dp,
      ~width=size->dp,
      ~transform=[ReactNative.Style.rotate(~rotate=rotateInterpolate->StyleProp.angle)],
      (),
    )}
    source=imageSource
  />
}

let make = React.memo(make)

// TODO - this component is a draft and is missing features and variation

open StyleX
open WebAPI

type variation = [#info]

let lineHeight = 18

let backgroundColor = (~variation) =>
  switch variation {
  | #info => "#F2F6FC"
  }
let textColor = (~variation) =>
  switch variation {
  | #info => "#354473"
  }

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~minHeight="18px",
    ~gap=Spaces.xnormalPx,
    ~padding=Spaces.normalPx,
    ~borderRadius=Spaces.xsmallPx,
    (),
  ),
  "content": style(~display=#flex, ~flexDirection=#column, ~alignSelf=#center, ~gap="6px", ()),
  "contentTitle": style(~font=`normal 600 16px "Archivo"`, ()),
  "contentText": style(
    ~position=#relative,
    ~\"-webkit-box-orient"=#vertical,
    ~overflow=#hidden,
    ~font=`normal 400 14px "Libre Franklin"`,
    ~lineHeight=Int.toString(lineHeight) ++ "px",
    (),
  ),
  "contentTextMoreButton": style(
    ~position=#absolute,
    ~overflow=#hidden,
    ~right="0",
    ~cursor=#pointer,
    ~font=`normal 400 14px "Archivo"`,
    ~lineHeight=Int.toString(lineHeight) ++ "px",
    ~\":hover"=style(~textDecorationLine=#underline, ()),
    (),
  ),
})

let styleProps = (~variation) =>
  StyleX.props([styles["root"], style(~backgroundColor=backgroundColor(~variation), ())])
let contentStyleProps = () => StyleX.props([styles["content"]])
let contentTitleStyleProps = (~variation) =>
  StyleX.props([styles["contentTitle"], style(~color=textColor(~variation), ())])
let contentTextStyleProps = (~variation, ~maxLines, ~clamped, ~expanded) =>
  StyleX.props([
    styles["contentText"],
    style(
      ~paddingRight=!expanded && clamped ? "23px" : "0px",
      ~display=!expanded ? #"-webkit-box" : #flex,
      ~\"-webkit-line-clamp"=Int.toString(maxLines),
      ~color=textColor(~variation),
      (),
    ),
  ])
let contentTextMoreButtonStyleProps = (~variation, ~maxLines) =>
  StyleX.props([
    styles["contentTextMoreButton"],
    style(
      ~top=Int.toString((maxLines - 1) * lineHeight) ++ "px",
      ~boxShadow=backgroundColor(~variation) ++ " -5px 0 4px 0",
      (),
    ),
  ])

let iconElement = (~variation) =>
  switch variation {
  | #info =>
    <div style={ReactDOM.Style.make(~display="flex", ())}>
      <Svg width="20" height="18" viewBox="0 0 16 16">
        <Svg.Path
          d="M7.19314 5.5998C7.19314 5.15798 7.55131 4.7998 7.99314 4.7998H8.00647C8.4483 4.7998 8.80647 5.15798 8.80647 5.5998C8.80647 6.04163 8.4483 6.3998 8.00647 6.3998H7.99314C7.55131 6.3998 7.19314 6.04163 7.19314 5.5998Z"
          fill="#4F70C1"
        />
        <Svg.Path
          d="M7.9998 7.1998C8.44163 7.1998 8.7998 7.55798 8.7998 7.9998V9.5998C9.24163 9.5998 9.5998 9.95798 9.5998 10.3998C9.5998 10.8416 9.24163 11.1998 8.7998 11.1998H7.1998C6.75798 11.1998 6.3998 10.8416 6.3998 10.3998C6.3998 9.95798 6.75798 9.5998 7.1998 9.5998V8.7998C6.75798 8.7998 6.3998 8.44163 6.3998 7.9998C6.3998 7.55798 6.75798 7.1998 7.1998 7.1998H7.9998Z"
          fill="#4F70C1"
        />
        <Svg.Path
          d="M0.799805 7.9998C0.799805 4.02335 4.02335 0.799805 7.9998 0.799805C11.9763 0.799805 15.1998 4.02335 15.1998 7.9998C15.1998 11.9763 11.9763 15.1998 7.9998 15.1998C4.02335 15.1998 0.799805 11.9763 0.799805 7.9998ZM7.9998 2.3998C4.90701 2.3998 2.3998 4.90701 2.3998 7.9998C2.3998 11.0926 4.90701 13.5998 7.9998 13.5998C11.0926 13.5998 13.5998 11.0926 13.5998 7.9998C13.5998 4.90701 11.0926 2.3998 7.9998 2.3998Z"
          fill="#4F70C1"
        />
      </Svg>
    </div>
  }

@react.component
let make = (~title=?, ~text, ~maxLines=0, ~variation) => {
  let textContentRef = React.useRef(Js.Nullable.null)
  let (textClamped, setTextClamped) = React.useState(() => false)
  let (expanded, setExpanded) = React.useState(() => false)

  let textClampingHandler = React.useCallback1(() =>
    switch textContentRef->ReactDomElement.fromRef {
    | Some(domElement) =>
      setTextClamped(_ => domElement->DomElement.scrollHeight > domElement->DomElement.clientHeight)
    | _ => ()
    }
  , [textContentRef])

  React.useLayoutEffect0(() => {
    textClampingHandler()
    None
  })

  ReactAria.Resize.useObserver({
    ref: textContentRef->ReactDOM.Ref.domRef,
    onResize: textClampingHandler,
  })

  let {?style, ?className} = styleProps(~variation)
  let {style: ?contentStyle, className: ?contentClassName} = contentStyleProps()
  let {style: ?contentTitleStyle, className: ?contentTitleClassName} = contentTitleStyleProps(
    ~variation,
  )
  let {style: ?contentTextStyle, className: ?contentTextClassName} = contentTextStyleProps(
    ~variation,
    ~maxLines,
    ~clamped=textClamped,
    ~expanded,
  )
  let {
    style: ?contentTextMoreButtonStyle,
    className: ?contentTextMoreButtonClassName,
  } = contentTextMoreButtonStyleProps(~variation, ~maxLines)

  <div ?style ?className>
    {iconElement(~variation)}
    <div style=?contentStyle className=?contentClassName>
      {switch title {
      | Some(title) =>
        <span style=?contentTitleStyle className=?contentTitleClassName>
          {title->React.string}
        </span>
      | None => React.null
      }}
      <span
        ref={textContentRef->ReactDOM.Ref.domRef}
        style=?contentTextStyle
        className=?contentTextClassName>
        {text->React.string}
        {if textClamped && !expanded {
          <span
            onClick={_ => setExpanded(_ => true)}
            style=?contentTextMoreButtonStyle
            className=?contentTextMoreButtonClassName>
            {Intl.t("More")->React.string}
          </span>
        } else {
          React.null
        }}
      </span>
    </div>
  </div>
}

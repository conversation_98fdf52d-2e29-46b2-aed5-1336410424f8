open Style

let styles = StyleSheet.create({
  "pastille": style(~width=7.->dp, ~height=7.->dp, ~borderRadius=50., ()),
  "successView": style(~backgroundColor=Colors.successColor50, ()),
  "warningView": style(~backgroundColor=Colors.warningColor70, ()),
  "dangerView": style(~backgroundColor=Colors.dangerColor50, ()),
  "blankView": style(
    ~backgroundColor=Colors.neutralColor05,
    ~borderWidth=1.,
    ~borderColor=Colors.neutralColor25,
    (),
  ),
})

let viewStyleFromParams = (~variant, ~color) =>
  [
    switch (variant, color) {
    | (_, Some(color)) => style(~backgroundColor=color, ())
    | (Some(#success), None) => styles["successView"]
    | (Some(#warning), None) => styles["warningView"]
    | (Some(#danger), None) => styles["dangerView"]
    | (None, None) => styles["blankView"]
    },
  ]->arrayStyle

type variant = [#success | #warning | #danger]

@react.component
let make = (~variant: option<variant>=?, ~color: option<Color.t>=?) =>
  <View style={[styles["pastille"], viewStyleFromParams(~variant, ~color)]->arrayStyle} />

let make = React.memo(make)

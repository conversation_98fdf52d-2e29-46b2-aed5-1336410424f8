open Style

let styles = StyleSheet.create({
  "view": style(~alignSelf=#flexStart, ~justifyContent=#center, ~borderRadius=4., ()),
  "text": merge([style(~textAlign=#center, ()), FontFaces.archivoRegularStyle]),
  "normalTextView": style(~color=Colors.warningColor90, ()),
  "normalView": style(~backgroundColor=Colors.warningColor30, ()),
  "informationTextView": style(~color=Colors.secondaryColor70, ()),
  "informationView": style(~backgroundColor=Colors.secondaryColor05, ()),
  "successView": style(~backgroundColor=Colors.successColor10, ()),
  "successTextView": style(~color=Colors.successColor80, ()),
  "dangerView": style(~backgroundColor=Colors.dangerColor10, ()),
  "dangerTextView": style(~color=Colors.dangerColor55, ()),
  "primaryView": style(~backgroundColor=Colors.brandColor10, ()),
  "primaryTextView": style(~color=Colors.brandColor50, ()),
  "importantView": style(~backgroundColor=Colors.warningColor25, ()),
  "importantTextView": style(~color=Colors.Product.whiteTextColor, ()),
  "warningView": style(~backgroundColor=Colors.extraColorK70, ()),
  "warningTextView": style(~color=Colors.extraColorK80, ()),
  "neutralView": style(~backgroundColor=Colors.neutralColor20, ()),
  "neutralTextView": style(~color=Colors.neutralColor100, ()),
  "highlightedView": style(~backgroundColor=Colors.neutralColor00, ()),
  "lightText": style(~color=Colors.neutralColor00, ()),
  "darkText": style(~color=Colors.neutralColor80, ()),
})

let viewStyleFromParams = (~variation, ~highlighted, ~size, ~backgroundColor, ~borderColor) =>
  [
    Some(
      switch (variation, highlighted) {
      | (_, true) => styles["highlightedView"]
      | (#normal, false) => styles["normalView"]
      | (#success, false) => styles["successView"]
      | (#danger, false) => styles["dangerView"]
      | (#warning, false) => styles["warningView"]
      | (#primary, false) => styles["primaryView"]
      | (#neutral, false) => styles["neutralView"]
      | (#information, false) => styles["informationView"]
      | (#important, false) => styles["importantView"]
      | _ => style()
      },
    ),
    Some(
      switch size {
      | #small =>
        style(
          ~minHeight=16.->dp,
          ~height=16.->dp,
          ~paddingHorizontal=4.->dp,
          ~paddingVertical=2.->dp,
          (),
        )
      | #medium =>
        style(
          ~minHeight=20.->dp,
          ~height=20.->dp,
          ~paddingHorizontal=6.->dp,
          ~paddingVertical=4.->dp,
          (),
        )
      | #large =>
        style(
          ~minHeight=24.->dp,
          ~height=24.->dp,
          ~paddingHorizontal=6.->dp,
          ~paddingVertical=4.->dp,
          (),
        )
      },
    ),
    switch backgroundColor {
    | Some(backgroundColor) => Some(style(~backgroundColor, ()))
    | _ => None
    },
    switch borderColor {
    | Some(borderColor) => Some(style(~paddingVertical=3.->dp, ~borderWidth=1., ~borderColor, ()))
    | _ => None
    },
  ]->arrayOptionStyle

let textStyleFromParams = (~variation, ~highlighted, ~size, ~foregroundColor) =>
  [
    Some(
      switch (variation, highlighted) {
      | (#normal, _) => styles["normalTextView"]
      | (#success, _) => styles["successTextView"]
      | (#danger, _) => styles["dangerTextView"]
      | (#warning, _) => styles["warningTextView"]
      | (#neutral, _) => styles["neutralTextView"]
      | (#primary, _) => styles["primaryTextView"]
      | (#information, _) => styles["informationTextView"]
      | (#important, _) => styles["importantTextView"]
      | _ => style()
      },
    ),
    Some(
      switch size {
      | #xsmall | #small => style(~fontSize=10., ())
      | #medium => style(~fontSize=12., ())
      | #large => style(~fontSize=14., ())
      },
    ),
    Some(
      switch foregroundColor {
      | Some(color) => style(~color, ())
      | _ => style()
      },
    ),
  ]->arrayOptionStyle

type size = [#small | #medium | #large]

type variation = [
  | #normal
  | #success
  | #danger
  | #warning
  | #primary
  | #neutral
  | #information
  | #important
]

@react.component
let make = (
  ~children,
  ~highlighted=false,
  ~size: size=#large, // TODO - as new design system gets integrated, will later on be #medium as default
  ~variation=#normal,
  ~borderColor=?,
  ~backgroundColor=?,
  ~foregroundColor=?,
) =>
  <View
    style={[
      styles["view"],
      viewStyleFromParams(~variation, ~size, ~highlighted, ~backgroundColor, ~borderColor),
    ]->arrayStyle}>
    <Text
      style={[
        styles["text"],
        textStyleFromParams(~variation, ~size, ~highlighted, ~foregroundColor),
      ]->arrayStyle}>
      children
    </Text>
  </View>

let make = React.memo(make)

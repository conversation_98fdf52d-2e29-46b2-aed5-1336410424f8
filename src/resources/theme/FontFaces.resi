let fontFamilyFromFontName: [< #Archivo | #LibreFranklin] => string

let styles: {
  "archivoBoldStyle": Style.t,
  "archivoMediumStyle": Style.t,
  "archivoRegularStyle": Style.t,
  "archivoSemiBoldStyle": Style.t,
  "libreFranklinBoldStyle": Style.t,
  "libreFranklinMediumStyle": Style.t,
  "libreFranklinRegularStyle": Style.t,
  "libreFranklinSemiBoldStyle": Style.t,
}

let archivoRegularStyle: Style.t
let archivoMediumStyle: Style.t
let archivoSemiBoldStyle: Style.t
let archivoBoldStyle: Style.t
let libreFranklinRegularStyle: Style.t
let libreFranklinMediumStyle: Style.t
let libreFranklinSemiBoldStyle: Style.t
let libreFranklinBoldStyle: Style.t

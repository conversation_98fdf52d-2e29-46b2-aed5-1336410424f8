// ReactiveConf 2019 - <PERSON>: Rethinking Design Practices
// > https://www.youtube.com/watch?v=jnV1u67_yVg
// Space in Design Systems
// > https://medium.com/eightshapes-llc/space-in-design-systems-188bcbae0d62

open Style

let xxsmall = 2.
let xsmall = 4.
let small = 8.
let xnormal = 10.
let normal = 12.
let xmedium = 14.
let medium = 16.
let large = 20.
let xlarge = 24.
let xxlarge = 32.
let huge = 48.
let xhuge = 64.
let xxhuge = 96.

@inline let xxsmallPx = "2px"
@inline let xsmallPx = "4px"
@inline let smallPx = "8px"
@inline let xnormalPx = "10px"
@inline let normalPx = "12px"
@inline let xmediumPx = "14px"
@inline let mediumPx = "16px"
@inline let largePx = "20px"
@inline let xlargePx = "24px"
@inline let xxlargePx = "32px"
@inline let hugePx = "48px"
@inline let xhugePx = "64px"
@inline let xxhugePx = "96px"

@genType
type t = [
  | #none
  | #xxsmall
  | #xsmall
  | #small
  | #xnormal
  | #normal
  | #xmedium
  | #medium
  | #large
  | #xlarge
  | #xxlarge
  | #huge
  | #xhuge
  | #xxhuge
]

let toFloat = value =>
  switch value {
  | #none => 0.
  | #xxsmall => xxsmall
  | #xsmall => xsmall
  | #small => small
  | #xnormal => xnormal
  | #normal => normal
  | #xmedium => xmedium
  | #medium => medium
  | #large => large
  | #xlarge => xlarge
  | #xxlarge => xxlarge
  | #huge => huge
  | #xhuge => xhuge
  | #xxhuge => xhuge
  }

let styleFromSpace = (~spaceX, ~spaceY) =>
  [
    spaceX->Option.map(spaceX =>
      switch spaceX {
      | #xxsmall => style(~paddingHorizontal=xxsmall->dp, ())
      | #xsmall => style(~paddingHorizontal=xsmall->dp, ())
      | #small => style(~paddingHorizontal=small->dp, ())
      | #xnormal => style(~paddingHorizontal=xnormal->dp, ())
      | #normal => style(~paddingHorizontal=normal->dp, ())
      | #xmedium => style(~paddingHorizontal=xmedium->dp, ())
      | #medium => style(~paddingHorizontal=medium->dp, ())
      | #large => style(~paddingHorizontal=large->dp, ())
      | #xlarge => style(~paddingHorizontal=xlarge->dp, ())
      | #xxlarge => style(~paddingHorizontal=xxlarge->dp, ())
      | #huge => style(~paddingHorizontal=huge->dp, ())
      | #xhuge => style(~paddingHorizontal=xhuge->dp, ())
      | #xxhuge => style(~paddingHorizontal=xxhuge->dp, ())
      | #none => style(~paddingHorizontal=0.->dp, ())
      }
    ),
    spaceY->Option.map(spaceY =>
      switch spaceY {
      | #xxsmall => style(~paddingVertical=xxsmall->dp, ())
      | #xsmall => style(~paddingVertical=xsmall->dp, ())
      | #small => style(~paddingVertical=small->dp, ())
      | #xnormal => style(~paddingVertical=xnormal->dp, ())
      | #normal => style(~paddingVertical=normal->dp, ())
      | #xmedium => style(~paddingVertical=xmedium->dp, ())
      | #medium => style(~paddingVertical=medium->dp, ())
      | #large => style(~paddingVertical=large->dp, ())
      | #xlarge => style(~paddingVertical=xlarge->dp, ())
      | #xxlarge => style(~paddingVertical=xxlarge->dp, ())
      | #huge => style(~paddingVertical=huge->dp, ())
      | #xhuge => style(~paddingVertical=xhuge->dp, ())
      | #xxhuge => style(~paddingHorizontal=xxhuge->dp, ())
      | #none => style(~paddingVertical=0.->dp, ())
      }
    ),
  ]->arrayOptionStyle

let styleExceptionFromSpace = (~spaceTop, ~spaceBottom, ~spaceLeft, ~spaceRight) =>
  [
    spaceTop->Option.map(spaceTop =>
      switch spaceTop {
      | #xxsmall => style(~paddingTop=xxsmall->dp, ())
      | #xsmall => style(~paddingTop=xsmall->dp, ())
      | #small => style(~paddingTop=small->dp, ())
      | #xnormal => style(~paddingTop=xnormal->dp, ())
      | #normal => style(~paddingTop=normal->dp, ())
      | #xmedium => style(~paddingTop=xmedium->dp, ())
      | #medium => style(~paddingTop=medium->dp, ())
      | #large => style(~paddingTop=large->dp, ())
      | #xlarge => style(~paddingTop=xlarge->dp, ())
      | #xxlarge => style(~paddingTop=xxlarge->dp, ())
      | #huge => style(~paddingTop=huge->dp, ())
      | #xhuge => style(~paddingTop=xhuge->dp, ())
      | #xxhuge => style(~paddingHorizontal=xxhuge->dp, ())
      | #none => style(~paddingTop=0.->dp, ())
      }
    ),
    spaceBottom->Option.map(spaceBottom =>
      switch spaceBottom {
      | #xxsmall => style(~paddingBottom=xxsmall->dp, ())
      | #xsmall => style(~paddingBottom=xsmall->dp, ())
      | #small => style(~paddingBottom=small->dp, ())
      | #xnormal => style(~paddingBottom=xnormal->dp, ())
      | #normal => style(~paddingBottom=normal->dp, ())
      | #xmedium => style(~paddingBottom=xmedium->dp, ())
      | #medium => style(~paddingBottom=medium->dp, ())
      | #large => style(~paddingBottom=large->dp, ())
      | #xlarge => style(~paddingBottom=xlarge->dp, ())
      | #xxlarge => style(~paddingBottom=xxlarge->dp, ())
      | #huge => style(~paddingBottom=huge->dp, ())
      | #xhuge => style(~paddingBottom=xhuge->dp, ())
      | #xxhuge => style(~paddingHorizontal=xxhuge->dp, ())
      | #none => style(~paddingBottom=0.->dp, ())
      }
    ),
    spaceLeft->Option.map(spaceLeft =>
      switch spaceLeft {
      | #xxsmall => style(~paddingLeft=xxsmall->dp, ())
      | #xsmall => style(~paddingLeft=xsmall->dp, ())
      | #small => style(~paddingLeft=small->dp, ())
      | #xnormal => style(~paddingLeft=xnormal->dp, ())
      | #normal => style(~paddingLeft=normal->dp, ())
      | #xmedium => style(~paddingLeft=xmedium->dp, ())
      | #medium => style(~paddingLeft=medium->dp, ())
      | #large => style(~paddingLeft=large->dp, ())
      | #xlarge => style(~paddingLeft=xlarge->dp, ())
      | #xxlarge => style(~paddingLeft=xxlarge->dp, ())
      | #huge => style(~paddingLeft=huge->dp, ())
      | #xhuge => style(~paddingLeft=xhuge->dp, ())
      | #xxhuge => style(~paddingHorizontal=xxhuge->dp, ())
      | #none => style(~paddingLeft=0.->dp, ())
      }
    ),
    spaceRight->Option.map(spaceRight =>
      switch spaceRight {
      | #xxsmall => style(~paddingRight=xxsmall->dp, ())
      | #xsmall => style(~paddingRight=xsmall->dp, ())
      | #small => style(~paddingRight=small->dp, ())
      | #xnormal => style(~paddingRight=xnormal->dp, ())
      | #normal => style(~paddingRight=normal->dp, ())
      | #xmedium => style(~paddingRight=xmedium->dp, ())
      | #medium => style(~paddingRight=medium->dp, ())
      | #large => style(~paddingRight=large->dp, ())
      | #xlarge => style(~paddingRight=xlarge->dp, ())
      | #xxlarge => style(~paddingRight=xxlarge->dp, ())
      | #huge => style(~paddingRight=huge->dp, ())
      | #xhuge => style(~paddingRight=xhuge->dp, ())
      | #xxhuge => style(~paddingHorizontal=xxhuge->dp, ())
      | #none => style(~paddingRight=0.->dp, ())
      }
    ),
  ]->arrayOptionStyle

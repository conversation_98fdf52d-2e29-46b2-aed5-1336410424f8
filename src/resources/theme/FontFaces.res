open Style

let fontFamilyFromFontName = fontName =>
  switch fontName {
  | #Archivo => "Archivo"
  | #LibreFranklin => "Libre Franklin"
  }

let styles = StyleSheet.create({
  "archivoRegularStyle": style(
    ~fontFamily=#Archivo->fontFamilyFromFontName,
    ~fontWeight=#_400,
    ~fontStyle=#normal,
    (),
  ),
  "archivoMediumStyle": style(
    ~fontFamily=#Archivo->fontFamilyFromFontName,
    ~fontWeight=#_500,
    ~fontStyle=#normal,
    (),
  ),
  "archivoSemiBoldStyle": style(
    ~fontFamily=#Archivo->fontFamilyFromFontName,
    ~fontWeight=#_600,
    ~fontStyle=#normal,
    (),
  ),
  "archivoBoldStyle": style(
    ~fontFamily=#Archivo->fontFamilyFromFontName,
    ~fontWeight=#_700,
    ~fontStyle=#normal,
    (),
  ),
  "libreFranklinRegularStyle": style(
    ~fontFamily=#LibreFranklin->fontFamilyFromFontName,
    ~fontWeight=#_400,
    ~fontStyle=#normal,
    (),
  ),
  "libreFranklinMediumStyle": style(
    ~fontFamily=#LibreFranklin->fontFamilyFromFontName,
    ~fontWeight=#_500,
    ~fontStyle=#normal,
    (),
  ),
  "libreFranklinSemiBoldStyle": style(
    ~fontFamily=#LibreFranklin->fontFamilyFromFontName,
    ~fontWeight=#_600,
    ~fontStyle=#normal,
    (),
  ),
  "libreFranklinBoldStyle": style(
    ~fontFamily=#LibreFranklin->fontFamilyFromFontName,
    ~fontWeight=#_700,
    ~fontStyle=#normal,
    (),
  ),
})

// REVIEW - doesn't work without `merge`
let archivoRegularStyle = merge([styles["archivoRegularStyle"]])
let archivoMediumStyle = merge([styles["archivoMediumStyle"]])
let archivoSemiBoldStyle = merge([styles["archivoSemiBoldStyle"]])
let archivoBoldStyle = merge([styles["archivoBoldStyle"]])
let libreFranklinRegularStyle = merge([styles["libreFranklinRegularStyle"]])
let libreFranklinMediumStyle = merge([styles["libreFranklinMediumStyle"]])
let libreFranklinSemiBoldStyle = merge([styles["libreFranklinSemiBoldStyle"]])
let libreFranklinBoldStyle = merge([styles["libreFranklinBoldStyle"]])

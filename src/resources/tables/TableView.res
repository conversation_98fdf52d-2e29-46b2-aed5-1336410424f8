module TableFilterBar = {
  @react.component
  let make = (~filters) =>
    <Box spaceX=#large spaceBottom=#xmedium>
      <SliderTrack layout=Adaptative gap=#small compact=true> {filters->React.array} </SliderTrack>
    </Box>
}

// TODO - to improve
module OptionalTableCard = {
  @react.component
  let make = React.memo((~children, ~displayed) =>
    if displayed {
      <Box
        grow=true
        spaceTop=#xmedium
        style={Style.style(~backgroundColor=Colors.neutralColor00, ~borderRadius=6., ())}>
        children
      </Box>
    } else {
      children
    }
  )
}

@react.component
let make = (
  ~data,
  ~columns,
  ~keyExtractor,
  ~disabledRowsKeys=?,
  ~erroredRowsMap=?,
  ~selectionEnabled=?,
  ~selectAllEnabled=?,
  ~initialAllSelected=?,
  ~hideCard=false,
  ~hideReloadingPlaceholder=false,
  ~maxWidth=?,
  ~minHeight=?,
  ~maxHeight=?,
  ~compactRows=?,
  ~placeholderEmptyState=<Placeholder status=NoDataAvailable />,
  ~searchBar=?,
  ~searchPlaceholder=Intl.t("Search"),
  ~filters=[],
  ~typesDrop=?,
  ~sortDescriptor=?,
  ~onSortChange=?,
  ~onSearchQueryChange=?,
  ~onLoadMore as onLoadMoreProp=?,
  ~onSelectChange=?,
  ~onSuccessDrop=?,
  ~onErrorDrop=?,
) => {
  let tableComponent = {
    let onLoadMore = data->AsyncData.isIdle ? onLoadMoreProp : None
    let footerComponent = switch data {
    | AsyncData.Loading => Some(<Placeholder status=Loading />)
    | Reloading(_) if !hideReloadingPlaceholder => Some(<Placeholder status=Loading />)
    | Done(Error(_)) | Reloading(Error(_)) => Some(<Placeholder status=Error />)
    | Done(Ok([])) | NotAsked => Some(placeholderEmptyState)
    | Done(Ok(_)) | Reloading(Ok(_)) =>
      onLoadMoreProp->Option.isSome && data->AsyncData.isReloading
        ? Some(<Placeholder compact=true status=Loading />)
        : None
    }
    let rows = switch data {
    | Reloading(Ok(rows)) | Done(Ok(rows)) => rows
    | _ => []
    }

    <Table
      columns
      rows
      keyExtractor
      ?footerComponent
      ?disabledRowsKeys
      ?erroredRowsMap
      ?maxWidth
      ?minHeight
      ?maxHeight
      ?compactRows
      ?selectionEnabled
      ?selectAllEnabled
      ?initialAllSelected
      ?sortDescriptor
      ?onSortChange
      ?onSelectChange
      ?onLoadMore
    />
  }

  <OptionalTableCard displayed={!hideCard}>
    {switch (searchBar, onSearchQueryChange) {
    | (Some(searchBar), _) => searchBar
    | (_, Some(onChange)) =>
      <Box spaceX=#large spaceBottom=#xmedium>
        <SearchBar placeholder=searchPlaceholder onChange />
      </Box>
    | (None, None) => React.null
    }}
    {if filters->Array.length > 0 {
      <TableFilterBar filters />
    } else {
      React.null
    }}
    {switch (onSuccessDrop, onErrorDrop) {
    | (Some(onSuccess), Some(onError)) =>
      <DropZone types=?typesDrop onSuccess onError> tableComponent </DropZone>
    | _ => tableComponent
    }}
  </OptionalTableCard>
}

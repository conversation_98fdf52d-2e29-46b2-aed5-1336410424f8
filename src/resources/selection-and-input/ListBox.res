open Intl
open WebAPI

let defaultSectionTitle = t("Results")
let defaultItemsPerScroll = 20

type size = [#normal | #compact]

let backgroundColor = (~focused, ~selected, ~disabled) =>
  switch (focused, selected, disabled) {
  | (_, _, true) => Colors.neutralColor10
  | (false, false, false)
  | (false, true, false) => Colors.transparent
  | (true, _, false) => Colors.brandColor10
  }

let fontColor = (~focused, ~selected, ~disabled) =>
  switch (focused, selected, disabled) {
  | (_, _, true) => Colors.neutralColor35
  | (true, false, _) => Colors.brandColor50
  | (false, false, _) => Colors.neutralColor90
  | (_, true, false) => Colors.brandColor50
  }

let tickIconColor = (~disabled) => disabled ? Colors.neutralColor25 : Colors.brandColor40

let noResultItemStyle = ReactDOM.Style.make(
  ~lineHeight="35px",
  ~cursor="default",
  ~paddingLeft="10px",
  ~paddingRight="10px",
  ~backgroundColor=Colors.transparent,
  ~color=Colors.neutralColor50,
  ~fontSize="15px",
  ~fontFamily=#LibreFranklin->FontFaces.fontFamilyFromFontName,
  ~fontStyle="italic",
  (),
)

let itemStyle = (~focused, ~selected, ~disabled, ~size) =>
  ReactDOM.Style.make(
    ~display="flex",
    ~flexDirection="row",
    ~alignItems="center",
    ~minHeight=size === #compact ? "32px" : "35px",
    ~lineHeight=size === #compact ? "20px" : "22px",
    ~cursor=disabled ? "default" : "pointer",
    ~marginTop="1px",
    ~marginBottom="1px",
    ~paddingLeft="10px",
    ~paddingRight="10px",
    ~borderRadius=size === #compact ? "3px" : "5px",
    ~backgroundColor=backgroundColor(~focused, ~selected, ~disabled),
    ~color=fontColor(~focused, ~selected, ~disabled),
    ~fontWeight=selected ? "500" : "400",
    ~fontSize=size === #compact ? "14px" : "15px",
    ~fontFamily=#LibreFranklin->FontFaces.fontFamilyFromFontName,
    (),
  )

let listBottomGradientStyle = ReactDOM.Style.make(
  ~position="absolute",
  ~bottom="0",
  ~left="0",
  ~right="0",
  ~zIndex="1",
  ~pointerEvents="none",
  ~height="16px",
  ~backgroundImage="radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 75%)",
  ~backgroundSize="100% 200%",
  ~backgroundPosition="center top",
  (),
)

let sectionStyle = ReactDOM.Style.make(
  ~display="flex",
  ~flexDirection="column",
  ~overflow="hidden",
  (),
)

let listStyle = (~size) =>
  ReactDOM.Style.make(
    ~display="flex",
    ~overflow="auto",
    ~flexDirection="column",
    ~maxHeight="235px",
    ~padding=size === #compact ? "2px 3px" : "3px 4px",
    (),
  )->ReactDOM.Style.unsafeAddProp("scrollPaddingBlock", "5px")

// TODO - maybe some performance issues to investigate (maybe adds virtualized scrolling)
module ListBoxItem = {
  @react.component
  let make = React.memo((
    ~item: ReactStately.Collection.node,
    ~state: ReactStately.ListBox.state,
    ~size,
  ) => {
    let optionRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {optionProps, selected, focused, disabled} = ReactAria.Option.use(
      ~props={key: item.key},
      ~state,
      ~ref=optionRef,
      (),
    )

    <ReactAria.Spread props=optionProps>
      <div ref=optionRef style={itemStyle(~focused, ~selected, ~disabled, ~size)}>
        <Box grow=true spaceY=#xsmall> {item.rendered} </Box>
        <div style={ReactDOMStyle.make(~display="flex", ~minWidth="14px", ~paddingLeft="12px", ())}>
          {if selected {
            <Icon
              name=#tick_bold size={size === #compact ? 12. : 14.} fill={tickIconColor(~disabled)}
            />
          } else {
            React.null
          }}
        </div>
      </div>
    </ReactAria.Spread>
  })
}

module ListBoxSection = {
  @react.component
  let make = React.memo((
    ~section: ReactStately.Collection.node,
    ~state: ReactStately.ListBox.state,
    ~size,
    ~itemsLimit=?,
  ) => {
    let {itemProps, headingProps, groupProps} = ReactAria.ListBox.Section.use(
      ~props={
        heading: section.rendered,
        \"aria-label": "section",
      },
    )

    let childElements = {
      let children = section.childNodes->ReactStately.Collection.toArray
      let itemsLimit = itemsLimit->Option.getWithDefault(children->Array.length)

      children->Array.slice(~offset=0, ~len=itemsLimit)
    }

    <ReactAria.Spread props=itemProps>
      <div>
        {if (
          section.rendered !== React.null &&
            section.childNodes->ReactStately.Collection.toArray->Array.size > 0
        ) {
          <Box
            spaceLeft=#xnormal
            spaceTop=#xnormal
            spaceBottom=#xsmall
            style={Style.unsafeCss({"cursor": "default"})}>
            <TextStyle size=#xxsmall variation=#normal>
              <ReactAria.Spread props=headingProps>
                <span> {section.rendered} </span>
              </ReactAria.Spread>
            </TextStyle>
          </Box>
        } else {
          React.null
        }}
        <ReactAria.Spread props=groupProps>
          <div style=sectionStyle>
            {childElements
            ->Array.map(item => <ListBoxItem key=item.key size item state />)
            ->React.array}
          </div>
        </ReactAria.Spread>
      </div>
    </ReactAria.Spread>
  })
}

external toReactRef: ReactDOM.domRef => React.ref<Js.Nullable.t<'a>> = "%identity"

@react.component
let make = (
  ~domRef,
  ~state,
  ~props,
  ~size=#normal,
  ~itemsLimit=?,
  ~noResultLabel=?,
  ~loading=false,
  ~onScroll=?,
) => {
  let (scrollable, setScrollable) = React.useState(() => false)
  let (scrollReachedBottom, setScrollReachedBottom) = React.useState(() => false)
  let {listBoxProps} = ReactAria.ListBox.use(~props, ~state, ~ref=domRef, ())

  let handleScrollable = React.useCallback1(() =>
    switch toReactRef(domRef).current->Js.Nullable.toOption {
    | Some(domElement) =>
      setScrollable(_ => domElement->DomElement.scrollHeight > domElement->DomElement.clientHeight)
    | None => ()
    }
  , [domRef])

  React.useLayoutEffect2(() => {
    handleScrollable()
    None
  }, (handleScrollable, domRef))

  ReactAria.Resize.useObserver({
    ref: domRef,
    onResize: handleScrollable,
  })

  let onScroll = React.useCallback0(event => {
    let domElement =
      event
      ->ReactEvent.UI.currentTarget
      ->ReactDomEventTarget.toUnsafeDomEventTarget
      ->EventTarget.unsafeAsDomElement

    let pixelThreshold = 15
    let scrollBottom =
      domElement->DomElement.scrollHeight -
      domElement->DomElement.scrollTop->Float.toInt -
      domElement->DomElement.clientHeight

    setScrollable(_ => scrollBottom > 0)
    setScrollReachedBottom(_ => scrollBottom < pixelThreshold)
    onScroll->Option.forEach(fn => fn(event))
  })

  let noItemPlaceholder =
    <span style=noResultItemStyle>
      {switch noResultLabel {
      | Some(label) => label->React.string
      | None => t("No result found")->React.string
      }}
    </span>

  let listBoxProps = ReactAria.mergeProps2(listBoxProps, {"tabIndex": 0})

  <ReactAria.Spread props=listBoxProps>
    <div style={ReactDOM.Style.make(~position="relative", ())}>
      <div ref=domRef style={listStyle(~size)} onScroll>
        {switch state.collection->ReactStately.Collection.toArray {
        | _ if loading =>
          <Inline align=#center>
            <Box spaceY={size === #compact ? #xsmall : #small}>
              <Spinner size={size === #compact ? 21. : 26.} />
            </Box>
          </Inline>
        | [section]
          if section.childNodes
          ->ReactStately.Collection.toArray
          ->Array.size === 0 => noItemPlaceholder
        | [] => noItemPlaceholder
        | collection =>
          collection
          ->Array.map(section => <ListBoxSection key=section.key size section state ?itemsLimit />)
          ->React.array
        }}
        <AnimatedRender displayed={scrollable && !scrollReachedBottom} animation=#fade duration=250>
          <div style=listBottomGradientStyle />
        </AnimatedRender>
      </div>
    </div>
  </ReactAria.Spread>
}

let make = React.memo(make)

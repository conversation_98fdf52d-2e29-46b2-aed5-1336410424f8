let makeProps: (
  ~value: option<float>,
  ~minValue: float=?,
  ~maxValue: float=?,
  ~step: float=?,
  ~minPrecision: int=?,
  ~precision: int=?,
  ~label: string=?,
  ~tooltip: React.element=?,
  ~errorMessage: string=?,
  ~placeholder: string=?,
  ~appender: InputNumberField.appender=?,
  ~shrinkInput: bool=?,
  ~autoFocused: bool=?,
  ~focused: bool=?,
  ~required: bool=?,
  ~disabled: bool=?,
  ~strongStyle: bool=?,
  ~hideStepper: bool=?,
  ~useGrouping: bool=?,
  ~onChange: option<float> => unit,
  ~onFocus: unit => unit=?,
  ~onBlur: unit => unit=?,
  ~testLocalization: Intl.locale=?,
  ~key: string=?,
  unit,
) => {
  "appender": option<InputNumberField.appender>,
  "autoFocused": option<bool>,
  "disabled": option<bool>,
  "errorMessage": option<string>,
  "focused": option<bool>,
  "strongStyle": option<bool>,
  "hideStepper": option<bool>,
  "tooltip": option<React.element>,
  "label": option<string>,
  "maxValue": option<float>,
  "minPrecision": option<int>,
  "minValue": option<float>,
  "onBlur": option<unit => unit>,
  "onChange": option<float> => unit,
  "onFocus": option<unit => unit>,
  "placeholder": option<string>,
  "precision": option<int>,
  "required": option<bool>,
  "shrinkInput": option<bool>,
  "step": option<float>,
  "testLocalization": option<Intl.locale>,
  "useGrouping": option<bool>,
  "value": option<float>,
}

let make: {
  "appender": option<InputNumberField.appender>,
  "autoFocused": option<bool>,
  "disabled": option<bool>,
  "errorMessage": option<string>,
  "focused": option<bool>,
  "strongStyle": option<bool>,
  "hideStepper": option<bool>,
  "tooltip": option<React.element>,
  "label": option<string>,
  "maxValue": option<float>,
  "minPrecision": option<int>,
  "minValue": option<float>,
  "onBlur": option<unit => unit>,
  "onChange": option<float> => unit,
  "onFocus": option<unit => unit>,
  "placeholder": option<string>,
  "precision": option<int>,
  "required": option<bool>,
  "shrinkInput": option<bool>,
  "step": option<float>,
  "testLocalization": option<Intl.locale>,
  "useGrouping": option<bool>,
  "value": option<float>,
} => React.element

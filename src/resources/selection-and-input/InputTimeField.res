module Segment = {
  let makeStyle = (~focused, ~editable, ~placeholder) => {
    let color = switch (focused, placeholder) {
    | (true, _) => Colors.brandColor50
    | (false, false) => Colors.neutralColor90
    | (false, true) => Colors.placeholderTextColor
    }
    ReactDOM.Style.make(
      ~caretColor=Colors.transparent,
      ~minWidth=editable ? "2ch" : "default",
      ~padding=editable ? "4px 1px" : "3px 1px",
      ~borderRadius="3px",
      ~backgroundColor=focused ? Colors.brandColor10 : "inherit",
      ~color,
      ~textAlign="center",
      (),
    )
  }

  @react.component
  let make = React.memo((~value, ~state) => {
    let (focused, setFocused) = React.useState(() => false)
    let domRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef

    let {segmentProps} = ReactAria.DateSegment.use(~segment=value, ~state, ~ref=domRef)
    let {type_, editable, hasPlaceholder} = value
    let props = ReactAria.mergeProps2(
      segmentProps,
      {
        "style": makeStyle(
          ~focused,
          ~editable=editable && type_ !== #literal,
          ~placeholder=hasPlaceholder,
        ),
        "onFocus": _ => setFocused(_ => true),
        "onBlur": _ => setFocused(_ => false),
      },
    )

    <ReactAria.Spread props>
      <div ref=domRef> {value.text->React.string} </div>
    </ReactAria.Spread>
  })
}

let internationalizationDatetimeFromJsDateObj = jsDate =>
  Internationalization.Date.Time.make(
    ~hours=jsDate->Js.Date.getHours,
    ~minutes=jsDate->Js.Date.getMinutes,
  )

let internationalizationDatetimeToJsDateObj = time => {
  let today = Js.Date.make()
  Js.Date.makeWithYMDHM(
    ~year=today->Js.Date.getFullYear,
    ~month=today->Js.Date.getMonth,
    ~date=today->Js.Date.getDate,
    ~hours=time->Internationalization.Date.Time.getHours,
    ~minutes=time->Internationalization.Date.Time.getMinutes,
    (),
  )
}

let style = ReactDOM.Style.make(~display="flex", ~cursor="text", ())

@react.component
let make = (
  ~label=?,
  ~required=false,
  ~errorMessage=?,
  ~value as initialValue,
  ~onChange,
  ~onFocus=?,
  ~onBlur=?,
  ~testLocalization=?,
) => {
  let (value, setValue) = React.useState(() =>
    initialValue->Option.map(internationalizationDatetimeFromJsDateObj)
  )
  let (focused, setFocused) = React.useState(() => false)
  let (ref, hovered) = Hover.use()
  let domRef = ref->ReactDOM.Ref.domRef

  let onFocusChange = React.useCallback0(value => setFocused(_ => value))
  let props = {
    ReactStately.TimeField.locale: testLocalization->Option.getWithDefault(Intl.locale),
    shouldForceLeadingZeros: true,
    \"aria-label": label->Option.getWithDefault(Intl.t("time")),
    value,
    onChange: value => {
      let value = Js.Nullable.toOption(value)
      setValue(_ => value)
      switch value {
      | Some(value) => onChange(internationalizationDatetimeToJsDateObj(value))
      | None => ()
      }
    },
    onFocusChange,
    ?onFocus,
    ?onBlur,
  }
  let state = ReactStately.TimeField.useState(~props)
  let {fieldProps} = ReactAria.TimeField.use(~props, ~state, ~ref=domRef)

  ReactUpdateEffect.use1(() => {
    if !focused && value->Option.isNone {
      state.clearSegment(. ~type_=#hour)
      state.clearSegment(. ~type_=#minute)
    }
    None
  }, [focused])

  <View ref>
    <OverlayTriggerView
      ?label
      preset=#inputField({required, ?errorMessage})
      growContent=true
      hideIcon=true
      hovered
      focused>
      <ReactAria.Spread props=fieldProps>
        <div style>
          {state.segments
          ->Array.mapWithIndex((index, segment) =>
            <Segment key={index->Int.toString} value=segment state />
          )
          ->React.array}
        </div>
      </ReactAria.Spread>
    </OverlayTriggerView>
  </View>
}

let make = React.memo(make)

// NOTE - Legacy component, ComboBox is better

open Style

let styles = StyleSheet.create({
  "item": style(~paddingHorizontal=Spaces.xnormal->dp, ~overflow=#hidden, ()),
  "itemHovered": style(~backgroundColor=Colors.neutralColor10, ()),
  "itemCentered": style(~alignSelf=#center, ()),
  "itemText": merge([
    FontFaces.libreFranklinRegularStyle,
    style(
      ~fontSize=FontSizes.normal,
      ~color=Colors.neutralColor90,
      ~lineHeight=36.,
      ~letterSpacing=0.125,
      (),
    ),
  ]),
})

let itemStyleFromParams = (~hovered, ~centered) =>
  [
    hovered ? Some(styles["itemHovered"]) : None,
    centered ? Some(styles["itemCentered"]) : None,
  ]->arrayOptionStyle

@react.component
let make = (~children=React.null, ~name: option<string>=?, ~onPress=?) => {
  let (ref, hovered) = Hover.use()

  <View
    style={[
      styles["item"],
      itemStyleFromParams(
        ~hovered=hovered && onPress->Option.isSome,
        ~centered=name->Option.isNone,
      ),
    ]->arrayStyle}>
    {switch (name, onPress) {
    | (Some(name), Some(onPress)) =>
      <Touchable ref onPress={_ => onPress()}>
        <Text style={styles["itemText"]}> {name->React.string} </Text>
      </Touchable>
    | _ => children
    }}
  </View>
}

let make = React.memo(make)

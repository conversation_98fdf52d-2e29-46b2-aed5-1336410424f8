// NOTE - At some point, some parts of InputDateField component should be moved to primitives.

@react.component
let make = (
  ~label=?,
  ~required=false,
  ~errorMessage=?,
  ~disabledFutureDays=false,
  ~value,
  ~onChange,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())
  let (focused, setFocused) = React.useState(() => false)

  React.useEffect1(_ => {
    setFocused(_ => popover.opened)
    None
  }, [popover.opened])

  let onChange = value => {
    onChange(value)
    popover.onRequestClose()
  }

  let isDayBlocked = day => {
    let day = Js.Date.fromFloat(day->Js.Date.getTime)
    let now = Js.Date.make()
    day->Js.Date.setHours(0.)->ignore
    day->Js.Date.setMinutes(0.)->ignore
    now->Js.Date.setHours(0.)->ignore
    now->Js.Date.setMinutes(0.)->ignore
    disabledFutureDays && day->Js.Date.getTime > now->Js.Date.getTime
  }

  <>
    <Touchable
      ref=popoverTriggerRef
      ariaProps=popoverAriaProps.triggerProps
      onPress={_ => popover.onRequestToggle()}>
      <OverlayTriggerView
        preset=#inputField({?errorMessage, required})
        icon=#calendar
        ?label
        hovered=triggerHovered
        active=popover.opened
        focused>
        {switch value {
        | None => <TextStyle variation=#normal> {Intl.t("Select a date")->React.string} </TextStyle>
        | Some(date) =>
          <TextStyle> {date->Intl.dateTimeFormat(~dateStyle=#long)->React.string} </TextStyle>
        }}
      </OverlayTriggerView>
    </Touchable>
    {if popover.opened {
      <Popover
        triggerRef=popoverTriggerRef
        placement=#"bottom start"
        dismissable=false // NOTE - hack for https://github.com/react-dates/react-dates/issues/2014
        state={
          opened: popover.opened,
          onRequestToggle: popover.onRequestToggle,
          onRequestClose: popover.onRequestClose,
        }>
        <Popover.Dialog
          ariaProps=popoverAriaProps.overlayProps
          style={ReactDOMStyle.make(~minHeight="300px", ~maxWidth="316px", ())}>
          <DayPickerSingle value isDayBlocked onChange />
        </Popover.Dialog>
      </Popover>
    } else {
      React.null
    }}
  </>
}

let make = React.memo(make)

// NOTE - Legacy component, ComboBox is better

open Intl
open Style
open WebAPI

let styles = StyleSheet.create({
  "wrapper": style(~alignSelf=#flexStart, ~width=100.->pct, ()),
  "append": style(
    ~position=#absolute,
    ~width=40.->dp,
    ~height=40.->dp,
    ~top=0.->dp,
    ~right=0.->dp,
    ~bottom=0.->dp,
    ~alignItems=#center,
    ~justifyContent=#center,
    ~backgroundColor="#FFF",
    (),
  ),
  "itemsList": merge([
    style(
      ~maxHeight=210.->dp,
      ~borderColor=Colors.neutralColor20,
      ~borderTopWidth=1.,
      ~borderBottomLeftRadius=5.,
      ~borderBottomRightRadius=5.,
      (),
    ),
    unsafeCss({"overflow": "auto"}),
  ]),
})

let wrapperStyleFromParams = (~expendableWidth) =>
  [expendableWidth ? Some(style(~width=100.->pct, ())) : None]->arrayOptionStyle

@react.component
let make = (
  ~children,
  ~expendableWidth: bool,
  ~items: option<array<'item>>,
  ~itemToValue: 'item => string,
  ~itemToOption: 'item => string,
  ~opened=?,
  ~searchQueryDelay=400,
  ~onSearchQueryChange=?,
  ~onChange,
  ~onToggle,
  ~onEndReached,
  ~renderEndItem=() => React.null,
  ~renderEndActions: option<unit => array<React.element>>=?,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())

  let (searchQuery, setSearchQuery) = React.useState(() => "")
  let debouncedSearchQuery = ReactUpdateDebounced.use(searchQuery, ~delay=searchQueryDelay)

  let textInputRef = React.useRef(Js.Nullable.null)
  let itemsListRef = React.useRef(Js.Nullable.null)

  React.useEffect1(() => {
    switch opened {
    | Some(false) => popover.onRequestClose()
    | _ => ()
    }
    None
  }, [opened])

  // Executes onEndReached callback with scrolling meta data when event fires
  React.useEffect1(() => {
    let domElement = itemsListRef->ReactDomElement.fromRef
    let handler = _ =>
      domElement
      ->Option.map(domElement => {
        let offsetY = domElement->DomElement.scrollHeight
        let scrolledY = domElement->DomElement.scrollTop +. domElement->DomElement.offsetHeight
        let offsetFromEndReached = offsetY - scrolledY->Float.toInt

        if offsetFromEndReached < 100 {
          onEndReached(offsetFromEndReached)
        }
      })
      ->ignore

    domElement
    ->Option.map(domElement => domElement->DomElement.addEventListener("scroll", handler))
    ->ignore

    Some(
      () =>
        domElement
        ->Option.map(domElement => domElement->DomElement.removeEventListener("scroll", handler))
        ->ignore,
    )
  }, [itemsListRef.current])

  React.useEffect2(() => {
    onToggle(popover.opened)
    None
  }, (popover.opened, textInputRef.current))

  React.useEffect1(() => {
    switch onSearchQueryChange {
    | Some(onSearchQueryChange) => onSearchQueryChange(debouncedSearchQuery)
    | _ => ()
    }
    None
  }, [debouncedSearchQuery])

  let renderedItems = React.useMemo1(() =>
    switch items {
    | Some(items) =>
      items->Array.mapWithIndex((index, item) =>
        <SearchListItem
          key={item->itemToValue ++ index->Int.toString}
          name={item->itemToOption}
          onPress={_ => {
            onChange(item)
            popover.onRequestClose()
          }}
        />
      )
    | _ => []
    }
  , [items])

  // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
  let popover = {
    ...popover,
    onRequestClose: () =>
      if !triggerHovered {
        popover.onRequestClose()
      },
  }

  <>
    <View style={[styles["wrapper"], wrapperStyleFromParams(~expendableWidth)]->Style.arrayStyle}>
      <Touchable
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        onPress={_ => popover.onRequestToggle()}>
        children
      </Touchable>
    </View>
    {if popover.opened {
      <Popover
        triggerRef=popoverTriggerRef
        state=popover
        placement=#"bottom start"
        modal=false // FIXME - tmp: doesn't open on iPad if true
        layout={expendableWidth ? #triggerMinWidth : #auto}>
        <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
          <InputSearch
            loading={items->Option.isNone}
            onRequestClear={() => setSearchQuery(_ => "")}
            autoFocus=true
            bordered=false
            placeholder={t("Search")}
            value=searchQuery
            onChange={searchQuery => setSearchQuery(_ => searchQuery)}
          />
          {switch items {
          | Some(_) =>
            <View ref=itemsListRef style={styles["itemsList"]}>
              <Box spaceLeft=#xnormal spaceTop=#small spaceBottom=#xxsmall>
                <TextStyle size=#xxsmall variation=#normal>
                  {t("Search results")->React.string}
                </TextStyle>
              </Box>
              {renderedItems
              ->Array.concat([<View key="end-list-item"> {renderEndItem()} </View>])
              ->React.array}
              {switch renderEndActions {
              | Some(renderEndActions) =>
                <>
                  <Box spaceLeft=#xnormal spaceTop=#xsmall spaceBottom=#xxsmall>
                    <TextStyle size=#xxsmall variation=#normal>
                      {t("Other suggestions")->React.string}
                    </TextStyle>
                  </Box>
                  {renderEndActions()
                  ->Array.mapWithIndex((index, element) =>
                    <View key={index->Int.toString}> element </View>
                  )
                  ->React.array}
                  <Box spaceBottom=#xsmall />
                </>
              | _ => React.null
              }}
            </View>
          | _ =>
            <View style={styles["itemsList"]}>
              <SearchListItem key="end-list-item"> {renderEndItem()} </SearchListItem>
            </View>
          }}
        </Popover.Dialog>
      </Popover>
    } else {
      React.null
    }}
  </>
}

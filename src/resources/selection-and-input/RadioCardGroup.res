open StyleX

type item = {
  value: string,
  title: string,
  tooltip?: string,
  description?: string,
  disabled?: bool,
}

module RadioCardItem = {
  module RadioButton = {
    let styles = StyleX.create({
      "RadioCardItem_RadioButton_root": style(
        ~minWidth="16px",
        ~width="16px",
        ~height="16px",
        ~borderRadius="50%",
        ~background="white",
        ~outline="solid",
        ~transition="outline-color .1s ease-in",
        (),
      ),
    })

    let styleProps = (~selected, ~disabled, ~hovered, ~pressed) =>
      StyleX.props([
        styles["RadioCardItem_RadioButton_root"],
        switch (selected, pressed, disabled) {
        | (true, _, false) => style(~outlineWidth="5px", ~outlineOffset="-5.5px", ())
        | (false, true, false) => style(~outlineWidth="3px", ~outlineOffset="-3.5px", ())
        | _ => style(~outlineWidth="1.5px", ~outlineOffset="-2.5px", ())
        },
        style(
          ~outlineColor=if disabled {
            Colors.neutralColor20
          } else if selected {
            Colors.brandColor50
          } else if hovered {
            Colors.brandColor60
          } else {
            Colors.neutralColor30
          },
          (),
        ),
        style(~transition=selected ? "outline-width .045s cubic-bezier(0, 0, 0, 1)" : "none", ()),
        style(~background=disabled ? Colors.neutralColor05 : "white", ()),
      ])

    @react.component
    let make = React.memo((~selected, ~disabled, ~hovered, ~pressed) => {
      let {?style, ?className} = styleProps(~selected, ~disabled, ~hovered, ~pressed)
      <div ?style ?className />
    })
  }

  let styles = StyleX.create({
    "RadioCardItem_root": style(
      ~flex="1",
      ~boxSizing=#"border-box",
      ~padding="18px",
      ~outline="solid",
      ~borderRadius="5px",
      ~transition="outline-color 0.05s ease-out",
      (),
    ),
    "RadioCardItem_container": style(
      ~display=#flex,
      ~flexDirection=#row,
      ~gap=Spaces.xnormalPx,
      (),
    ),
    "RadioCardItem_textContainer": style(
      ~display=#flex,
      ~flexDirection=#column,
      ~flex="1",
      ~gap="6px",
      (),
    ),
    "RadioCardItem_title": style(
      ~font=`normal 700 16px "Archivo"`,
      ~lineHeight=Spaces.mediumPx,
      ~wordBreak=#normal,
      (),
    ),
    "RadioCardItem_description": style(
      ~font=`normal 400 12px "Libre Franklin"`,
      ~lineHeight=Spaces.xmediumPx,
      (),
    ),
  })

  let styleProps = (~selected, ~disabled, ~hovered, ~pressed) =>
    StyleX.props([
      styles["RadioCardItem_root"],
      switch (selected, pressed, disabled) {
      | (true, false, false) | (false, true, false) =>
        style(~outlineWidth="2px", ~outlineOffset="-2px", ())
      | _ => style(~outlineWidth="1.5px", ~outlineOffset="-1.5px", ())
      },
      style(
        ~outlineColor=if disabled {
          Colors.neutralColor20
        } else if selected || hovered {
          Colors.brandColor50
        } else {
          Colors.neutralColor30
        },
        (),
      ),
      style(~background=disabled ? Colors.neutralColor05 : "white", ()),
    ])
  let containerStyleProps = () => StyleX.props([styles["RadioCardItem_container"]])
  let textContainerStyleProps = () => StyleX.props([styles["RadioCardItem_textContainer"]])
  let titleStyleProps = (~disabled) =>
    StyleX.props([
      styles["RadioCardItem_title"],
      style(~color=disabled ? Colors.neutralColor35 : Colors.neutralColor90, ()),
    ])
  let descriptionStyleProps = (~disabled) =>
    StyleX.props([
      styles["RadioCardItem_description"],
      style(~color=disabled ? Colors.neutralColor35 : Colors.neutralColor90, ()),
    ])

  @react.component
  let make = React.memo((
    ~title,
    ~tooltip=?,
    ~description=?,
    ~groupState,
    ~selected,
    ~disabled=false,
    ~value,
  ) => {
    let (hoveredRef, hovered) = Hover.use()
    let {pressProps, pressed} = ReactAria.Press.use(~props={allowTextSelectionOnPress: false})
    let inputDomRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef

    let {?style, ?className} = styleProps(~selected, ~disabled, ~hovered, ~pressed)
    let {className: ?containerClassName} = containerStyleProps()
    let {className: ?textContainerClassName} = textContainerStyleProps()
    let {style: ?titleStyle, className: ?titleClassName} = titleStyleProps(~disabled)
    let {style: ?descriptionStyle, className: ?descriptionClassName} = descriptionStyleProps(
      ~disabled,
    )

    let children =
      <div className=?containerClassName>
        <RadioButton selected disabled hovered pressed />
        <div className=?textContainerClassName>
          <Inline grow=true align=#spaceBetween>
            <span style=?titleStyle className=?titleClassName> {title->React.string} </span>
            {switch tooltip {
            | Some(tooltipText) =>
              <Offset right=0. top={-8.5} width=10.>
                <TooltipIcon>
                  <Tooltip.Span text=tooltipText />
                </TooltipIcon>
              </Offset>
            | None => React.null
            }}
          </Inline>
          {switch description {
          | Some(description) =>
            <div style=?descriptionStyle className=?descriptionClassName>
              {description->React.string}
            </div>
          | None => React.null
          }}
        </div>
      </div>

    let props = {
      ReactAria.Radio.value,
      disabled,
      children,
    }
    let {inputProps} = ReactAria.Radio.use(~props, ~state=groupState, ~ref=inputDomRef)

    <ReactAria.Spread props=pressProps>
      <label ref={hoveredRef->ReactDOM.Ref.domRef} ?style ?className>
        <ReactAria.VisuallyHidden>
          <ReactAria.Spread props=inputProps>
            <input ref=inputDomRef />
          </ReactAria.Spread>
        </ReactAria.VisuallyHidden>
        {children}
      </label>
    </ReactAria.Spread>
  })
}

let styles = StyleX.create({
  "root": style(~display=#flex, ~gap=Spaces.mediumPx, ()),
})

let styleProps = (~orientation) =>
  StyleX.props([
    styles["root"],
    style(
      ~flexDirection=switch orientation {
      | #horizontal => #row
      | #vertical => #column
      },
      (),
    ),
  ])

@react.component
let make = (~orientation=#horizontal, ~items, ~value, ~disabled=false, ~onChange) => {
  let props = {
    ReactStately.RadioGroup.value: value->Js.Nullable.return,
    orientation,
    disabled,
    onChange,
  }
  let state = ReactStately.RadioGroup.useState(~props)
  let {radioGroupProps} = ReactAria.Radio.Group.use(~props, ~state)

  React.useEffect2(() => {
    if !(items->Array.some(item => item.value === value)) {
      let defaultValue = items[0]->Option.map(item => item.value)->Js.Nullable.fromOption
      state.onRequestSelectedValue(defaultValue)
    }
    None
  }, (value, items))

  let {?style, ?className} = styleProps(~orientation)

  <ReactAria.Spread props=radioGroupProps>
    <div ?style ?className>
      {items
      ->Array.map(item => {
        let {title, ?tooltip, ?description, value} = item
        let disabled = item.disabled->Option.getWithDefault(false) || disabled
        let selected = state.selectedValue === value->Js.Nullable.return

        <RadioCardItem
          key=value title ?tooltip ?description disabled selected groupState=state value
        />
      })
      ->React.array}
    </div>
  </ReactAria.Spread>
}

let make = React.memo(make)

open Style

let styles = StyleSheet.create({
  "view": style(
    ~width=40.->dp,
    ~height=26.->dp,
    ~position=#relative,
    ~display=#flex,
    ~borderColor=Colors.neutralColor20,
    ~borderRadius=34.,
    ~borderWidth=1.,
    (),
  ),
  "round": style(
    ~position=#absolute,
    ~height=20.->dp,
    ~width=20.->dp,
    ~left=2.->dp,
    ~bottom=2.->dp,
    ~borderRadius=100.,
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
})

let roundStyleFromParams = (~value) =>
  [
    if value {
      merge([
        style(
          ~transform=[ReactNative.Style.translateX(~translateX=14.)],
          ~backgroundColor=Colors.brandColor50,
          (),
        ),
        unsafeCss({
          "transitionDuration": ".25s",
        }),
      ])
    } else {
      merge([
        style(~transform=[ReactNative.Style.translateX(~translateX=0.)], ()),
        unsafeCss({
          "transitionDuration": ".25s",
        }),
      ])
    },
  ]->arrayStyle

let viewStyleFromParams = (~disabled) =>
  [disabled ? style(~opacity=0.4, ()) : style(~opacity=1., ())]->arrayStyle

@react.component
let make = (~value, ~onChange=?, ~disabled=false) => {
  <Touchable
    disabled
    onPress={_ =>
      switch onChange {
      | Some(handler) => handler(!value)
      | None => ()
      }}>
    <View style={[styles["view"], viewStyleFromParams(~disabled)]->arrayStyle}>
      <View style={[styles["round"], roundStyleFromParams(~value)]->arrayStyle} />
    </View>
  </Touchable>
}

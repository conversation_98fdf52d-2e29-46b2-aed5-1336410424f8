open Intl
open Style

let styles = StyleSheet.create({
  "listTypingValidation": style(
    ~flexDirection=#row,
    ~flexWrap=#wrap,
    ~paddingTop=8.->dp,
    ~marginBottom=-10.->dp,
    (),
  ),
  "itemTypingValidation": style(~flexDirection=#row, ~flexBasis=50.->pct, ()),
  "iconItemTypingValidation": style(~marginTop=4.->dp, ~marginRight=-2.->dp, ()),
  "textItemTypingValidation": merge([
    FontFaces.libreFranklinRegularStyle,
    style(~color=Colors.neutralColor70, ~fontSize=FontSizes.xsmall, ()),
  ]),
  "labelWrapper": style(~flexDirection=#row, ()),
  "textLabel": merge([
    FontFaces.libreFranklinMediumStyle,
    style(~color=Colors.neutralColor50, ~fontSize=FontSizes.xsmall, ()),
  ]),
})

type rule =
  | MinStr
  | MinLower
  | MinUpper
  | MinDigit

let minStr = MinStr
let minLower = MinLower
let minUpper = MinUpper
let minDigit = MinDigit

// NOTE - these regex are also implied in FormSchema (grouped)
let ruleToReg = x =>
  switch x {
  | MinStr => %re("/.{10,}.*/")
  | MinLower => %re("/(.*[a-z]){1,}.*/")
  | MinUpper => %re("/(.*[A-Z]){1,}.*/")
  | MinDigit => %re("/(.*[0-9]){1,}.*/")
  }

let ruleToErrorMsg = x =>
  switch x {
  | MinStr => "At least 10 characters"
  | MinLower => "One lowercase letter"
  | MinUpper => "One uppercase letter"
  | MinDigit => "One digit"
  }

let applyRule = (value, rule) => Js.Re.test_(rule->ruleToReg, value)

type error = (bool, rule)

type state = {rules: array<error>}

type action = FieldValueChanged(string)

@react.component
let make = (
  ~label,
  ~value,
  ~focused,
  ~required=?,
  ~errorMessage=?,
  ~onChange=?,
  ~onFocus,
  ~onBlur,
  ~placeholder=?,
  ~showTypingValidation=false,
) => {
  let ({rules}, dispatch) = React.useReducer(
    (state, action) =>
      switch action {
      | FieldValueChanged(textField) => {
          rules: state.rules->Array.map(ruleState => {
            let (_, rule) = ruleState
            let isValid = textField->applyRule(rule)
            (isValid, rule)
          }),
        }
      },
    {
      rules: [(false, MinStr), (false, MinLower), (false, MinUpper), (false, MinDigit)],
    },
  )
  let (show, setShow) = React.useState(_ => true)

  React.useEffect1(() => {
    dispatch(FieldValueChanged(value))
    None
  }, [value])

  <Stack>
    <InputTextField
      label
      fieldAction={
        Field.Action.text: t(show ? "Show" : "Hide"),
        handler: Callback(_ => setShow(show => !show)),
      }
      ?required
      ?placeholder
      ?errorMessage
      focused
      secureTextEntry=show
      value
      ?onChange
      onFocus
      onBlur
    />
    {showTypingValidation
      ? <View style={styles["listTypingValidation"]}>
          {rules
          ->Array.mapWithIndex((i, rule) =>
            <View key={i->Int.toString} style={styles["itemTypingValidation"]}>
              <View style={styles["iconItemTypingValidation"]}>
                <Icon name=#oval fill={fst(rule) ? Colors.successColor50 : Colors.neutralColor15} />
              </View>
              <Text style={styles["textItemTypingValidation"]}>
                {t(snd(rule)->ruleToErrorMsg)->React.string}
              </Text>
            </View>
          )
          ->React.array}
        </View>
      : React.null}
  </Stack>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["label"] === newProps["label"] &&
  oldProps["errorMessage"] === newProps["errorMessage"] &&
  oldProps["value"] === newProps["value"] &&
  oldProps["focused"] === newProps["focused"] &&
  oldProps["placeholder"] === newProps["placeholder"] &&
  oldProps["onBlur"] === newProps["onBlur"]
)

open StyleX

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~flex="1",
    ~flexDirection=#row,
    ~alignItems=#center,
    ~overflow=#hidden,
    ~height="40px",
    ~backgroundColor=Colors.neutralColor00,
    (),
  ),
  "item": style(
    ~display=#flex,
    ~flex="1",
    ~alignItems=#center,
    ~justifyContent=#center,
    ~minWidth="40px",
    ~height="40px",
    ~padding="0 15px",
    ~borderRadius="5px",
    ~boxSizing=#"border-box",
    (),
  ),
})

let styleProps = () => StyleX.props([styles["root"]])

let itemStyleProps = (~active, ~compact, ~firstPosition, ~lastPosition) =>
  StyleX.props([
    styles["item"],
    style(
      ~backgroundColor=active ? Colors.neutralColor05 : "white",
      ~border="1px solid " ++ (active ? Colors.neutralColor50 : Colors.neutralColor20),
      ~borderRight="1px solid transparent",
      ~padding=compact ? "0 10px" : "0 15px",
      (),
    ),
    switch (firstPosition, lastPosition) {
    | (true, true) => style(~borderRadius="5px", ())
    | (true, false) =>
      style(
        ~borderRightColor=active ? Colors.neutralColor50 : Colors.neutralColor20,
        ~borderTopRightRadius="0px",
        ~borderBottomRightRadius="0px",
        (),
      )
    | (false, false) => style(~borderRadius="0px", ())
    | (false, true) =>
      style(
        ~borderRightColor=active ? Colors.neutralColor50 : Colors.neutralColor20,
        ~borderTopLeftRadius="0px",
        ~borderBottomLeftRadius="0px",
        (),
      )
    },
  ])

let itemTextStyleProps = (~compact) =>
  StyleX.props([
    compact
      ? style(~color=Colors.neutralColor45, ~font=`normal 400 16px "Libre Franklin"`, ())
      : style(~color=Colors.neutralColor90, ~font=`normal 400 14px "Archivo"`, ()),
  ])

@react.component
let make = (
  ~label=?,
  ~grow=false,
  ~compact=false,
  ~tooltip=?,
  ~value as derivedValue,
  ~required: bool,
  ~onChange,
  ~errorMessage=?,
  ~options,
  ~optionToText,
) => {
  let (value, setValue) = React.useState(_ => derivedValue)

  React.useEffect1(_ => {
    if derivedValue != value {
      setValue(_ => derivedValue)
    }
    None
  }, [derivedValue])

  let handlePress = value => {
    setValue(_ => value)
    WebAPI.requestAnimationFrame(_ => onChange(value))
  }

  let {?style, ?className} = styleProps()
  let {style: ?itemTextStyle, className: ?itemTextClassName} = itemTextStyleProps(~compact)

  <Field ?label ?tooltip required ?errorMessage>
    <div ?style ?className>
      {options
      ->List.mapWithIndex((index, item) => {
        let {style: ?itemStyle, className: ?itemClassName} = itemStyleProps(
          ~active=item === value,
          ~compact,
          ~firstPosition=index === 0,
          ~lastPosition=index + 1 === List.length(options),
        )
        <Touchable
          style={ReactDOM.Style.make(
            ~display=grow ? "flex" : "block",
            ~flex=grow ? "1" : "auto",
            (),
          )}
          onPress={_ => handlePress(item)}>
          <div style=?itemStyle className=?itemClassName>
            <span style=?itemTextStyle className=?itemTextClassName>
              {item->optionToText->React.string}
            </span>
          </div>
        </Touchable>
      })
      ->List.toArray
      ->React.array}
    </div>
  </Field>
}

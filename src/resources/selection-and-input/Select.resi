type size = [#normal | #compact]

type overlayFooterLink = {
  text: string,
  to: Navigation.to,
}

type item<'value> = {
  key: string,
  value: 'value,
  label: string,
  description?: string,
  disabled?: bool,
  sticky?: bool,
}
type section<'itemValue> = {
  items: array<item<'itemValue>>,
  title?: string,
}

type itemSet<'itemValue> = {
  sectionsItems: array<item<'itemValue>>,
  searchableSectionsItems: array<item<'itemValue>>,
  filteredSectionsItems: array<item<'itemValue>>,
}

let processItems: (
  ~sections: array<section<'itemValue>>,
  ~filterByLabel: string => bool,
) => itemSet<'itemValue>

@genType @react.component
let make: (
  ~label: string=?,
  ~placeholder: string=?,
  ~disabled: bool=?,
  ~highlighted: bool=?,
  ~loading: bool=?,
  ~defaultOpen: bool=?,
  ~searchable: bool=?,
  ~sections: array<section<'itemValue>>,
  ~preset: OverlayTriggerView.preset,
  ~size: size=?,
  ~excludeFromTabOrder: bool=?,
  ~overlayPlacement: ReactAria.Overlay.Position.placement=?,
  ~overlayNoResultLabel: string=?,
  ~overlayFooterLink: overlayFooterLink=?,
  ~renderTriggerView: (
    ~children: React.element,
    ~item: option<item<'itemValue>>,
    ~hovered: bool,
    ~active: bool,
    ~focused: bool,
  ) => React.element=?,
  ~renderItemContent: item<'itemValue> => React.element=?,
  ~value: 'itemValue,
  ~onChange: 'itemValue => unit,
  ~onToggle: bool => unit=?,
) => React.element

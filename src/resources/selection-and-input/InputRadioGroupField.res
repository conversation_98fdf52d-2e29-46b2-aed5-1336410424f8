// NOTE - the component works today but at some point
// it should use @react-aria/radio to improve tests/accessibility

open Style

let styles = StyleSheet.create({
  "root": style(~marginTop=Spaces.xsmall->dp, ~paddingRight=20.->dp, ()),
  "item": style(~flexDirection=#row, ~paddingVertical=0.->dp, ()),
  "itemIcon": merge([
    unsafeCss({"height": "16px", "width": "16px"}),
    style(
      ~borderRadius=50.,
      ~backgroundColor=Colors.neutralColor00,
      ~borderWidth=1.,
      ~justifyContent=#center,
      ~alignItems=#center,
      ~shadowColor=Colors.neutralColor100,
      ~shadowOffset=shadowOffset(~width=0.0, ~height=0.1),
      ~shadowOpacity=0.08,
      ~shadowRadius=1.,
      (),
    ),
  ]),
  "itemIconEnabled": style(~borderColor=Colors.neutralColor50, ()),
  "itemIconDisabled": style(~borderColor=Colors.neutralColor15, ()),
  "itemText": merge([
    FontFaces.libreFranklinRegularStyle,
    style(~marginTop=-1.->dp, ~marginLeft=8.->dp, ~fontSize=FontSizes.normal, ()),
  ]),
  "itemTextDisabled": merge([style(~color=Colors.neutralColor50, ())]),
})

let itemIconStyleFromParams = (~active, ~hovered) =>
  [
    styles["itemIcon"],
    switch (active, hovered) {
    | (true, _)
    | (_, true) =>
      styles["itemIconEnabled"]
    | _ => styles["itemIconDisabled"]
    },
  ]->arrayStyle

let itemTextStyleFromParams = (~disabled, ~active) =>
  [
    Some(styles["itemText"]),
    switch (disabled, active) {
    | (true, false) => Some(styles["itemTextDisabled"])
    | _ => None
    },
  ]->arrayOptionStyle

module Item = {
  @react.component
  let make = React.memo((~children, ~active, ~onPress, ~disabled) => {
    let (ref, hovered) = Hover.use()

    <Touchable disabled onPress={_ => onPress()}>
      <View ref style={styles["item"]}>
        <View style={itemIconStyleFromParams(~active, ~hovered={!disabled && hovered})}>
          <Icon
            size=16. fill={active ? Colors.brandColor60 : Colors.neutralColor00} name=#inner_oval
          />
        </View>
        <Text style={itemTextStyleFromParams(~disabled, ~active)}> children </Text>
      </View>
    </Touchable>
  })
}

@react.component
let make = (
  ~label=?,
  ~value,
  ~required,
  ~onChange,
  ~isEqualValue=(a, b) => a === b,
  ~errorMessage=?,
  ~options,
  ~optionToText,
  ~optionToDisable=?,
) => {
  <Field ?label required ?errorMessage>
    <View style={styles["root"]}>
      <Stack space=#normal>
        {options
        ->Array.map(item =>
          <Item
            active={isEqualValue(value, item)}
            onPress={() => onChange(item)}
            disabled={switch optionToDisable {
            | Some(optionToDisable) => optionToDisable(item)
            | None => false
            }}>
            {item->optionToText->React.string}
          </Item>
        )
        ->React.array}
      </Stack>
    </View>
  </Field>
}

// TODO - eventually should replace the following components:
// SearchListPopover ; SearchListItem
// FIXME - minor arrow selection issue skipping an option sometime
open Intl
open Style

type overlayFooterLink = {
  text: string,
  to: Navigation.to,
}

type item<'value> = {
  key: string,
  value: 'value,
  label: string,
  description?: string,
  disabled?: bool,
  sticky?: bool,
}

type section<'itemValue> = {
  items: array<item<'itemValue>>,
  title?: string,
}

type itemSet<'itemValue> = {
  sectionsItems: array<item<'itemValue>>,
  searchableSectionsItems: array<item<'itemValue>>,
  filteredSectionsItems: array<item<'itemValue>>,
}

module TriggerButton = {
  @react.component
  let make = (
    ~triggerRef,
    ~variation: OverlayTriggerView.preset,
    ~size,
    ~label=?,
    ~placeholder,
    ~state: ReactStately.Select.state,
    ~props: ReactAria.Button.props,
    ~item: option<item<'value>>,
    ~valueProps,
    ~hovered,
    ~highlighted,
    ~excludeFromTabOrder,
    ~renderTriggerView=?,
  ) => {
    let contentElement =
      <ReactAria.Spread props=valueProps>
        {switch item {
        | Some({label}) => <span> {label->React.string} </span>
        // TODO - placeholder/unselected styling should be handled in OverlayTriggerView
        | _ =>
          <TextStyle variation=#normal size={size === #compact ? #xsmall : #normal}>
            {placeholder->React.string}
          </TextStyle>
        }}
      </ReactAria.Spread>

    let disabled = props.disabled->Option.getWithDefault(false)
    let ariaProps = {
      ...props,
      \"aria-labelledby": ?props.\"aria-labelledby"->Option.map(x =>
        x->Js.String2.replace(props.id->Option.getWithDefault(""), "")
      ),
      onPressStart: _ => (),
    }

    let active = state.opened
    let focused = state.focused

    <Touchable ref=triggerRef ariaProps excludeFromTabOrder onPress={_ => state.onRequestToggle()}>
      {switch renderTriggerView {
      | Some(render) =>
        // NOTE - `item` is passed instead of the render `state.selectedItem.rendered`
        // to get rid of the `item.description` rendered node.
        // REVIEW - an "unsafe" alternative could be to extract the `rendered.props.children[0]` element
        // this is the recommanded ReactAria way https://react-spectrum.adobe.com/react-aria/useListBox.html#complex-options
        render(~children=contentElement, ~item, ~hovered, ~active, ~focused) // REVIEW - yet to see if `highlighted` should be added
      | None =>
        <OverlayTriggerView
          preset=variation size ?label disabled hovered active focused highlighted>
          contentElement
        </OverlayTriggerView>
      }}
    </Touchable>
  }
}

module SelectSearchBox = {
  let styles = StyleSheet.create({
    "root": style(~borderBottomWidth=1., ~borderColor=Colors.neutralColor15, ()),
  })

  @react.component
  let make = React.memo((~value, ~onChange, ~onRequestClear) =>
    // NOTE - makes component focusable to not close the overlay on click
    <div tabIndex=0>
      // NOTE - override the focus handling by <ReactAria.Overlay> of the Popover
      <ReactAria.Focus.Scope autoFocus=true restoreFocus=false>
        <View style={styles["root"]}>
          <InputSearch
            loading=false onRequestClear bordered=false placeholder={t("Search")} value onChange
          />
        </View>
      </ReactAria.Focus.Scope>
    </div>
  )
}

module SelectFooterLink = {
  let style = ReactDOM.Style.make(
    ~padding=`${Spaces.normal->Float.toString}px ${Spaces.xmedium->Float.toString}px`,
    ~border=`1px solid ${Colors.neutralColor15}`,
    (),
  )
  @react.component
  let make = React.memo((~text, ~to, ~onRequestClose) =>
    // NOTE - makes component focusable to not close the overlay on click
    <div tabIndex=0 style>
      <TextLink text icon=#external_link to openNewTab=true onPress={() => onRequestClose()} />
    </div>
  )
}

module SelectionManager = {
  let rec getNextKeyFromState = (state, ~key, ~direction) => {
    let isDisabled = (~key) =>
      Js.Array.from(state.ReactStately.Select.disabledKeys)->Array.some(current => current === key)

    let nextKey = switch direction {
    | #above =>
      state.collection.getKeyBefore(. ~key)
      ->Js.Nullable.toOption
      ->Option.orElse(state.collection.getLastKey(.)->Js.Nullable.toOption)
    | #below =>
      state.collection.getKeyAfter(. ~key)
      ->Js.Nullable.toOption
      ->Option.orElse(state.collection.getFirstKey(.)->Js.Nullable.toOption)
    }

    nextKey->Option.flatMap(key => {
      let nextItem = state.collection.getItem(. ~key)->Js.Nullable.toOption

      nextItem->Option.flatMap(({type_, key}) =>
        switch (type_, isDisabled(~key)) {
        | (#item, false) => Some(key)
        | _ => state->getNextKeyFromState(~key, ~direction)
        }
      )
    })
  }

  let use = (~state: ReactStately.Select.state) => {
    let stateRef = React.useRef(state)

    // NOTE - state as react ref to get latest values inside onKeyDown event handler
    React.useEffect1(() => {
      stateRef.current = state
      None
    }, [state])

    let onKeyDown = React.useCallback0(event => {
      let state = stateRef.current
      let focusedKey = state.selectionManager.focusedKey

      // NOTE - stopPropagation prevents uncontrolled behavior happening when no search input is focused
      // NOTE - preventDefault to not move search input cursor when scrolling selection with arrow keys
      switch WebAPI.KeyboardEvent.key(event) {
      | "Enter" =>
        switch state.selectedKey->Js.Nullable.toOption {
        | Some(selectedKey) if focusedKey === selectedKey => state.onRequestClose()
        | _ => state.onRequestSelectKey(focusedKey)
        }
      | "ArrowUp" =>
        if state.opened {
          event->WebAPI.KeyboardEvent.stopPropagation
          event->WebAPI.KeyboardEvent.preventDefault
        }
        state
        ->getNextKeyFromState(~key=focusedKey, ~direction=#above)
        ->Option.forEach(key => state.selectionManager.onRequestFocusedKey(. ~key=Some(key)))
      | "ArrowDown" =>
        if state.opened {
          event->WebAPI.KeyboardEvent.stopPropagation
          event->WebAPI.KeyboardEvent.preventDefault
        }
        state
        ->getNextKeyFromState(~key=focusedKey, ~direction=#below)
        ->Option.forEach(key => state.selectionManager.onRequestFocusedKey(. ~key=Some(key)))
      | _ => ()
      }
    })

    React.useEffect1(() => {
      let document = WebAPI.document->WebAPI.Document.asDomElement

      state.focused
        ? document->WebAPI.DomElement.addKeyDownEventListener(onKeyDown)
        : document->WebAPI.DomElement.removeKeyDownEventListener(onKeyDown)

      Some(_ => document->WebAPI.DomElement.removeKeyDownEventListener(onKeyDown))
    }, [state.focused])

    state
  }
}

let processItems = (~sections: array<section<'itemValue>>, ~filterByLabel) => {
  let sectionsItems = sections->Array.flatMap(section => section.items)
  let searchableSectionsItems =
    sectionsItems->Array.keep(item => !(item.sticky->Option.getWithDefault(false)))
  let filteredSectionsItems =
    sectionsItems->Array.keep(item =>
      !(searchableSectionsItems->Array.some(current => current === item)) ||
      filterByLabel(item.label)
    )

  {sectionsItems, searchableSectionsItems, filteredSectionsItems}
}

let itemDescriptionStyle = ReactDOM.Style.make(~color=Colors.neutralColor50, ())

type size = [#normal | #compact]

@react.component
let make = (
  ~label=?,
  ~placeholder=t("Select"),
  ~disabled=false,
  ~highlighted as triggerHighlighted=false,
  ~loading=false,
  ~defaultOpen=false,
  ~searchable=?,
  ~sections,
  ~preset,
  ~size=#normal,
  ~excludeFromTabOrder=false,
  ~overlayPlacement=#"bottom start",
  ~overlayNoResultLabel=?,
  ~overlayFooterLink=?,
  ~renderTriggerView=?,
  ~renderItemContent=?,
  ~value,
  ~onChange,
  ~onToggle=?,
) => {
  let {contains} = ReactAria.Filter.use({sensitivity: #base})
  let (itemsLimit, setItemsLimit) = React.useState(_ => ListBox.defaultItemsPerScroll)
  let (filterValue, setFilterValue) = React.useState(() => "")
  let (triggerRef, triggerHovered) = Hover.use()
  let listBoxRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef

  let filterByLabel = label => contains(label, filterValue)
  let {sectionsItems, searchableSectionsItems, filteredSectionsItems} = processItems(
    ~sections,
    ~filterByLabel,
  )

  let disabledKeys = sectionsItems->Array.keepMap(item =>
    switch item.disabled {
    | Some(true) => Some(item.key)
    | _ => None
    }
  )

  let nodeElements = React.useMemo2(() =>
    sections->Array.map(section => {
      let defaultSectionTitle =
        section.title->Option.mapWithDefault(React.null, title => title->React.string)
      let filteredSectionsItems =
        section.items->Array.keep(
          item => filteredSectionsItems->Array.some(current => current === item),
        )

      <ReactStately.ListBox.Section title=defaultSectionTitle>
        {filteredSectionsItems
        ->Array.map(
          item =>
            <ReactStately.ListBox.Item key=item.key textValue=item.label>
              {switch renderItemContent {
              | Some(render) => render(item)
              | None =>
                <span>
                  {item.label->React.string}
                  {item.description->Option.mapWithDefault(
                    React.null,
                    desc => <span style=itemDescriptionStyle> {(" " ++ desc)->React.string} </span>,
                  )}
                </span>
              }}
            </ReactStately.ListBox.Item>,
        )
        ->React.array}
      </ReactStately.ListBox.Section>
    })
  , (sections, searchableSectionsItems))

  let selectedKey = {
    // NOTE - deep comparison to ensure resolution for any datatype
    let matchingItem = sectionsItems->Array.getBy(item => item.value == value)
    let matchingKey = matchingItem->Option.map(item => item.key)

    matchingKey->Js.Nullable.fromOption
  }

  let props = {
    ReactStately.Select.\"aria-label": label->Option.getWithDefault("select"),
    children: nodeElements,
    defaultOpen,
    placeholder,
    disabled,
    disabledKeys,
    selectedKey,
    onSelectionChange: key =>
      key
      ->Js.Nullable.toOption
      ->Option.flatMap(key => sectionsItems->Array.getBy(item => item.key === key))
      ->Option.map(item => item.value)
      ->Option.forEach(onChange),
  }

  let state = ReactStately.Select.useState(~props)
  let {triggerProps, valueProps, menuProps} = ReactAria.Select.use(
    ~props,
    ~state,
    ~ref=triggerRef->ReactDOM.Ref.domRef,
  )

  let state = SelectionManager.use(~state)

  React.useEffect1(() => {
    setFilterValue(_ => "")
    None
  }, [state.selectedKey])

  React.useEffect1(() => {
    if !state.focused {
      setFilterValue(_ => "")
      state.selectionManager.onRequestFocusedKey(. ~key=None)
    }
    None
  }, [state.focused])

  React.useEffect1(() => {
    let firstRelevantItem =
      searchableSectionsItems->Array.getBy(item => contains(item.label, filterValue))

    switch firstRelevantItem {
    | Some({key}) if filterValue !== "" =>
      state.selectionManager.onRequestFocusedKey(. ~key=Some(key))
    | _ => ()
    }
    None
  }, [filterValue])

  ReactUpdateEffect.use1(() => {
    onToggle->Option.forEach(fn => fn(state.opened))
    None
  }, [state.opened])

  React.useEffect1(() => {
    let firstKey =
      state.collection.getFirstKey(.)
      ->Js.Nullable.toOption
      ->Option.flatMap(key => state.collection.getKeyAfter(. ~key)->Js.Nullable.toOption)
    let noItemSelected = state.selectedKey->Js.Nullable.isNullable
    // NOTE - ensures overlay focusing in some rerendering/usage cases
    let firstItemSelected = firstKey === state.selectedKey->Js.Nullable.toOption

    if state.opened && (noItemSelected || firstItemSelected) {
      state.selectionManager.onRequestFocusedKey(. ~key=firstKey)
    }
    None
  }, [state.opened])

  let onScroll = React.useCallback0(event => {
    let element =
      event
      ->ReactEvent.UI.currentTarget
      ->ReactDomEventTarget.toUnsafeDomEventTarget
      ->WebAPI.EventTarget.unsafeAsDomElement
    let scrollHeight = element->WebAPI.DomElement.scrollHeight
    let clientHeight = element->WebAPI.DomElement.clientHeight
    let scrollOffset = scrollHeight - clientHeight * 2
    if element->WebAPI.DomElement.scrollTop > scrollOffset->Int.toFloat {
      setItemsLimit(itemsLimit => itemsLimit + ListBox.defaultItemsPerScroll)
    }
  })

  let onRequestToggle = state.onRequestToggle
  let onRequestClose = React.useCallback1(() =>
    if !triggerHovered {
      state.onRequestFocused(false)
      state.onRequestClose()
    }
  , [triggerHovered])

  let opened = state.opened
  let searchable = searchable->Option.getWithDefault(sectionsItems->Array.length > 5)
  let listBoxProps = ReactAria.mergeProps2(menuProps, {"shouldUseVirtualFocus": searchable})
  let selectedItem =
    state.selectedKey
    ->Js.Nullable.toOption
    ->Option.flatMap(key => sectionsItems->Array.getBy(item => item.key === key))

  <>
    <TriggerButton
      variation=preset
      size
      excludeFromTabOrder
      ?label
      placeholder
      hovered=triggerHovered
      highlighted=triggerHighlighted
      ?renderTriggerView
      triggerRef
      props=triggerProps
      item=selectedItem
      valueProps
      state
    />
    {if opened {
      <Popover
        size
        placement=overlayPlacement
        layout=#triggerMinWidth
        modal=false
        triggerRef
        state={opened, onRequestToggle, onRequestClose}>
        {if searchable {
          <SelectSearchBox
            value=filterValue
            onChange={value => setFilterValue(_ => value)}
            onRequestClear={() => setFilterValue(_ => "")}
          />
        } else {
          React.null
        }}
        <ListBox
          size
          loading
          noResultLabel=?overlayNoResultLabel
          itemsLimit
          domRef=listBoxRef
          props=listBoxProps
          state={state->ReactStately.ListBox.fromSelectState}
          onScroll
        />
        {switch overlayFooterLink {
        | Some({text, to}) => <SelectFooterLink text to onRequestClose />
        | None => React.null
        }}
      </Popover>
    } else {
      React.null
    }}
  </>
}

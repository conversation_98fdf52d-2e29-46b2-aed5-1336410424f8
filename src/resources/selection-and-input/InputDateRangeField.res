open Intl

let {defaultFocusedInput} = module(DayPickerRange)

let copyDate = date => date->Js.Date.getTime->Js.Date.fromFloat

let startOfDay = date =>
  Js.Date.setHoursMSMs(
    date->copyDate,
    ~hours=0.,
    ~minutes=0.,
    ~seconds=0.,
    ~milliseconds=0.,
    (),
  )->Js.Date.fromFloat

let endOffDay = date =>
  Js.Date.setHoursMSMs(
    date->copyDate,
    ~hours=23.,
    ~minutes=59.,
    ~seconds=59.,
    ~milliseconds=999.,
    (),
  )->Js.Date.fromFloat

@react.component
let make = (
  ~label,
  ~value,
  ~placeholder=?,
  ~tooltip=?,
  ~errorMessage=?,
  ~required=false,
  ~onChange,
) => {
  let mounted = React.useRef(false)
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())

  let (dateRange, setDateRange) = React.useState(_ =>
    switch value {
    | Some(value) => (Some(value->fst), Some(value->snd))
    | None => (None, None)
    }
  )
  let (focusedInput, setFocusedInput) = React.useState(_ => defaultFocusedInput)

  React.useEffect1(() => {
    switch (value, fst(dateRange), snd(dateRange)) {
    | (Some((startDateA, endDateA)), Some(startDateB), Some(endDateB))
      if startDateA->Js.Date.getTime !== startDateB->Js.Date.getTime ||
        endDateA->Js.Date.getTime === endDateB->Js.Date.getTime => ()
    | (None, None, None) => ()
    | _ =>
      setDateRange(_ =>
        switch value {
        | Some(value) => (Some(value->fst), Some(value->snd))
        | None => (None, None)
        }
      )
    }
    None
  }, [value])

  let onFocusChange = React.useCallback0(focusedInput =>
    setFocusedInput(_ =>
      switch focusedInput->Js.Nullable.toOption {
      | Some(focusedInput) => focusedInput
      | _ => defaultFocusedInput
      }
    )
  )

  React.useEffect2(() => {
    switch dateRange {
    | (Some(startDate), Some(endDate)) => onChange(Some((startDate, endDate)))
    | (Some(_), None) => ()
    | _ =>
      if mounted.current {
        onChange(None)
      } else {
        ()
      }
    }
    None
  }, dateRange)

  React.useEffect1(() => {
    mounted.current = true
    Some(() => mounted.current = false)
  }, [])

  let onChange = ((startDate, endDate)) => {
    setDateRange(_ => (
      switch startDate {
      | Some(startDate) => Some(startDate->startOfDay)
      | _ => None
      },
      switch endDate {
      | Some(endDate) => Some(endDate->endOffDay)
      | _ => None
      },
    ))
    if startDate->Option.isSome && endDate->Option.isSome {
      popover.onRequestClose()
    }
  }

  let handleResetDate = React.useCallback0(_ => {
    setDateRange(_ => (None, None))
    popover.onRequestClose()
  })

  let formattedValue = switch dateRange {
  | (Some(startDate), Some(endDate)) =>
    startDate->Intl.dateTimeFormat(~dateStyle=#short) ++
    ` → ` ++
    endDate->Intl.dateTimeFormat(~dateStyle=#short)
  | (_, _) => ""
  }

  let (startDate, endDate) = dateRange

  <>
    <Touchable ref=popoverTriggerRef onPress={_ => popover.onRequestToggle()}>
      <OverlayTriggerView
        preset=#inputField({required, ?tooltip, ?errorMessage})
        label
        icon=#calendar
        active=popover.opened
        hovered=triggerHovered>
        {switch (placeholder, value) {
        | (Some(placeholder), None) =>
          <TextStyle variation=#normal> {placeholder->React.string} </TextStyle>
        | _ => <TextStyle> {formattedValue->React.string} </TextStyle>
        }}
      </OverlayTriggerView>
    </Touchable>
    {if popover.opened {
      <Popover
        triggerRef=popoverTriggerRef
        placement=#"bottom start"
        dismissable=false // NOTE - hack for https://github.com/react-dates/react-dates/issues/2014
        state={
          opened: popover.opened,
          onRequestToggle: popover.onRequestToggle,
          onRequestClose: popover.onRequestClose,
        }>
        <Popover.Dialog
          ariaProps=popoverAriaProps.overlayProps
          style={ReactDOMStyle.make(~minHeight="300px", ())}>
          <DayPickerRange startDate endDate focusedInput onChange onFocusChange />
          <Box spaceLeft=#large spaceTop=#xsmall spaceBottom=#xmedium>
            <TextIconButton
              size=#small
              icon=#delete_light
              disabled={endDate->Option.isNone}
              onPress=handleResetDate>
              {t("Reset period")->React.string}
            </TextIconButton>
          </Box>
        </Popover.Dialog>
      </Popover>
    } else {
      React.null
    }}
  </>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps["label"] === newProps["label"] &&
  oldProps["value"] === newProps["value"] &&
  oldProps["errorMessage"] === newProps["errorMessage"]
)

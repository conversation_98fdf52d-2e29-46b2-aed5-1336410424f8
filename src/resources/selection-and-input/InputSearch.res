open Style

module SearchPrepender = {
  @react.component
  let make = (~loading, ~onRequestInputFocus) =>
    <View style={style(~width=16.->dp, ~height=18.->dp, ~position=#relative, ())}>
      <View style={style(~position=#absolute, ~left=-1.->dp, ())}>
        <AnimatedRender displayed=loading duration=250 animation=#fade>
          <Spinner size=18. />
        </AnimatedRender>
      </View>
      <AnimatedRender displayed={!loading} duration=100 animation=#fade>
        <Touchable
          excludeFromTabOrder=true
          style={ReactDOM.Style.make(~cursor="default", ())}
          onPress={_ => onRequestInputFocus()}>
          <Icon name=#search size=17.5 fill=Colors.neutralColor25 stroke=Colors.neutralColor25 />
        </Touchable>
      </AnimatedRender>
    </View>
}

module EraseAppender = {
  @react.component
  let make = (~onRequestInputFocus, ~onRequestInputClear) =>
    <IconButton
      name=#close_medium
      size=17.
      color=Colors.neutralColor25
      hoveredColor=Colors.neutralColor50
      onPress={_ => {
        onRequestInputFocus()
        onRequestInputClear()
      }}
    />
}

let style = (~focused, ~hovered, ~disabled, ~bordered) =>
  ReactDOM.Style.make(
    ~height="40px",
    ~overflow="hidden",
    ~display="flex",
    ~flexDirection="row",
    ~alignItems="center",
    ~boxSizing="border-box",
    ~padding=`0 ${Spaces.normal->Float.toString}px`,
    ~columnGap=Spaces.small->Float.toString ++ "px",
    ~backgroundColor=TextFieldStyle.backgroundColor(~disabled),
    ~border=bordered ? "1px solid" : "none",
    ~borderColor=TextFieldStyle.borderColor(~focused, ~hovered, ~errored=false, ~disabled),
    ~borderRadius=TextFieldStyle.borderRadiusPx,
    (),
  )

let textInputStyle = (~disabled) =>
  ReactDOM.Style.make(
    ~flex="1", // NOTE - necessary on firefox/safari
    ~width="0", // NOTE - necessary on firefox/safari
    ~height="35px",
    ~color=TextFieldStyle.color(~disabled),
    ~fontSize=TextFieldStyle.fontSizePx,
    ~textOverflow="ellipsis",
    (),
  )

@react.component
let make = (
  ~loading,
  ~onRequestClear,
  ~disabled=false,
  ~bordered=true,
  ~focused=false,
  ~placeholder=?,
  ~autoFocus=?,
  ~containerRef: option<ReactDOM.Ref.currentDomRef>=?,
  ~inputRef=?,
  ~ariaProps=?,
  ~value="",
  ~onChange=?,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let inputRef = inputRef->Option.getWithDefault(React.useRef(Js.Nullable.null))
  let (ref, hovered) = Hover.use(~ref=?containerRef, ())

  let focusInput = () =>
    switch inputRef.current->Js.Nullable.toOption {
    | Some(input) => ReactDomElement.focus(input)
    | None => ()
    }

  let style = style(~focused, ~hovered, ~bordered, ~disabled)
  let textInputStyle = textInputStyle(~disabled)

  let value =
    ariaProps->Option.flatMap(ariaProps => ariaProps.JsxDOM.value)->Option.getWithDefault(value)

  <div style ref={ref->ReactDOM.Ref.domRef}>
    <SearchPrepender loading onRequestInputFocus=focusInput />
    <TextInput
      ?ariaProps
      readOnly=disabled
      ?placeholder
      ?autoFocus
      style=textInputStyle
      inputRef={inputRef->ReactDOM.Ref.domRef}
      value
      ?onChange
      ?onFocus
      ?onBlur
    />
    {if value !== "" && !disabled {
      <EraseAppender onRequestInputFocus=focusInput onRequestInputClear=onRequestClear />
    } else {
      React.null
    }}
  </div>
}

let make = React.memo(make)

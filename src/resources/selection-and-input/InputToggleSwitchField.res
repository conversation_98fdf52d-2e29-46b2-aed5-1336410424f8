open Style

let styles = StyleSheet.create({
  "view": style(
    ~display=#flex,
    ~flexDirection=#row,
    ~alignItems=#center,
    ~paddingBottom=0.->dp,
    ~justifyContent=#spaceBetween,
    (),
  ),
  "text": merge([
    FontFaces.libreFranklinRegularStyle,
    style(~marginTop=-1.->dp, ~color=Colors.neutralColor90, ~fontSize=FontSizes.xsmall, ()),
  ]),
  "badge": style(~marginRight=12.->dp, ()),
})

@react.component
let make = (~label, ~badge=?, ~required, ~errorMessage=?, ~value, ~disabled=false, ~onChange) => {
  <Field ?errorMessage>
    <View style={[styles["view"]]->arrayStyle}>
      <Touchable onPress={_ => onChange(!value)} disabled>
        <Box spaceRight=#xmedium>
          <TextStyle> {label->Field.makeLabel(~required)->React.string} </TextStyle>
        </Box>
      </Touchable>
      <Inline>
        {switch badge {
        | Some(badge) =>
          <View style={styles["badge"]}>
            <Badge variation=#information> {badge->React.string} </Badge>
          </View>
        | _ => React.null
        }}
        <ToggleSwitch onChange value disabled />
      </Inline>
    </View>
  </Field>
}

let make = React.memo(make)

// NOTE - At some point, the DropZone component should be revamped
// and moved to primitives.

open Intl
open Style

let styles = StyleSheet.create({
  "main": style(
    ~backgroundColor=Colors.brandColor00,
    ~alignItems=#center,
    ~borderWidth=1.,
    ~borderColor=Colors.brandColor50,
    ~borderStyle=#dashed,
    (),
  ),
  "textWrapper": style(~paddingVertical=70.->dp, ()),
  "textTitle": merge([
    FontFaces.archivoBoldStyle,
    style(
      ~textAlign=#center,
      ~color=Colors.brandColor50,
      ~fontSize=FontSizes.huge,
      ~letterSpacing=0.125,
      ~paddingBottom=4.->dp,
      (),
    ),
  ]),
  "textSubtitle": merge([
    FontFaces.libreFranklinRegularStyle,
    style(
      ~maxWidth=310.->dp,
      ~textAlign=#center,
      ~color=Colors.neutralColor70,
      ~fontSize=FontSizes.normal,
      ~letterSpacing=0.125,
      (),
    ),
  ]),
})

@react.component
let make = (
  ~children as placeholderContent,
  ~customSubtitle=?,
  ~types=?,
  ~maxSizeMb=1.,
  ~disabled=false,
  ~onSuccess,
  ~onError,
) => {
  let (dragging, setDragging) = React.useState(() => false)
  let dropZoneRef = React.useRef(Js.Nullable.null)
  let dropTargetRef = React.useRef(Js.Nullable.null)
  let disabledRef = React.useRef(disabled)

  // Handle dropped files by checking identity then executing passed parser
  let onDrop = React.useCallback0(event => {
    let data = event->WebAPI.DragEvent.dataTransfer
    let file = data->WebAPI.DataTransfer.files->Array.get(0)

    switch (file, types) {
    | (Some(file), Some(types))
      if file->FilePicker.isFileBadType(~types) && file->FilePicker.isFileBadExtension(~types) =>
      onError(FileBadExtension(types)->FilePicker.messageFromError)
    | (Some(file), _) if file->File.size > maxSizeMb *. 1048576. =>
      onError(FileTooHeavy->FilePicker.messageFromError)
    | (Some(file), _) => onSuccess(file)
    | _ => onError(FileNotFound->FilePicker.messageFromError)
    }

    setDragging(_ => false)
    event->WebAPI.DragEvent.preventDefault
  })

  // Add or remove below listed event listeners
  let eventListenersHandler = React.useCallback0((ref, listener) => {
    open WebAPI

    let eventsList = [
      (
        "dragenter",
        event =>
          if !disabledRef.current {
            setDragging(_ => true)
            dropTargetRef.current = event->DragEvent.target->Js.Nullable.return
          },
      ),
      (
        "dragleave",
        event => {
          let currentElement = event->DragEvent.target->EventTarget.unsafeAsDomElement
          let refElement =
            dropTargetRef.current->Js.Nullable.toOption->Option.map(EventTarget.unsafeAsDomElement)

          if Some(currentElement) === refElement {
            setDragging(_ => false)
          }
        },
      ),
      (
        "dragover",
        event => {
          event->DragEvent.preventDefault
          event->DragEvent.stopPropagation
        },
      ),
      (
        "drop",
        event =>
          if !disabledRef.current {
            onDrop(event)
          } else {
            event->DragEvent.preventDefault
          },
      ),
    ]

    ref
    ->ReactDomElement.fromRef
    ->Option.map(domElement =>
      eventsList->Array.forEach(((eventName, handler)) => domElement->listener(eventName, handler))
    )
  })

  // Bind drop interaction to the children element zone
  React.useEffect0(() => {
    dropZoneRef->eventListenersHandler(WebAPI.DomElement.addEventListener)->ignore

    Some(() => dropZoneRef->eventListenersHandler(WebAPI.DomElement.removeEventListener)->ignore)
  })

  React.useEffect1(() => {
    disabledRef.current = disabled
    None
  }, [disabled])

  // Formats file types text for dropping message placeholder
  let placeholderTypes = React.useMemo0(() =>
    switch types {
    | Some([fileType]) => fileType->FilePicker.extensionFromType
    | Some(fileTypes) =>
      "(" ++
      (fileTypes->Array.reduce("", (acc, fileType) =>
        acc ++ ((acc->Js.String2.length > 0 ? " " : "") ++ fileType->FilePicker.extensionFromType)
      ) ++
      ")")
    | _ => ""
    }
  )

  <View ref=dropZoneRef>
    {if dragging && !disabled {
      <View style={styles["main"]}>
        <View style={styles["textWrapper"]}>
          <Stack>
            <Text style={styles["textTitle"]}>
              {t("Drop your file in this area")->React.string}
            </Text>
            <Text style={styles["textSubtitle"]}>
              {switch customSubtitle {
              | Some(text) => text->React.string
              | None =>
                template(
                  t("Import your {{types}} file by drag and dropping it in here."),
                  ~values={"types": placeholderTypes},
                  (),
                )->React.string
              }}
            </Text>
          </Stack>
        </View>
      </View>
    } else {
      placeholderContent
    }}
  </View>
}

let make = React.memo(make)

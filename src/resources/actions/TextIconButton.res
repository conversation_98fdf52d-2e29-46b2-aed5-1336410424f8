open Style

let styles = StyleSheet.create({
  "normalText": FontFaces.libreFranklinRegularStyle,
  "importantText": FontFaces.archivoSemiBoldStyle,
  "lightText": style(~color=Colors.neutralColor20, ()),
  "grayText": style(~color=Colors.neutralColor100, ()),
  "darkText": style(~color=Colors.brandColor60, ()),
})

let iconSizeFromParams = (~size) =>
  switch size {
  | #tiny | #xxsmall | #xsmall => 16.
  | #small => 18.
  | #normal | _ => 20.
  }

let iconColorFromParams = (~disabled, ~hovered) =>
  switch (disabled, hovered) {
  | (true, _) => Colors.neutralColor20
  | (false, true) => Colors.brandColor60
  | (false, _) => Colors.neutralColor100
  }

let textStyleFromParams = (~variation, ~size, ~disabled, ~hovered) =>
  [
    switch variation {
    | #normal => styles["normalText"]
    | #important => styles["importantText"]
    },
    switch size {
    | #tiny | #xxsmall | #xsmall => FontSizes.styleFromSize(#xsmall)
    | #small => FontSizes.styleFromSize(#small)
    | #normal | _ => FontSizes.styleFromSize(#large)
    },
    switch (disabled, hovered) {
    | (true, _) => styles["lightText"]
    | (false, true) => styles["darkText"]
    | (false, _) => styles["grayText"]
    },
  ]->arrayStyle

type variation = [#normal | #important]

@react.component
let make = React.forwardRef((
  ~children,
  ~variation=#important,
  ~size=#normal,
  ~disabled=false,
  ~icon,
  ~textTooltip=?,
  ~onPress,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  <Touchable ref disabled onPress>
    <Inline space=#xsmall>
      <Icon
        name=icon size={iconSizeFromParams(~size)} fill={iconColorFromParams(~disabled, ~hovered)}
      />
      <Text style={textStyleFromParams(~variation, ~size, ~disabled, ~hovered)}> children </Text>
      {switch textTooltip {
      | Some(textTooltip) =>
        <Box spaceTop=#xxsmall>
          <TooltipIcon variation=#info altTriggerRef=ref>
            <Tooltip.Span text=textTooltip />
          </TooltipIcon>
        </Box>
      | None => React.null
      }}
    </Inline>
  </Touchable>
})

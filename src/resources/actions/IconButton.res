open Style

let styles = StyleSheet.create({
  "view": style(~borderRadius=50., ~justifyContent=#center, ~alignItems=#center, ()),
  "xxsmallView": style(~width=20.->dp, ~height=26.->dp, ()),
  "xsmallView": style(~width=26.->dp, ~height=26.->dp, ()),
  "smallView": style(~width=35.->dp, ~height=35.->dp, ()),
  "normalView": style(~width=40.->dp, ~height=40.->dp, ()),
})

let viewStyleFromParams = (~marginSize as size, ~borderedColor, ~hoveredColor) =>
  [
    switch size {
    | Some(#xxsmall) => styles["xxsmallView"]
    | Some(#xsmall) => styles["xsmallView"]
    | Some(#small) => styles["smallView"]
    | Some(#normal) => styles["normalView"]
    | _ => style()
    },
    switch borderedColor {
    | Some(color) =>
      style(
        ~borderWidth=Style.hairlineWidth,
        ~borderStyle=#solid,
        ~borderColor=color,
        ~borderRadius=5.,
        (),
      )
    | None => style()
    },
    switch hoveredColor {
    | Some(color) => style(~borderColor=color, ())
    | None => style(~borderColor=Colors.neutralColor20, ())
    },
  ]->arrayStyle

type iconName = Icon.t

@react.component
let make = React.forwardRef((
  ~name: iconName,
  ~size=?,
  ~marginSize=?,
  ~color=Colors.neutralColor50,
  ~hoveredColor=?,
  ~borderedColor=?,
  ~bold=false,
  ~disabled=false,
  ~ariaProps=?,
  ~onPress,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  let fill = switch (hovered, hoveredColor) {
  | (true, Some(hoveredColor)) if !disabled && borderedColor->Option.isNone => hoveredColor
  | _ => color
  }
  let hoveredColor = hovered ? hoveredColor : None
  let ariaProps = ReactAria.mergeProps2(
    {ReactAria.Button.\"aria-label": (name :> string)},
    ariaProps,
  )

  <Touchable ref ariaProps disabled excludeFromTabOrder=true onPress>
    <View
      style={[
        styles["view"],
        viewStyleFromParams(~marginSize, ~hoveredColor, ~borderedColor),
      ]->arrayStyle}>
      <Icon name ?size fill stroke=?{bold ? Some(fill) : None} />
    </View>
  </Touchable>
})

let make = React.memo(make)

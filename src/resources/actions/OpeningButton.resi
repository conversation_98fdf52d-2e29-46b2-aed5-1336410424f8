// NOTE - explicit makeProps because of inbox `ref` props
let makeProps: (
  ~children: React.element,
  ~ariaProps: ReactAria.Button.props=?,
  ~opened: bool,
  ~disabled: bool=?,
  ~onPress: ReactAria.Button.pressEvent => unit,
  ~key: string=?,
  ~ref: 'ref=?,
  unit,
) => {
  "children": React.element,
  "ariaProps": option<ReactAria.Button.props>,
  "opened": bool,
  "disabled": option<bool>,
  "onPress": ReactAria.Button.pressEvent => unit,
}

@genType
let make: {
  "children": React.element,
  "ariaProps": option<ReactAria.Button.props>,
  "opened": bool,
  "disabled": option<bool>,
  "onPress": ReactAria.Button.pressEvent => unit,
} => React.element

type iconName = Icon.t

let makeProps: (
  ~name: iconName,
  ~size: float=?,
  ~marginSize: Sizes.t=?,
  ~color: Style.Color.t=?,
  ~hoveredColor: Style.Color.t=?,
  ~borderedColor: Style.Color.t=?,
  ~bold: bool=?,
  ~disabled: bool=?,
  ~ariaProps: ReactAria.Button.props=?,
  ~onPress: ReactAria.Button.pressEvent => unit,
  ~key: string=?,
  ~ref: 'ref=?,
  unit,
) => {
  "name": iconName,
  "size": option<float>,
  "marginSize": option<Sizes.t>,
  "bold": option<bool>,
  "color": option<Style.Color.t>,
  "borderedColor": option<Style.Color.t>,
  "hoveredColor": option<Style.Color.t>,
  "disabled": option<bool>,
  "ariaProps": option<ReactAria.Button.props>,
  "onPress": ReactAria.Button.pressEvent => unit,
}

@genType
let make: {
  "name": iconName,
  "size": option<float>,
  "marginSize": option<Sizes.t>,
  "bold": option<bool>,
  "color": option<Style.Color.t>,
  "borderedColor": option<Style.Color.t>,
  "hoveredColor": option<Style.Color.t>,
  "disabled": option<bool>,
  "ariaProps": option<ReactAria.Button.props>,
  "onPress": ReactAria.Button.pressEvent => unit,
} => React.element

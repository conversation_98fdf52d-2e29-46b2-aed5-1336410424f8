open Style

let styles = StyleSheet.create({
  "container": style(
    ~alignSelf=#flexStart,
    ~minHeight=40.->dp,
    ~paddingHorizontal=Spaces.normal->dp,
    ~paddingVertical=Spaces.small->dp,
    ~justifyContent=#center,
    ~borderRadius=5.,
    ~borderWidth=1.,
    ~borderColor=Colors.transparent,
    (),
  ),
  "containerInteracted": style(~backgroundColor=Colors.neutralColor05, ()),
  "containerHovered": style(
    ~borderColor=Colors.neutralColor20,
    ~backgroundColor=Colors.neutralColor15,
    ~zIndex=10,
    (),
  ),
  "containerFocused": style(
    ~borderColor=Colors.neutralColor25,
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  "containerErrored": style(~borderColor=Colors.dangerColor50, ~borderWidth=1., ()),
})

let containerStyleFromParams = (~disabled, ~hovered, ~focused, ~errored) =>
  [
    Some(styles["container"]),
    switch (disabled, hovered, focused) {
    | (false, true, true) => Some(styles["containerInteracted"])
    | _ => None
    },
    switch (disabled, hovered, focused, errored) {
    | (false, _, _, true) => Some(styles["containerErrored"])
    | (false, _, true, _) => Some(styles["containerFocused"])
    | (false, true, _, _) => Some(styles["containerHovered"])
    | _ => None
    },
  ]->arrayOptionStyle

type variation = [#normal | #neutral | #subdued | #important]

module OptionalTouchable = {
  @react.component
  let make = React.forwardRef((~children, ~disabled, ~onPress=?, ref) =>
    switch onPress {
    | Some(onPress) => <Touchable ref disabled onPress={_ => onPress()}> children </Touchable>
    | None => children
    }
  )
}

@react.component
let make = React.forwardRef((
  ~children,
  ~variation: variation=#normal,
  ~hovered,
  ~compact=false,
  ~focused=false,
  ~errored=false,
  ~disabled=false,
  ~onPress=?,
  ref,
) =>
  <OptionalTouchable disabled ?onPress>
    <View ref style={containerStyleFromParams(~disabled, ~hovered, ~focused, ~errored)}>
      <Text
        style={compact
          ? unsafeCss({
              "textOverflow": "ellipsis",
              "whiteSpace": "nowrap",
              "overflow": "hidden",
              "textAlign": "left",
              "direction": "rtl",
            })
          : style()}>
        <TextStyle
          variation={switch variation {
          | #normal | #important => #neutral
          | #neutral => #normal
          | #subdued => #subdued
          }}
          weight={switch variation {
          | #normal | #neutral | #subdued => #regular
          | #important => #medium
          }}
          size={compact ? #xsmall : #normal}>
          children
        </TextStyle>
      </Text>
    </View>
  </OptionalTouchable>
)

let make = React.memo(make)

open Style

let styles = StyleSheet.create({
  "view": merge([
    style(
      ~borderRadius=50.,
      ~justifyContent=#center,
      ~alignItems=#center,
      ~borderWidth=1.,
      ~borderColor=Colors.neutralColor20,
      (),
    ),
    unsafeCss({
      "backgroundImage": "linear-gradient(0deg, rgba(243,243,247,1) 0%, rgba(243,243,247,1) 50%, rgba(255,255,255,1) 100%)",
    }),
  ]),
  "smallView": style(~width=35.->dp, ~height=35.->dp, ()),
  "normalView": style(~width=40.->dp, ~height=40.->dp, ()),
  "mediumView": style(~width=56.->dp, ~height=56.->dp, ()),
  "viewDisabled": style(~borderColor=Colors.neutralColor15, ()),
  "viewFocused": style(~borderColor=Colors.neutralColor30, ()),
  "viewHovered": style(~borderColor=Colors.neutralColor25, ()),
})

let viewStyleFromParams = (~size, ~disabled, ~focused, ~hovered) =>
  [
    switch size {
    | #small => styles["smallView"]
    | #medium => styles["mediumView"]
    | #normal => styles["normalView"]
    | _ => styles["smallView"]
    },
    switch (disabled, focused, hovered) {
    | (true, _, _) => styles["viewDisabled"]
    | (_, true, _) => styles["viewFocused"]
    | (_, _, true) => styles["viewHovered"]
    | _ => style()
    },
  ]->arrayStyle

@react.component
let make = React.forwardRef((
  ~ariaProps=?,
  ~icon,
  ~size=#normal,
  ~disabled=false,
  ~focused=false,
  ~onPress,
  ref,
) => {
  let (hoveredRef, hovered) = Hover.use()

  let iconColor = switch (disabled, hovered) {
  | (true, _) => Colors.neutralColor30
  | (_, true) => Colors.brandColor50
  | _ => Colors.neutralColor70
  }

  <Touchable ref ?ariaProps disabled onPress>
    <View
      ref=hoveredRef
      style={[
        styles["view"],
        viewStyleFromParams(~size, ~disabled, ~focused, ~hovered),
      ]->arrayStyle}>
      <Icon name=icon fill=iconColor />
    </View>
  </Touchable>
})

let make = React.memo(make)

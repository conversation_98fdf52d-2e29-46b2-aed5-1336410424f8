open Style

let styles = StyleSheet.create({
  "view": style(
    ~flexDirection=#row,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~borderRadius=5.,
    ~paddingHorizontal=24.->dp,
    (),
  ),
  "xxsmallView": style(~paddingHorizontal=10.->dp, ~height=32.->dp, ()),
  "xsmallView": style(~paddingHorizontal=20.->dp, ~height=36.->dp, ()),
  "smallView": style(~paddingHorizontal=20.->dp, ~height=40.->dp, ()),
  "mediumView": style(~height=42.->dp, ()),
  "largeView": style(~height=50.->dp, ()),
  "xlargeView": style(~height=60.->dp, ()),
  "fillView": style(~borderWidth=0., ()),
  "strokeView": style(~borderWidth=Style.hairlineWidth, ~borderStyle=#solid, ()),
  "primaryLightenView": style(~backgroundColor=Colors.brandColor30, ()),
  "primaryView": style(~backgroundColor=Colors.brandColor50, ()),
  "primaryDarkenView": style(~backgroundColor=Colors.brandColor55, ()),
  "primaryDarkerView": style(~backgroundColor=Colors.brandColor60, ()),
  "successView": style(~backgroundColor=Colors.successColor50, ()),
  "successLightenView": style(~backgroundColor=Colors.successColor20, ()),
  "successDarkenView": style(~backgroundColor=Colors.successColor60, ()),
  "dangerView": style(~backgroundColor=Colors.dangerColor50, ()),
  "dangerDarkenView": style(~backgroundColor=Colors.dangerColor60, ()),
  "neutralLightenView": style(
    ~backgroundColor=Colors.neutralColor00,
    ~borderColor=Colors.neutralColor15,
    (),
  ),
  "neutralDefaultView": style(
    ~backgroundColor=Colors.neutralColor00,
    ~borderColor=Colors.neutralColor20,
    (),
  ),
  "neutralDarkenView": style(
    ~backgroundColor=Colors.neutralColor05,
    ~borderColor=Colors.neutralColor25,
    (),
  ),
  "neutralDarkerView": style(
    ~backgroundColor=Colors.neutralColor05,
    ~borderColor=Colors.neutralColor30,
    (),
  ),
  "secondaryView": style(~backgroundColor=Colors.secondaryColor50, ()),
  "text": merge([FontFaces.archivoSemiBoldStyle]),
  "iconView": style(~marginLeft=8.->dp, ~marginRight=-5.->dp, ~marginTop=1.->dp, ()),
  "lightText": style(~color=Colors.neutralColor00, ()),
  "grayText": style(~color=Colors.neutralColor35, ()),
  "darkText": style(~color=Colors.neutralColor80, ()),
  "xsmallButtonText": merge([FontFaces.archivoBoldStyle, style(~fontSize=12., ())]),
  "smallButtonText": merge([FontFaces.archivoBoldStyle, style(~fontSize=14., ())]),
  "largeButtonText": style(~fontSize=16., ()),
  "xlargeButtonText": style(~fontSize=18., ()),
})

let viewStyleFromParams = (~size, ~variation, ~disabled, ~hovered, ~focused) =>
  [
    styles["view"],
    switch size {
    | #tiny | #xxsmall => styles["xxsmallView"]
    | #xsmall => styles["xsmallView"]
    | #small => styles["smallView"]
    | #normal | #medium => styles["mediumView"]
    | #large => styles["largeView"]
    | #xlarge => styles["xlargeView"]
    },
    switch variation {
    | #neutral => styles["strokeView"]
    | _ => styles["fillView"]
    },
    // TODO — There are branches that are not handled
    switch (variation, disabled, hovered, focused) {
    | (#primary, true, false, _)
    | (#primary, true, true, _) =>
      styles["primaryLightenView"]
    | (#primary, _, _, true) => styles["primaryDarkerView"]
    | (#primary, false, true, _) => styles["primaryDarkenView"]
    | (#primary, _, _, _) => styles["primaryView"]
    | (#success, true, _, _) => styles["successLightenView"]
    | (#success, _, true, _) => styles["successDarkenView"]
    | (#success, _, _, _) => styles["successView"]
    | (#danger, _, false, _) => styles["dangerView"]
    | (#danger, _, true, _) => styles["dangerDarkenView"]
    | (#neutral, true, _, _) => styles["neutralLightenView"]
    | (#neutral, _, _, true) => styles["neutralDarkerView"]
    | (#neutral, _, true, _) => styles["neutralDarkenView"]
    | (#neutral, _, _, _) => styles["neutralDefaultView"]
    | (#secondary, _, _, _) => styles["secondaryView"]
    | _ => style()
    },
  ]->arrayStyle

let textStyleFromParams = (~size, ~variation, ~disabled, ~hovered) =>
  [
    styles["text"],
    switch size {
    | #tiny | #xxsmall => styles["xsmallButtonText"]
    | #xsmall | #small => styles["smallButtonText"]
    | #normal | #medium | #large => styles["largeButtonText"]
    | #xlarge => styles["xlargeButtonText"]
    },
    switch (variation, disabled, hovered) {
    | (#neutral, true, _) => styles["grayText"]
    | (#neutral, _, _)
    | (#warning, _, _) =>
      styles["darkText"]
    | (#success, _, _)
    | (#danger, _, _)
    | (#secondary, _, _)
    | (#primary, _, _) =>
      styles["lightText"]
    | _ => style()
    },
  ]->arrayStyle

type variation = [
  | #success
  | #danger
  | #warning
  | #primary
  | #neutral
  | #secondary
]

module OptionalTouchable = {
  @react.component
  let make = React.forwardRef((~children, ~disabled, ~ariaProps=?, ~onPress=?, ref) =>
    switch onPress {
    | Some(onPress) => <Touchable disabled ?ariaProps onPress ref> children </Touchable>
    | None => children
    }
  )
}

module BadgeBeta = {
  @react.component
  let make = (~variation) => {
    let text = Intl.t("BETA")
    let size = #small
    switch variation {
    | #primary =>
      <Badge
        size
        borderColor=Colors.brandColor50
        backgroundColor=Colors.brandColor10
        foregroundColor=Colors.brandColor60>
        {text->React.string}
      </Badge>
    | _ =>
      <Badge
        size
        variation={switch variation {
        | #primary => #normal
        | _ => #information
        }}>
        {text->React.string}
      </Badge>
    }
  }
}

@react.component
let make = React.forwardRef((
  ~children,
  ~size=#medium,
  ~variation=#primary,
  ~betaBadge=false,
  ~loading=false,
  ~disabled=false,
  ~focused=false,
  ~icon=?,
  ~ariaProps=?,
  ~onPress=?,
  ref,
) => {
  let (hoveredRef, hovered) = Hover.use()
  let (ref, {Dimensions.width: buttonWidth}) = Dimensions.use(~ref=?ref->Js.Nullable.toOption, ())

  // Keeps content width when getting replaced by spinner (loading: true)
  let loadingStateStyle = switch (loading, buttonWidth) {
  | (false, _) | (_, 0.) => None
  | _ => Some(style(~width=buttonWidth->dp, ()))
  }

  let hovered = hovered && !loading
  let disabledTouch = disabled || loading

  <OptionalTouchable ref ?ariaProps disabled=disabledTouch ?onPress>
    <View ref=hoveredRef style=?loadingStateStyle>
      <View style={viewStyleFromParams(~size, ~variation, ~disabled, ~hovered, ~focused)}>
        {if loading {
          <Spinner
            size={switch size {
            | #tiny | #xxsmall | #xsmall => 20.
            | #small => 22.
            | #normal | #medium => 24.
            | #large => 26.
            | #xlarge => 28.
            }}
            variation={switch variation {
            | #primary => #primary
            | #neutral => #neutral
            | #success => #success
            | _ => #neutral
            }}
          />
        } else {
          <Text style={textStyleFromParams(~size, ~variation, ~disabled, ~hovered)}>
            children
          </Text>
        }}
        {switch (icon, loading) {
        | (Some(name), false) =>
          let iconColor = switch (variation, disabled, hovered) {
          | (#neutral, true, _) => Colors.neutralColor50
          | (#neutral, false, _) => Colors.neutralColor90
          | (#primary, _, _) => Colors.neutralColor00
          | (_, _, true) => Colors.neutralColor50
          | _ => Colors.neutralColor00
          }

          <View style={styles["iconView"]}>
            <Icon name fill=iconColor />
          </View>
        | _ => React.null
        }}
        {if betaBadge {
          <Box spaceLeft=#small>
            <BadgeBeta variation />
          </Box>
        } else {
          React.null
        }}
      </View>
    </View>
  </OptionalTouchable>
})

let make = React.memo(make)

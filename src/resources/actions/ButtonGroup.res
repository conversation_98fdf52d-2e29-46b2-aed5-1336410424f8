open Style

let styles = StyleSheet.create({
  "item": style(~width=100.->pct, ~marginLeft=Spaces.small->dp, ()),
})

@react.component
let make = (~children) =>
  <Inline space=#xsmall align=#center>
    {children
    ->React.Children.toArray
    ->Array.mapWithIndex((index, child) =>
      <View key={index->Int.toString} style={styles["item"]}> child </View>
    )
    ->React.array}
  </Inline>

let make = React.memo(make)

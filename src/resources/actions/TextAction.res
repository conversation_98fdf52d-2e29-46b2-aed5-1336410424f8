open Style

let styles = StyleSheet.create({
  "main": merge([
    FontFaces.libreFranklinSemiBoldStyle,
    style(~color=Colors.neutralColor90, ~letterSpacing=0.125, ()),
  ]),
  "highlighted": style(~color=Colors.brandColor50, ()),
  "hovered": style(~color=Colors.brandColor50, ~textDecorationLine=#underline, ()),
})

let styleFromParams = (~highlighted, ~hovered, ~size) =>
  [
    styles["main"],
    switch (highlighted, hovered) {
    | (true, false) => styles["highlighted"]
    | (_, true) => styles["hovered"]
    | (false, false) => style()
    },
    switch size {
    | #normal => style(~fontSize=FontSizes.normal, ())
    | #compact => style(~fontSize=FontSizes.xsmall, ())
    },
  ]->arrayStyle

type size = [#normal | #compact]

@react.component
let make = (~text, ~size=#normal, ~highlighted=false, ~onPress) => {
  let (ref, hovered) = Hover.use()

  <Touchable wrap=false onPress={_ => onPress()}>
    <Text ref style={styleFromParams(~size, ~highlighted, ~hovered)}> {text->React.string} </Text>
  </Touchable>
}

open Intl
open Style

let styles = StyleSheet.create({
  "standardView": merge([
    FontFaces.archivoBoldStyle,
    style(~color=Colors.neutralColor90, ~fontSize=26., ()),
  ]),
  "compactView": merge([
    FontFaces.archivoRegularStyle,
    style(~color=Colors.neutralColor90, ~fontSize=16., ~fontWeight=#_600, ()),
  ]),
})

type variant = [
  | #standard
  | #compact
]

type value = [
  | #currency(float, Intl.currency)
  | #percent(float)
  | #decimal(float)
  | #integer(int)
]

let currencyValue = (value, ~currency) => #currency(value, currency)
let percentValue = value => #percent(value)
let decimalValue = value => #decimal(value)
let integerValue = value => #integer(value)

let styleFromParams = (~variant) =>
  [
    switch variant {
    | #standard => styles["standardView"]
    | #compact => styles["compactView"]
    },
  ]->arrayStyle

let formatValue = value =>
  switch value {
  | #currency(value, currency) =>
    value->currencyFormat(~currency, ~minimumFractionDigits=2, ~maximumFractionDigits=2)
  | #percent(value) => value->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)
  | #decimal(value) => value->decimalFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)
  | #integer(value) =>
    value->Float.fromInt->decimalFormat(~minimumFractionDigits=0, ~maximumFractionDigits=0)
  }

@react.component
let make = (~value, ~variant=#standard) => {
  let formattedValue = formatValue(value)
  <Text style={styleFromParams(~variant)}> {formattedValue->React.string} </Text>
}

let make = React.memo(make)

React.setDisplayName(make, "ValueIndicator")

// TODO - once the specs of this component will be all defined and the subcomponents specs mature enough
// with responsive handled, this component will eventually merge with <AnalyticsCashFlowPage.OperationMetrics>.

module BadgeProgressionText = {
  let style = ReactDOM.Style.make(~lineHeight="12px", ())
  let infinityStyle = ReactDOM.Style.make(~fontSize="1.4em", ())

  @react.component
  let make = (~value) =>
    <span style ariaLabel="progression">
      {if !(value->Js.Float.isFinite) {
        <>
          {"+ "->React.string}
          <span style={infinityStyle}> {"∞"->React.string} </span>
        </>
      } else if value > 0. {
        <> {("+ " ++ value->Intl.percentFormat(~maximumFractionDigits=0))->React.string} </>
      } else {
        <>
          {value
          ->Intl.percentFormat(~maximumFractionDigits=0)
          ->Js.String2.replace("-", "- ")
          ->React.string}
        </>
      }}
    </span>
}

let getVariation = (~value) => {
  let roundedValue = (value *. 100.)->Js.Math.round
  if roundedValue >= 1. {
    #success
  } else if roundedValue <= -1. {
    #danger
  } else {
    #neutral
  }
}

let style = (~muted) =>
  ReactDOMStyle.make(
    ~display="flex",
    ~position="relative",
    ~height="100%",
    ~minWidth="180px",
    ~flexDirection="column",
    ~justifyContent="space-between",
    ~padding=`${Spaces.medium->Float.toString}px ${Spaces.large->Float.toString}px`,
    ~boxSizing="border-box",
    ~backgroundColor=Colors.neutralColor00,
    ~borderRadius="5px",
    ~boxShadow=!muted
      ? `rgba(37, 36, 58, 0.08) 0 0 10px,
             rgba(37, 36, 58, 0.01) 4px 0 4px,
             rgba(37, 36, 58, 0.01) -4px 0 4px`
      : `rgba(37, 36, 58, 0.01) 0 6px 10px`,
    ~transition="box-shadow .3s ease-in-out",
    (),
  )
let titleBoxStyle = ReactDOM.Style.make(~marginRight="12px", ~paddingBottom="8px", ())
let titleBoxBadgeStyle = ReactDOM.Style.make(~position="relative", ~top="-2px", ())
let titleBoxTooltipStyle = ReactDOM.Style.make(
  ~display="inline-flex",
  ~position="absolute",
  ~top="4px",
  ~marginLeft="3px",
  ~transform="scale(0.95)",
  (),
)
let spinnerStyle = ReactDOM.Style.make(~position="absolute", ~top="8px", ~right="8px", ())

@react.component
let make = (~children, ~title, ~loading=false, ~progression=?, ~tooltip=?) => {
  let ref = React.createRef()
  let domRef = ref->ReactDOM.Ref.domRef

  <div style={style(~muted=loading)}>
    <div ref=domRef style=titleBoxStyle>
      <InlineText linespace=#xxsmall>
        <Title level=#4 weight=#strong> {(title ++ "   ")->React.string} </Title>
        {switch progression {
        | Some(value) if !(value->Js.Float.isNaN) =>
          <span style=titleBoxBadgeStyle>
            <Badge variation={getVariation(~value)} size=#small>
              <BadgeProgressionText value />
            </Badge>
            {switch tooltip {
            | Some(toolTipContent) =>
              <div style=titleBoxTooltipStyle>
                <TooltipIcon variation=#info altTriggerRef=ref> toolTipContent </TooltipIcon>
              </div>
            | _ => React.null
            }}
          </span>
        | _ => React.null
        }}
        {if loading {
          <div style=spinnerStyle>
            <Spinner size=18. />
          </div>
        } else {
          React.null
        }}
      </InlineText>
    </div>
    // TODO - should not be as children
    children
  </div>
}

let make = React.memo(make)

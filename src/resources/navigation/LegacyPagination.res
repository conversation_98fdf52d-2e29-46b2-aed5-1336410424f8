open Intl
open Style

type action =
  | First
  | Prev
  | Next
  | Last

let isButtonDisabled = (~action, ~currentPage, ~totalPages) =>
  switch action {
  | First | Prev => currentPage <= 1
  | Next | Last => currentPage >= totalPages
  }

module ControlButton = {
  let styles = StyleSheet.create({
    "view": style(~margin=-7.->dp, ()),
  })

  @react.component
  let make = (~currentPage, ~totalPages, ~action, ~onPress) => {
    let disabled = isButtonDisabled(~action, ~currentPage, ~totalPages)

    <View style={styles["view"]}>
      <IconButton
        name={switch action {
        | First => #double_arrow_left_light
        | Prev => #arrow_left_light
        | Next => #arrow_right_light
        | Last => #double_arrow_right_light
        }}
        marginSize=#small
        disabled
        onPress={_ => onPress(action)}
        color={disabled ? Colors.neutralColor20 : Colors.neutralColor90}
      />
    </View>
  }

  let make = React.memo(make)
}

module PageButton = {
  let styles = StyleSheet.create({
    "view": style(
      ~justifyContent=#center,
      ~height=28.->dp,
      ~minWidth=28.->dp,
      ~paddingHorizontal=5.->dp,
      ~marginHorizontal=1.->dp,
      (),
    ),
    "disabledView": style(~backgroundColor=Colors.neutralColor10, ()),
    "text": merge([
      FontFaces.libreFranklinRegularStyle,
      style(~textAlign=#center, ~fontSize=FontSizes.xsmall, ~color=Colors.neutralColor30, ()),
    ]),
    "disabledText": merge([
      FontFaces.libreFranklinBoldStyle,
      style(~color=Colors.neutralColor90, ()),
    ]),
  })

  let viewStyleFromParams = (~disabled) =>
    [Some(styles["view"]), disabled ? Some(styles["disabledView"]) : None]->arrayOptionStyle
  let textStyleFromParams = (~disabled) =>
    [Some(styles["text"]), disabled ? Some(styles["disabledText"]) : None]->arrayOptionStyle

  @react.component
  let make = (~currentPage, ~totalPages, ~action=?, ~children, ~onPress) => {
    let disabled = switch action {
    | Some(action) => isButtonDisabled(~action, ~currentPage, ~totalPages)
    | None => true
    }

    let onPress = _ => {
      switch action {
      | Some(action) => onPress(action)
      | None => ()
      }
    }

    <Touchable disabled onPress={_ => onPress(action)}>
      <View style={viewStyleFromParams(~disabled)}>
        <Text style={textStyleFromParams(~disabled)}> {children} </Text>
      </View>
    </Touchable>
  }

  let make = React.memo(make)
}

let styles = StyleSheet.create({
  "container": style(
    ~height=64.->dp,
    ~flexDirection=#row,
    ~alignItems=#center,
    ~justifyContent=#spaceBetween,
    ~backgroundColor=Colors.neutralColor00,
    ~paddingHorizontal=Spaces.large->dp,
    ~borderStyle=#solid,
    ~borderTopWidth=1.,
    ~borderTopColor=Colors.neutralColor15,
    (),
  ),
  "label": merge([
    FontFaces.libreFranklinRegularStyle,
    style(~fontSize=FontSizes.small, ~color=Colors.neutralColor30, ()),
  ]),
})

@react.component
let make = (~currentPage, ~totalPages, ~onRequestPaginate as onPress) =>
  <View style={styles["container"]}>
    <Text style={styles["label"]}>
      {("Page " ++ (Int.toString(currentPage) ++ (t(" of ") ++ Int.toString(totalPages))))
        ->React.string}
    </Text>
    <Inline>
      <ControlButton action=First onPress currentPage totalPages />
      <ControlButton action=Prev onPress currentPage totalPages />
      <Box spaceX=#xsmall>
        <Inline>
          {if currentPage > 1 {
            <PageButton action={Prev} onPress currentPage totalPages>
              {(currentPage - 1)->React.int}
            </PageButton>
          } else {
            React.null
          }}
          <PageButton onPress currentPage totalPages> {currentPage->React.int} </PageButton>
          {if currentPage < totalPages {
            <PageButton action={Next} onPress currentPage totalPages>
              {(currentPage + 1)->React.int}
            </PageButton>
          } else {
            React.null
          }}
        </Inline>
      </Box>
      <ControlButton action=Next onPress currentPage totalPages />
      <ControlButton action=Last onPress currentPage totalPages />
    </Inline>
  </View>

let make = React.memo(make)

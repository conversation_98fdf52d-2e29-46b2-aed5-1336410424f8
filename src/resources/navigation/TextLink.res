open Style

let styles = StyleSheet.create({
  "main": merge([
    FontFaces.libreFranklinSemiBoldStyle,
    style(~fontSize=FontSizes.normal, ~color=Colors.neutralColor90, ~letterSpacing=0.125, ()),
  ]),
  "highlighted": style(~color=Colors.brandColor50, ()),
  "hovered": style(~color=Colors.brandColor50, ~textDecorationLine=#underline, ()),
})

let styleFromParams = (~highlighted, ~hovered) =>
  [
    styles["main"],
    switch (highlighted, hovered) {
    | (true, false) => styles["highlighted"]
    | (_, true) => styles["hovered"]
    | (false, false) => style()
    },
  ]->arrayStyle

let colorFromParams = (~highlighted, ~hovered) =>
  switch (highlighted, hovered) {
  | (true, false)
  | (_, true) => Colors.brandColor50
  | (false, false) => Colors.neutralColor90
  }

@react.component
let make = (~text, ~to, ~openNewTab=false, ~highlighted=false, ~icon=?, ~onPress=?) => {
  let (ref, hovered) = Hover.use()

  <Navigation.Link to openNewTab ?onPress>
    {switch icon {
    | Some(name) =>
      // NOTE - these wrappers cancel some specific wrapping inline behaviors props to inline-block
      <View ref>
        <Inline space=#small align=#spaceBetween>
          <Text ref style={styleFromParams(~highlighted, ~hovered)}> {text->React.string} </Text>
          <Icon name fill={colorFromParams(~highlighted, ~hovered)} size=18. />
        </Inline>
      </View>
    | None =>
      <Text ref style={styleFromParams(~highlighted, ~hovered)}> {text->React.string} </Text>
    }}
  </Navigation.Link>
}

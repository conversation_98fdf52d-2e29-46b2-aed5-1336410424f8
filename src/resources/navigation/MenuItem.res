open Style

let styles = StyleSheet.create({
  "wrapper": style(~flex=1., ()),
  "view": style(
    ~flexDirection=#row,
    ~justifyContent=#flexStart,
    ~alignItems=#center,
    ~backgroundColor=Colors.neutralColor00,
    (),
  ),
  "hoveredView": style(~backgroundColor=Colors.neutralColor05, ()),
  "iconView": style(~marginRight=10.->dp, ()),
  "xxsmallView": style(~paddingHorizontal=3.0->dp, ~height=28.0->dp, ()),
  "xsmallView": style(~paddingHorizontal=10.0->dp, ~height=30.0->dp, ()),
  "smallView": style(~paddingHorizontal=12.0->dp, ~height=36.0->dp, ()),
  "normalView": style(~paddingHorizontal=16.0->dp, ~height=40.0->dp, ()),
  "mediumView": style(~paddingHorizontal=30.0->dp, ~height=44.0->dp, ()),
  "largeView": style(~paddingHorizontal=36.0->dp, ~height=60.0->dp, ()),
  "xlargeView": style(~paddingHorizontal=38.0->dp, ~height=60.0->dp, ()),
  "text": merge([FontFaces.archivoRegularStyle, style(~fontWeight=#_500, ())]),
  "lightText": style(~color=Colors.neutralColor20, ()),
  "grayText": style(~color=Colors.neutralColor90, ()),
  "darkText": style(~color=Colors.neutralColor100, ()),
  "dangerText": style(~color=Colors.dangerColor50, ()),
})

let viewStyleFromParams = (~size, ~hovered) =>
  [
    switch size {
    | #xxsmall => styles["xxsmallView"]
    | #xsmall => styles["xsmallView"]
    | #small => styles["smallView"]
    | #large => styles["largeView"]
    | #normal
    | _ =>
      styles["normalView"]
    },
    hovered ? styles["hoveredView"] : style(),
  ]->arrayStyle

let textStyleFromParams = (~size, ~disabled, ~variation) =>
  [
    FontSizes.styleFromSize(size),
    switch (disabled, variation) {
    | (true, _) => styles["lightText"]
    | (false, #danger) => styles["dangerText"]
    | _ => styles["darkText"]
    },
  ]->arrayStyle

type action =
  | Callback(unit => unit)
  | OpenLink(Navigation.to)

type content = Text(string) | Component(React.element)
type textVariation = [#normal | #danger]

@react.component
let make = (
  ~content,
  ~textVariation=#normal,
  ~size=#normal,
  ~disabled=false,
  ~shouldCloseOnPress=true,
  ~action,
) => {
  let state = Popover.useState()
  let (ref, hovered) = Hover.use()

  let component =
    <View style={[styles["view"], viewStyleFromParams(~size, ~hovered)]->arrayStyle}>
      {switch content {
      | Text(text) =>
        <Text
          style={[
            styles["text"],
            textStyleFromParams(~size, ~disabled, ~variation=textVariation),
          ]->arrayStyle}>
          {text->React.string}
        </Text>
      | Component(component) => component
      }}
    </View>

  <View ref style={styles["wrapper"]}>
    {switch action {
    | Callback(action) =>
      let onPress = _ => {
        action()
        if shouldCloseOnPress {
          state.onRequestClose()
        }
      }

      <Touchable disabled onPress> component </Touchable>
    | OpenLink(to) =>
      let onPress = _ =>
        if shouldCloseOnPress {
          state.onRequestClose()
        }

      <Navigation.Link to disabled onPress> component </Navigation.Link>
    }}
  </View>
}

let make = React.memo(make)

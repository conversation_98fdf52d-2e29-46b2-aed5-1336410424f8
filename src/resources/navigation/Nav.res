open WebAPI
open Intl
open Style

module Context = {
  type action = Toggled

  type state = {opened: bool}

  let initialState = {opened: false}

  type dispatch = action => unit
  type context = (option<state>, dispatch)
  let context: React.Context.t<context> = React.createContext((None, _ => ignore()))
  let use = () => {
    let (state, dispatch) = React.useContext(context)
    switch state {
    | None => (initialState, _ => ())
    | Some(state) => (state, dispatch)
    }
  }

  module Provider = {
    let make = React.Context.provider(context)
    let makeProps = (~value, ~children, ()) =>
      {
        "value": value,
        "children": children,
      }
  }

  let getReducer = _ => React.useReducer((state, action) =>
      switch action {
      | Toggled => {opened: !state.opened}
      }
    , initialState)
}

module NavTextIcon = {
  let styles = StyleSheet.create({
    "view": style(
      ~flex=1.,
      ~flexDirection=#row,
      ~justifyContent=#flexStart,
      ~alignItems=#center,
      ~minHeight=36.->dp,
      (),
    ),
    "text": merge([style(~marginLeft=4.->dp, ()), unsafeCss({"whiteSpace": "nowrap"})]),
    "textActive": style(~color=Colors.neutralColor90, ()),
    "textHovered": style(~color=Colors.neutralColor70, ()),
    "textImportant": merge([
      FontFaces.archivoSemiBoldStyle,
      style(~color=Colors.neutralColor00, ~fontSize=FontSizes.large, ()),
    ]),
    "textPrimary": merge([
      FontFaces.archivoRegularStyle,
      style(~color=Colors.neutralColor50, ~fontSize=FontSizes.normal, ()),
    ]),
  })

  let textStyleFromParams = (~variation, ~active, ~hovered) =>
    [
      switch (variation, active, hovered) {
      | (#important, _, _) => styles["textImportant"]
      | (#primary, true, _) => [styles["textPrimary"], styles["textActive"]]->arrayStyle
      | (#primary, false, true) => [styles["textPrimary"], styles["textHovered"]]->arrayStyle
      | (_, _, _) => styles["textPrimary"]
      },
    ]->arrayStyle

  let iconColorFromParams = (~active, ~hovered, navOpened) =>
    switch (active, hovered, navOpened) {
    | (true, _, false)
    | (true, _, true)
    | (false, true, false) => Colors.neutralColor90
    | (false, true, true) => Colors.neutralColor70
    | _ => Colors.neutralColor35
    }

  type variation = [#important | #primary | #secondary]

  @react.component
  let make = (
    ~children,
    ~icon,
    ~active=false,
    ~variation: variation=#primary,
    ~hovered as hoveredParent=false,
  ) => {
    let (nav, _) = Context.use()
    let (ref, hovered) = Hover.use()

    <View ref style={styles["view"]}>
      <Box spaceX=#normal>
        <Icon
          name=icon
          fill={variation === #important
            ? Colors.neutralColor00
            : iconColorFromParams(~active, ~hovered=hovered || hoveredParent, nav.opened)}
        />
      </Box>
      {if nav.opened {
        <Text
          style={[
            styles["text"],
            textStyleFromParams(~variation, ~active, ~hovered=hovered || hoveredParent),
          ]->arrayStyle}>
          children
        </Text>
      } else {
        // NOTE - small hack until the Nav components suite get revamped (new UX, new architecture)
        let textContent = Obj.magic(children)->Js.String2.replace("&#x27;", "'")
        <Tooltip
          placement=#start
          offset={-4.}
          crossOffset={1.}
          delay=0
          content={<Tooltip.Span text=textContent />}
          opened=hovered
        />
      }}
    </View>
  }

  let make = React.memo(make)
}

module Section = {
  let styles = StyleSheet.create({
    "header": style(~justifyContent=#center, ~height=42.0->dp, ()),
    "label": merge([
      FontFaces.archivoRegularStyle,
      style(~marginLeft=57.75->dp, ~letterSpacing=1.0, ()),
    ]),
    "labelHovered": style(~color=Colors.neutralColor90, ()),
    "labelActive": merge([FontFaces.archivoSemiBoldStyle, style(~color=Colors.neutralColor00, ())]),
    "labelActiveHovered": style(~color=Colors.neutralColor00, ()),
    "content": style(~maxHeight=999.->dp, ~overflow=#hidden, ()),
    "contentClosed": style(~maxHeight=0.->dp, ()),
    "list": style(~paddingHorizontal=12.->dp, ~paddingBottom=4.->dp, ()),
  })

  let contentStyleFromParams = (~opened) =>
    [opened ? None : Some(styles["contentClosed"])]->arrayOptionStyle

  @react.component
  let make = (~children, ~title, ~active, ~icon) => {
    let nav = Context.use()->fst
    let url = Navigation.useUrl()

    let (ref, hovered) = Hover.use()
    let (opened, setOpened) = React.useState(() => false)
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

    React.useEffect1(() => {
      if !nav.opened {
        setOpened(_ => false)
      }
      None
    }, [nav.opened])

    React.useEffect1(() => {
      popover.onRequestClose()
      None
    }, [url.pathname])

    <>
      {if nav.opened {
        <Touchable onPress={_ => setOpened(opened => !opened)}>
          <View ref style={styles["header"]}>
            <Inline align=#spaceBetween>
              <NavTextIcon active icon hovered> {title->React.string} </NavTextIcon>
              <Icon
                name={opened ? #arrow_up_light : #arrow_down_light}
                fill={hovered || active ? Colors.neutralColor70 : Colors.neutralColor30}
              />
            </Inline>
          </View>
        </Touchable>
      } else {
        <Touchable ariaProps=popoverAriaProps.triggerProps onPress={_ => popover.onRequestToggle()}>
          <View ref=popoverTriggerRef style={styles["header"]}>
            <NavTextIcon hovered active icon> {title->React.string} </NavTextIcon>
          </View>
        </Touchable>
      }}
      <View style={[styles["content"], contentStyleFromParams(~opened)]->arrayStyle}>
        <View style={styles["list"]}> children </View>
      </View>
      {if popover.opened {
        <Popover
          triggerRef=popoverTriggerRef
          state=popover
          variation=#arrowed
          placement=#end
          offset=0.
          crossOffset=1.>
          <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
            <Box spaceX=#medium spaceY=#small> children </Box>
          </Popover.Dialog>
        </Popover>
      } else {
        React.null
      }}
    </>
  }

  let make = React.memo(make)
}

module Button = {
  let styles = StyleSheet.create({
    "view": style(
      ~marginVertical=10.->dp,
      ~paddingVertical=6.->dp,
      ~height=42.->dp,
      ~borderRadius=5.,
      (),
    ),
    "label": merge([FontFaces.archivoRegularStyle, style(~fontSize=FontSizes.normal, ())]),
    "labelNormal": style(~color=Colors.neutralColor50, ()),
    "labelNormalHovered": style(~color=Colors.neutralColor70, ()),
    "labelImportant": style(~color=Colors.dangerColor50, ()),
    "labelImportantHovered": style(~color=Colors.dangerColor60, ()),
  })

  let labelStyleFromParams = (variation, hovered) =>
    [
      styles["label"],
      switch (variation, hovered) {
      | (#important, false) => styles["labelImportant"]
      | (#important, true) => styles["labelImportantHovered"]
      | (#normal, false) => styles["labelNormal"]
      | (#normal, true) => styles["labelNormalHovered"]
      },
    ]->arrayStyle

  type variation = [#normal | #important]

  @react.component
  let make = (~label=?, ~onPress=?, ~icon=?, ~variation: variation=#normal) => {
    let (ref, hovered) = Hover.use()

    <View ref>
      {switch (label, onPress, icon) {
      | (Some(label), Some(onPress), None) =>
        <Touchable onPress={_ => onPress()}>
          <Box spaceY=#small>
            <Text style={labelStyleFromParams(variation, hovered)}> {label->React.string} </Text>
          </Box>
        </Touchable>
      | (Some(label), Some(onPress), Some(icon)) =>
        <Touchable onPress={_ => onPress()}>
          <NavTextIcon icon> {label->React.string} </NavTextIcon>
        </Touchable>
      | _ => React.null
      }}
    </View>
  }

  let make = React.memo(make)
}

module Footer = {
  @module("./Nav_default_profile.png") external defaultProfilePictureUri: string = "default"

  let containerSize = Spaces.xxlarge->dp

  let styles = StyleSheet.create({
    "view": style(
      ~height=60.->dp,
      ~paddingHorizontal=Spaces.medium->dp,
      ~paddingVertical=Spaces.xmedium->dp,
      ~backgroundColor=Colors.backgroundDefaultColortemplate,
      (),
    ),
    "moreView": merge([
      style(
        ~backgroundColor=Colors.backgroundDefaultColortemplate,
        ~borderTopWidth=1.,
        ~borderTopColor=Colors.neutralColor15,
        (),
      ),
    ]),
    "moreViewOpened": merge([
      style(~maxHeight=300.->dp, ()),
      unsafeCss({
        "transitionDuration": ".55s",
        "transitionProperty": "max-height",
      }),
    ]),
    "moreViewClosed": merge([
      style(~maxHeight=0.->dp, ()),
      unsafeCss({
        "transitionDuration": ".15s",
        "transitionProperty": "max-height",
      }),
    ]),
    "moreList": style(
      ~marginHorizontal=23.->dp,
      ~paddingVertical=Spaces.medium->dp,
      ~paddingHorizontal=Spaces.xxsmall->dp,
      ~borderBottomWidth=1.,
      ~borderBottomColor=Colors.neutralColor15,
      (),
    ),
    "container": style(~height=containerSize, ~justifyContent=#center, ()),
    "containerClosed": style(~width=containerSize, ()),
    "inline": style(~flexDirection=#row, ~alignItems=#center, ()),
    "profilePicture": style(
      ~width=containerSize,
      ~height=containerSize,
      ~borderWidth=1.,
      ~borderColor=Colors.transparent,
      ~borderRadius=100.,
      (),
    ),
    "username": merge([
      FontFaces.archivoRegularStyle,
      style(
        ~flex=1.,
        ~paddingHorizontal=Spaces.normal->dp,
        ~fontSize=FontSizes.normal,
        ~color=Colors.neutralColor50,
        (),
      ),
      unsafeCss({
        "overflow": "hidden",
        "width": "100%",
        "whiteSpace": "nowrap",
        "textOverflow": "ellipsis",
      }),
    ]),
  })

  let profilePictureFromParams = (~hovered, ~active) =>
    arrayOptionStyle([
      Some(styles["profilePicture"]),
      hovered ? Some(style(~borderColor=Colors.neutralColor25, ())) : None,
      active ? Some(style(~borderColor=Colors.neutralColor30, ())) : None,
    ])

  let containerStyleFromParams = opened =>
    arrayOptionStyle([opened ? None : Some(styles["containerClosed"])])

  let moreViewStyleFromParams = opened =>
    arrayOptionStyle([opened ? Some(styles["moreViewOpened"]) : Some(styles["moreViewClosed"])])

  @react.component
  let make = (~children, ~userName, ~userProfilePictureUri=?) => {
    let (nav, _) = Context.use()
    let url = Navigation.useUrl()
    let (refProfilePicture, hoveredProfilePicture) = Hover.use()
    let (moreOpened, setMoreOpened) = React.useState(() => false)
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

    React.useEffect1(() => {
      popover.onRequestClose()
      None
    }, [url.pathname])

    React.useEffect1(() => {
      if !nav.opened {
        setMoreOpened(_ => false)
      }
      None
    }, [nav.opened])

    let handleFooterPress = _ =>
      if nav.opened {
        setMoreOpened(moreOpened => !moreOpened)
      } else {
        popover.onRequestToggle()
      }

    <>
      <View style={[styles["moreView"], moreViewStyleFromParams(moreOpened)]->arrayStyle}>
        <View style={styles["moreList"]}> children </View>
      </View>
      <Touchable ref=refProfilePicture onPress=handleFooterPress>
        <View style={styles["view"]}>
          <View style={[styles["container"], containerStyleFromParams(nav.opened)]->arrayStyle}>
            <View ref=popoverTriggerRef style={styles["inline"]}>
              <Image
                style={profilePictureFromParams(
                  ~hovered=hoveredProfilePicture && !nav.opened,
                  ~active=popover.opened,
                )}
                source={Image.fromUriSource(
                  Image.uriSource(
                    switch userProfilePictureUri {
                    | Some(profilePictureUri) => profilePictureUri
                    | None => defaultProfilePictureUri
                    },
                  ),
                )}
              />
              {if nav.opened {
                <>
                  <Text style={styles["username"]}> {userName->React.string} </Text>
                  <IconButton
                    name={moreOpened ? #arrow_down_light : #arrow_up_light}
                    marginSize=#xsmall
                    color={hoveredProfilePicture ? Colors.neutralColor70 : Colors.neutralColor30}
                    hoveredColor=Colors.neutralColor70
                    onPress=handleFooterPress
                  />
                </>
              } else if popover.opened {
                <Popover
                  triggerRef=popoverTriggerRef
                  state=popover
                  variation=#arrowed
                  placement=#end
                  offset=8.>
                  <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
                    <Box spaceX=#medium spaceY=#small> children </Box>
                  </Popover.Dialog>
                </Popover>
              } else {
                React.null
              }}
            </View>
          </View>
        </View>
      </Touchable>
    </>
  }

  let make = React.memo(make)
}

module Link = {
  let restrictedAccessIcon =
    <Tooltip content={<Tooltip.Span text={t("Restricted access")} />}>
      <Offset top={-1.} left={-2.} height=16. width=16.>
        <svg width="16px" height="16px" viewBox="0 0 20 20">
          <path
            fill=Colors.neutralColor90
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10 2C12.2091 2 14 3.79086 14 6V8H15C16.1046 8 17 8.89543 17 10V16C17 17.1046 16.1046 18 15 18H5C3.89543 18 3 17.1046 3 16V10C3 8.89543 3.89543 8 5 8H6V6C6 3.79086 7.79086 2 10 2ZM5 16H15V10H5V16ZM10 4C8.89543 4 8 4.89543 8 6V8H12V6C12 4.89543 11.1046 4 10 4Z"
          />
        </svg>
      </Offset>
    </Tooltip>

  let styles = StyleSheet.create({
    "view": style(~height=42.->dp, ~borderRadius=5., ()),
    "viewImportant": style(~backgroundColor=Colors.secondaryColor50, ()),
    "viewImportantHovered": style(~backgroundColor=Colors.secondaryColor65, ()),
    "viewPrimary": style(~backgroundColor=Colors.transparent, ()),
    "label": merge([
      FontFaces.archivoRegularStyle,
      style(~paddingVertical=8.->dp, ~color=Colors.neutralColor50, ~fontSize=FontSizes.normal, ()),
    ]),
    "labelActiveHovered": style(~color=Colors.neutralColor90, ()),
  })

  let viewStyleFromParams = (~variation, ~hovered, navOpened) =>
    [
      styles["view"],
      switch (variation, hovered, navOpened) {
      | (#important, false, true)
      | (#important, false, false) =>
        styles["viewImportant"]
      | (#important, true, _) => styles["viewImportantHovered"]
      | (#secondary, _, _)
      | (#primary, _, _) =>
        styles["viewPrimary"]
      },
    ]->arrayStyle

  let labelStyleFromParams = (~active, ~hovered) =>
    [
      Some(styles["label"]),
      if (!active && hovered) || active && !hovered || (active && hovered) {
        Some(styles["labelActiveHovered"])
      } else {
        None
      },
    ]->arrayOptionStyle

  @react.component
  let make = (
    ~to,
    ~label,
    ~active,
    ~icon=#ticket_bold,
    ~betaBadge=false,
    ~restrictedMode=false,
    ~variation=#secondary,
  ) => {
    let (ref, hovered) = Hover.use()
    let (nav, _) = Context.use()

    <Navigation.Link to>
      <View ref>
        <Inline space=#medium align=#spaceBetween>
          {switch variation {
          | #secondary =>
            <Inline space=#small>
              <Text style={labelStyleFromParams(~active, ~hovered)}> {label->React.string} </Text>
              {if betaBadge {
                <Badge variation=#information size=#small> {t("BETA")->React.string} </Badge>
              } else {
                React.null
              }}
              {if restrictedMode {
                restrictedAccessIcon
              } else {
                React.null
              }}
            </Inline>
          | #primary | #important =>
            <View style={viewStyleFromParams(~variation, ~hovered, nav.opened)}>
              <NavTextIcon variation icon> {label->React.string} </NavTextIcon>
            </View>
          }}
        </Inline>
      </View>
    </Navigation.Link>
  }
}

let openedSize = 262.0
let closedSize = 64.0

let styles = StyleSheet.create({
  "root": merge([
    style(
      ~zIndex=1,
      ~top=0.->dp,
      ~left=0.->dp,
      ~bottom=0.->dp,
      ~overflow=#hidden,
      ~backgroundColor=Colors.neutralColor00,
      ~shadowColor=Colors.neutralColor50,
      ~shadowOffset=shadowOffset(~width=3., ~height=0.),
      ~shadowOpacity=0.1,
      ~shadowRadius=10.,
      ~width=64.->dp,
      (),
    ),
    unsafeCss({
      "transitionDuration": ".25s",
      "transitionProperty": "width",
      "position": "fixed",
    }),
  ]),
  "rootOpened": style(~width=openedSize->dp, ()),
  "container": style(~flex=1., ~width=openedSize->dp, ()),
  "header": style(
    ~flexDirection=#row,
    ~justifyContent=#spaceBetween,
    ~alignItems=#center,
    ~borderBottomColor=Colors.neutralColor15,
    ~borderBottomWidth=Style.hairlineWidth,
    ~height=60.->dp,
    (),
  ),
  "burger": style(~marginLeft=10.->dp, ()),
  "userOrganizationName": merge([
    FontFaces.archivoBoldStyle,
    style(~fontSize=15., ~color=Colors.brandColor50, ()),
  ]),
  "userOrganizationNameAndBadge": style(~alignItems=#flexEnd, ~paddingRight=12.->dp, ()),
  "content": merge([
    style(
      ~flex=1.,
      ~paddingLeft=8.->dp,
      ~paddingRight=17.->dp,
      ~paddingTop=16.->dp,
      ~width=closedSize->dp,
      (),
    ),
    unsafeCss({
      "overflowY": "auto",
      "overflowX": "hidden",
      "scrollbarWidth": "thin",
      "transitionDuration": ".25s",
      "transitionProperty": "width",
    }),
  ]),
  "contentOpened": style(~width=100.->pct, ()),
})

let rootStyleFromParams = (~opened) =>
  arrayStyle([styles["root"], opened ? styles["rootOpened"] : style()])

let contentStyleFromParams = opened =>
  arrayStyle([styles["content"], opened ? styles["contentOpened"] : style()])

let contentScrollShadeOverlay = ReactDOM.Style.make(
  ~height="16px",
  ~marginTop="-16px",
  ~borderBottom="1px solid " ++ Colors.neutralColor10,
  ~boxSizing="border-box",
  ~backgroundImage="radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 75%)",
  ~backgroundSize="100% 200%",
  ~backgroundPosition="center top",
  ~pointerEvents="none",
  (),
)

@react.component
let make = (
  ~children,
  ~userOrganizationName,
  ~userName,
  ~userProfilePictureUri=?,
  ~badge=?,
  ~userImpersonating,
  ~userSettingsRoute,
  ~helpCenterUrl,
  ~legacyDashboardUrl,
  ~legacyDashboardText,
  ~onToggleHelpCenter,
  ~onRequestLogout,
) => {
  let (nav, dispatch) = Context.use()
  let {pathname: activeRoute} = Navigation.useUrl()

  let (scrollable, setScrollable) = React.useState(() => false)
  let ref = React.useRef(Js.Nullable.null)

  let handleScrollable = React.useCallback1(() =>
    switch ref.current->Js.Nullable.toOption {
    | Some(domElement) =>
      let hasNotReachedBottom =
        domElement->DomElement.scrollTop->Float.toInt + domElement->DomElement.clientHeight <
          domElement->DomElement.scrollHeight

      setScrollable(_ => hasNotReachedBottom)
    | None => ()
    }
  , [ref])

  React.useLayoutEffect0(() => {
    let bodyDomElement = ref->ReactDomElement.fromRef
    handleScrollable()
    bodyDomElement->Option.forEach(domNode =>
      domNode->DomElement.addEventListener("scroll", handleScrollable)
    )
    Some(
      () =>
        bodyDomElement->Option.forEach(domEmement =>
          domEmement->DomElement.removeEventListener("scroll", handleScrollable)
        ),
    )
  })

  ReactAria.Resize.useObserver({
    ref: ref->ReactDOM.Ref.domRef,
    onResize: handleScrollable,
  })

  <View style={rootStyleFromParams(~opened=nav.opened)}>
    <View style={styles["container"]}>
      <View style={styles["header"]}>
        <View style={styles["burger"]}>
          <Burger onPress={_ => dispatch(Toggled)} />
        </View>
        {if nav.opened {
          <View style={styles["userOrganizationNameAndBadge"]}>
            <Text style={styles["userOrganizationName"]}>
              {userOrganizationName->React.string}
            </Text>
            {switch badge {
            | Some(badge) => <Box spaceTop=#xsmall> badge </Box>
            | None => React.null
            }}
          </View>
        } else {
          React.null
        }}
      </View>
      <View ref style={contentStyleFromParams(nav.opened)}> {children} </View>
      <AnimatedRender displayed=scrollable animation=#fade duration=250>
        <div style=contentScrollShadeOverlay />
      </AnimatedRender>
      {if !userImpersonating {
        <Box spaceX=#small spaceY=#small>
          <Button label={t("Contact us")} icon=#help_bold onPress={_ => onToggleHelpCenter()} />
          <Navigation.Link to=Url(helpCenterUrl) openNewTab=true>
            <NavTextIcon icon=#book_bold> {t("Help center")->React.string} </NavTextIcon>
          </Navigation.Link>
          <Navigation.Link to=Url(legacyDashboardUrl) openNewTab=true>
            <NavTextIcon icon=#switch_bold> {legacyDashboardText->React.string} </NavTextIcon>
          </Navigation.Link>
        </Box>
      } else {
        React.null
      }}
      <Footer
        userName
        userProfilePictureUri=?{userImpersonating
          ? Some("/impersonating_user.png")
          : userProfilePictureUri}>
        <Link
          to={Navigation.Route(userSettingsRoute)}
          active={activeRoute === userSettingsRoute}
          label={t("Your account")}
        />
        <Button label={t("Sign out")} variation=#important onPress={_ => onRequestLogout()} />
      </Footer>
    </View>
  </View>
}

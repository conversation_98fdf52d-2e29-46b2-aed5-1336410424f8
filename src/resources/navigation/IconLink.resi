@genType let toRoute: string => Navigation.to

let makeProps: (
  ~to: Navigation.to,
  ~openNewTab: bool=?,
  ~name: Icon.t,
  ~marginSize: Sizes.t=?,
  ~color: Style.Color.t=?,
  ~hoveredColor: Style.Color.t=?,
  ~disabled: bool=?,
  ~key: string=?,
  ~ref: 'ref=?,
  unit,
) => {
  "to": Navigation.to,
  "openNewTab": option<bool>,
  "hoveredColor": option<Style.Color.t>,
  "color": option<Style.Color.t>,
  "disabled": option<bool>,
  "name": Icon.t,
  "marginSize": option<Sizes.t>,
}

@genType
let make: {
  "to": Navigation.to,
  "openNewTab": option<bool>,
  "hoveredColor": option<Style.Color.t>,
  "color": option<Style.Color.t>,
  "disabled": option<bool>,
  "name": Icon.t,
  "marginSize": option<Sizes.t>,
} => React.element

open Style

let styles = StyleSheet.create({
  "view": style(~borderRadius=50., ~justifyContent=#center, ~alignItems=#center, ()),
  "xxsmallView": style(~width=20.->dp, ~height=26.->dp, ()),
  "xsmallView": style(~width=26.->dp, ~height=26.->dp, ()),
  "smallView": style(~width=35.->dp, ~height=35.->dp, ()),
  "normalView": style(~width=40.->dp, ~height=40.->dp, ()),
})

let viewStyleFromParams = (~marginSize as size) =>
  [
    switch size {
    | Some(#xxsmall) => styles["xxsmallView"]
    | Some(#xsmall) => styles["xsmallView"]
    | Some(#small) => styles["smallView"]
    | Some(#normal) => styles["normalView"]
    | _ => style()
    },
  ]->arrayStyle

let toRoute = route => Navigation.Route(route)

@react.component
let make = React.forwardRef((
  ~to,
  ~openNewTab=false,
  ~name,
  ~marginSize=?,
  ~color=Colors.neutralColor50,
  ~hoveredColor=?,
  ~disabled=false,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  <Navigation.Link to openNewTab disabled>
    <View ref style={[styles["view"], viewStyleFromParams(~marginSize)]->arrayStyle}>
      <Icon
        name
        fill={switch (hovered, disabled, hoveredColor) {
        | (true, false, Some(active)) => active
        | _ => color
        }}
      />
    </View>
  </Navigation.Link>
})

let make = React.memo(make)

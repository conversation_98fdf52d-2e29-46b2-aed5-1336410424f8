open Intl
open Style

let styles = StyleSheet.create({
  "view": style(
    ~flexDirection=#row,
    ~alignItems=#center,
    ~marginLeft=(Spaces.xsmall *. -1.)->dp,
    (),
  ),
  "text": merge([
    FontFaces.archivoSemiBoldStyle,
    style(~fontSize=14., ~marginLeft=Spaces.xxsmall->dp, ()),
  ]),
  "defaultText": style(~color=Colors.neutralColor30, ()),
  "hoveredText": style(~color=Colors.neutralColor35, ()),
})

let textStyleFromParams = (~hovered) => hovered ? styles["hoveredText"] : styles["defaultText"]

@react.component
let make = () => {
  let (canGoBack, onGoBack) = Navigation.useGoBack()
  let (ref, hovered) = Hover.use()
  let hovered = hovered && canGoBack

  <Inline>
    <Touchable onPress={_ => onGoBack()} disabled={!canGoBack}>
      <View ref style={styles["view"]}>
        <Icon name=#back fill={hovered ? Colors.neutralColor50 : Colors.neutralColor25} />
        <Text style={[styles["text"], textStyleFromParams(~hovered)]->arrayStyle}>
          {t("Back")->React.string}
        </Text>
      </View>
    </Touchable>
  </Inline>
}

let make = React.memo(make)

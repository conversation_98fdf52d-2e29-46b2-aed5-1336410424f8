module Context: {
  type action = Toggled
  type state = {opened: bool}
  let initialState: state
  type dispatch = action => unit
  type context = (option<state>, dispatch)
  let context: React.Context.t<context>
  let use: unit => (state, dispatch)

  module Provider: {
    let make: React.component<{"children": React.element, "value": context}>
    let makeProps: (~value: 'a, ~children: 'b, unit) => {"children": 'b, "value": 'a}
  }

  let getReducer: 'a => (state, action => unit)
}

module Section: {
  @react.component
  let make: (
    ~children: React.element,
    ~title: string,
    ~active: bool,
    ~icon: Icon.t,
  ) => React.element
}

module Link: {
  @react.component
  let make: (
    ~to: Navigation.to,
    ~label: string,
    ~active: bool,
    ~icon: Icon.t=?,
    ~betaBadge: bool=?,
    ~restrictedMode: bool=?,
    ~variation: [#important | #primary | #secondary]=?,
  ) => React.element
}

let openedSize: float
let closedSize: float

@react.component
let make: (
  ~children: React.element,
  ~userOrganizationName: string,
  ~userName: string,
  ~userProfilePictureUri: string=?,
  ~badge: React.element=?,
  ~userImpersonating: bool,
  ~userSettingsRoute: string,
  ~helpCenterUrl: Url.t,
  ~legacyDashboardUrl: Url.t,
  ~legacyDashboardText: string,
  ~onToggleHelpCenter: unit => unit,
  ~onRequestLogout: unit => unit,
) => React.element

// TODO - to be revamped with React Aria (more explanation in Popover.res L46)
type variation = [#normal | #more_round | #more_square]

@react.component
let make = (
  ~children,
  ~variation: variation=#normal,
  ~buttonText=Intl.t("Actions"),
  ~buttonVariation=#neutral,
  ~buttonSize=#normal,
  ~buttonBordered=true,
  ~disabled=false,
  ~overlayPriority=true,
) => {
  let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()
  let (_, triggerHovered) = Hover.use(~ref=popoverTriggerRef, ())

  // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
  let onRequestClose = popover.onRequestClose
  let popover = {
    ...popover,
    onRequestClose: () => {
      if !triggerHovered {
        onRequestClose()
      }
    },
  }

  <>
    {switch variation {
    | #normal =>
      <Button
        size=buttonSize
        variation=buttonVariation
        icon={popover.opened ? #arrow_up_light : #arrow_down_light}
        disabled
        focused=popover.opened
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        onPress={_ => popover.onRequestToggle()}>
        {buttonText->React.string}
      </Button>
    | #more_round =>
      <RoundButton
        icon=#more_x_bold
        disabled
        focused=popover.opened
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        onPress={_ => popover.onRequestToggle()}
      />
    | #more_square =>
      <ShortIconButton
        name=#more_y_light
        bordered=buttonBordered
        focused=popover.opened
        ref=popoverTriggerRef
        ariaProps=popoverAriaProps.triggerProps
        action={Callback(popover.onRequestToggle)}
      />
    }}
    {if popover.opened {
      <Popover
        triggerRef=popoverTriggerRef
        state=popover
        variation={switch variation {
        | #normal | #more_square => #normal
        | #more_round => #arrowed
        }}
        overlayPriority
        dismissable=overlayPriority // NOTE - necessary for Safari
        modal=false
        placement=#"bottom end"
        offset={switch variation {
        | #more_round => 10.
        | #more_square if !buttonBordered => -4.
        | _ => 4.
        }}
        crossOffset={switch variation {
        | #more_square if !buttonBordered => -12.
        | _ => 0.
        }}>
        <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
          {switch variation {
          | #normal =>
            <Box spaceY=#xsmall>
              <Stack space=#none> children </Stack>
            </Box>
          | #more_round =>
            <Box spaceY=#xsmall>
              <Stack space=#xxsmall> children </Stack>
            </Box>
          | #more_square =>
            <Box spaceY=#xsmall>
              <Stack space=#xxsmall> children </Stack>
            </Box>
          }}
        </Popover.Dialog>
      </Popover>
    } else {
      React.null
    }}
  </>
}

let make = React.memo(make)
